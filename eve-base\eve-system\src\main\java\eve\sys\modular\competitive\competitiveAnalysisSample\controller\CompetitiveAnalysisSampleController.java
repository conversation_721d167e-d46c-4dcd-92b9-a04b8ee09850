package eve.sys.modular.competitive.competitiveAnalysisSample.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.competitive.competitiveAnalysisSample.entity.CompetitiveAnalysisSample;
import eve.sys.modular.competitive.competitiveAnalysisSample.service.ICompetitiveAnalysisSampleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 竞品分析样品追溯管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@RestController
@RequestMapping("/competitiveAnalysisSample")
public class CompetitiveAnalysisSampleController {

    @Resource
    ICompetitiveAnalysisSampleService competitiveAnalysisSampleService;

    @PostMapping("/pageList")
    @BusinessLog(title = "样品追溯管理-分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @ApiOperation("样品追溯管理-分页查询")
    public ResponseData pageList(@RequestBody CompetitiveAnalysisSample param) {
        return new SuccessResponseData(competitiveAnalysisSampleService.pageList(param));
    }

    @PostMapping("/chartData")
    @BusinessLog(title = "样品追溯管理-echarts图数据", opType = LogAnnotionOpTypeEnum.QUERY)
    @ApiOperation("样品追溯管理-echarts图数据")
    public ResponseData echartsData() {
        return new SuccessResponseData(competitiveAnalysisSampleService.echartsData());
    }


    @PostMapping("/add")
    @BusinessLog(title = "样品追溯管理-新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody CompetitiveAnalysisSample param) {
        return new SuccessResponseData(competitiveAnalysisSampleService.add(param));
    }

    @PostMapping("/remove")
    @BusinessLog(title = "样品追溯管理-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData remove(@RequestBody CompetitiveAnalysisSample param) {
        return new SuccessResponseData(competitiveAnalysisSampleService.remove(param));
    }

    @PostMapping("/update")
    @BusinessLog(title = "样品追溯管理-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody CompetitiveAnalysisSample param) {
        return new SuccessResponseData(competitiveAnalysisSampleService.update(param));
    }

}

