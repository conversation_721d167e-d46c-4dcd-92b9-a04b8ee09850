package eve.sys.dorisModular.testData.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.testData.entity.Cycle;
import eve.sys.dorisModular.testData.mapper.CycleMapper;
import eve.sys.dorisModular.testData.service.ICycleService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.mongoDbModular.shenghong.bean.HisCycData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class CycleServiceImpl extends ServiceImpl<CycleMapper, Cycle> implements ICycleService {

    @Resource
    private ITLimsTestdataPrimaryService testdataPrimaryService;

    @Override
    public PageResult<Cycle> cycListPage(HisCycData param) {
        TLimsTestdataPrimary testdataPrimary = testdataPrimaryService.getById(param.get_id());
        Map<String,Object> map = JSON.parseObject(testdataPrimary.getQueryparam(), Map.class);
        List<String> values = new ArrayList<>();

        for (String key : map.keySet()) {
            values.add(map.get(key).toString());
        }
        String flowId = String.join("-", values);
        LambdaQueryWrapper<Cycle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Cycle::getFlowId,flowId);

        queryWrapper.orderByAsc(Cycle::getCycleId);

        return new PageResult(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));

    }
}

