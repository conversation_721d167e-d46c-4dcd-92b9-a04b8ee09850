package eve.sys.limsModular.limsFile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.*;

/**
 * <p>
 * 文件版本
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Getter
@Setter
@TableName("T_LIMS_FILE_VERSION")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TLimsFileVersion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件ID
     */
    private Long documentid;

    /**
     * 是否启用
     */
    private String activatedflag;

    /**
     * 文件编码
     */
    private String documentcode;

    /**
     * 文件名称
     */
    private String documentname;

    /**
     * 文件描述
     */
    private String documentdesc;

    /**
     * 当前文件版本
     */
    private String documentversion;

    /**
     * 编制人
     */
    private String editname;

    /**
     * 发布时间
     */
    private Date edittime;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 添加人
     */
    private String createdbyname;

    /**
     * 添加时间
     */
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;

    /**
     * 启用人ID
     */
    private String activatedbyid;

    /**
     * 启用人名称
     */
    private String activatedbyname;

    /**
     * 启用时间
     */
    private Date activatedtime;

    /**
     * 储存的文件编号
     */
    private Long versionfileid;


}
