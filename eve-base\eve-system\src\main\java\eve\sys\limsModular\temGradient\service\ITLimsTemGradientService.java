package eve.sys.limsModular.temGradient.service;

import eve.sys.limsModular.temGradient.entity.TLimsTemGradient;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 温度梯度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
public interface ITLimsTemGradientService extends IService<TLimsTemGradient> {

    void sync();

    void syncSizeInfo();

    List<TLimsTemGradient> getByOrderIdAndOrdtaskId(Long folderId, Long ordtaskId);
}
