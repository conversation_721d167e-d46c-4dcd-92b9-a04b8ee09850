package eve.sys.limsModular.test.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import eve.sys.limsModular.test.entity.TLimsTestMethodCondition;
import eve.sys.limsModular.test.mapper.TLimsTestMethodConditionMapper;
import eve.sys.limsModular.test.service.ITLimsTestMethodConditionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 测试项目试验条件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
@DS("b3")
public class TLimsTestMethodConditionServiceImpl extends ServiceImpl<TLimsTestMethodConditionMapper, TLimsTestMethodCondition> implements ITLimsTestMethodConditionService {

}
