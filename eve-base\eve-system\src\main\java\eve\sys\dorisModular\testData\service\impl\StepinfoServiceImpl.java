package eve.sys.dorisModular.testData.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.testData.entity.Stepinfo;
import eve.sys.dorisModular.testData.mapper.StepinfoMapper;
import eve.sys.dorisModular.testData.service.IStepinfoService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.mongoDbModular.shenghong.bean.HisStepInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class StepinfoServiceImpl extends ServiceImpl<StepinfoMapper, Stepinfo> implements IStepinfoService {

    @Resource
    private ITLimsTestdataPrimaryService testdataPrimaryService;

    @Override
    public PageResult<Stepinfo> stepInfoPage(HisStepInfo param) {
        TLimsTestdataPrimary testdataPrimary = testdataPrimaryService.getById(param.get_id());
        Map<String,Object> map = JSON.parseObject(testdataPrimary.getQueryparam(), Map.class);
        List<String> values = new ArrayList<>();

        for (String key : map.keySet()) {
            values.add(map.get(key).toString());
        }
        String flowId = String.join("-", values);
        LambdaQueryWrapper<Stepinfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Stepinfo::getFlowId,flowId);

        if(null != param.getStepId()){
            queryWrapper.eq(Stepinfo::getStepId,param.getStepId());
        }
        if(null != param.getStepIdBefore()){
            queryWrapper.eq(Stepinfo::getStepId,param.getStepIdBefore());
        }
        if(null != param.getStepIdAfter()){
            queryWrapper.eq(Stepinfo::getStepId,param.getStepIdBefore());
        }

        if(StrUtil.isNotBlank(param.getStepName())){
            queryWrapper.eq(Stepinfo::getStepName,param.getStepName());
        }

        queryWrapper.orderByAsc(Stepinfo::getStepId);

        return new PageResult(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));

    }
}

