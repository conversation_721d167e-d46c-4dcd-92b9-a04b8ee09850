package eve.sys.limsModular.ordTask.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.core.pojo.response.ResponseData;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试验项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface ITLimsOrdtaskService extends IService<TLimsOrdtask> {

    PageResult<TLimsOrdtask> getLimsOrdtaskList(Map param) throws Exception;

    PageResult<TLimsOrdtask> getLimsOrdtaskListOfSafety(Map param) throws Exception;

    List<TLimsOrdtask> getLimsOrdtasksOfPreView(Map param) throws Exception;

    List<Map> getTestPerson(Map param);

    Map<String, Object> getOrdTasksByTesterCode(String testerCode);

    ResponseData updateOrdtaskStatus(Map param) throws Exception;

    ResponseData assignTaskOfAq(Map param) throws Exception;

    ResponseData InputDataSubmit(Map param);

    ResponseData reviewSubmit(List<TLimsOrdtask> ordTaskList);

    ResponseData reviewSendBack(Map param);

    List<TLimsOrdtask> getLimsOrdtaskListByParam(TLimsOrdtask param);

    TLimsFolder getFolderInfoByParam(TLimsOrdtask param);
}
