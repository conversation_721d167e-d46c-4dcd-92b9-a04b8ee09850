package eve.sys.modular.bomline.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import eve.core.exception.ServiceException;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.BomChangeParam;
import eve.sys.modular.bom.params.OaAddOrEditParam;
import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bom.service.impl.SysBomChangeUtil;
import eve.sys.modular.bom.service.impl.SysBomSapServiceUtil;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomend.service.ISysBomEndService;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.param.SysBomHistoryParam;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.mapper.SysBomLineMapper;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;
import eve.sys.modular.notice.enums.SysNoticeExceptionEnum;
import eve.sys.modular.werkline.entity.SysWerkLine;
import eve.sys.modular.werkline.service.ISysWerkLineService;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Service
public class SysBomLineServiceImpl extends ServiceImpl<SysBomLineMapper, SysBomLine> implements ISysBomLineService {

    @Resource
    private ISysBomEndService sysBomEndService;

    @Resource
    private ISysWerkLineService werkLineService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Resource
    private ISysBomService sysBomService;

    @Override
    public List<SysBomLine> getList(SysBomLineParam param) {

        LambdaQueryWrapper<SysBomLine> queryWrapper = new LambdaQueryWrapper<>();

        if (null != param.getBomId()) {
            queryWrapper.eq(SysBomLine::getBomId, param.getBomId());
        }

        return this.list(queryWrapper);
    }

    @Override
    public JSONObject getFilterList(SysBomLineParam param) {

        LambdaQueryWrapper<SysBomLine> queryWrapper = new LambdaQueryWrapper<>();

        if (null != param.getBomIssueId()) {
            queryWrapper.eq(SysBomLine::getBomIssueId, param.getBomIssueId());
        }
        if (null != param.getBomType()) {
            queryWrapper.eq(SysBomLine::getBomType, param.getBomType());
        }

        List<SysBomLine> bomLines = this.getList(param);

        List<Long> lineIds = null != bomLines && !bomLines.isEmpty() ? bomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()) : new ArrayList<>();

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getBomId());
        

        JSONObject rep = new JSONObject();
        rep.put("allLines", 1 == param.getBomType() ? new ArrayList<>() : this.list(queryWrapper));
        rep.put("originLines", lineIds);
        rep.put("lineflag", null == sysBomHistory ? 0 : 1);
        rep.put("bomtype", param.getBomType());
        return rep;
    }

    @Override
    public void deleteByLineIds(SysBomLineParam param,List<Long> delIds) {
        
        LambdaQueryWrapper<SysBomLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomLine::getBomId, param.getBomId());
        queryWrapper.in(SysBomLine::getLineId, delIds);
        this.baseMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByBomId(SysBomLineParam param) {
        
        LambdaQueryWrapper<SysBomLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomLine::getBomId, param.getBomId());
        this.baseMapper.delete(queryWrapper);
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void deleteWerks(SysBomLineParam param) {

        if (null == param.getLindIds() || param.getLindIds().isEmpty()) {
            throw new ServiceException(500, "请选择产线");
        }
        Map<String, List<SysWerkLine>> werkLines = sysBomService.getWerkLines();

        List<Long> lines = param.getLindIds();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "  ";
                        }
                        factory += sysWerkLines.get(j).getWerkNo() + "-"
                                + sysWerkLines.get(j).getLineName();
                        break;
                    }
                }
            }

        }


        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getBomId());
        SysBom sysBom = sysBomService.get(param.getBomId());
        List<SysBomLine> originLines = this.getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());
        List<Long> originLineIds = originLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        if (null == sysBomHistory) {
            deleteByLineIds(param,param.getLindIds());
            return;
        }


        if (originLineIds.size() == param.getLindIds().size()) {
            throw new ServiceException(500, "至少保留一个产线");
        }

        originLineIds.removeAll(param.getLindIds());

        List<SysWerkLine> _lines = werkLineService
                .getWerks(param.getLindIds());

        List<String> werkNos = originLineIds.isEmpty() ? new ArrayList<>() : werkLineService
                .getWerks(originLineIds)
                .stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        _lines.forEach(e -> {
            e.setFlag(werkNos.indexOf(e.getWerkNo()) > -1 ? 0 : 1);
        });

        String werkStrs = JSONObject.toJSONString(_lines);

        sysBom.setBomStatus(6L);
        sysBom.setBomIfAdd(2);
        sysBom.setBomAddWerks(werkStrs);
        sysBom.setBomRemark(factory);
        sysBom.setLineRemark(param.getLineRemark());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sysBom.setBomStartdate(sdf.format(new Date()));

        this.sysBomService.updateCodeVersionFileName(sysBom,param.getLindIds());

        sysBomService.updateById(sysBom);

        // 申请提交到OA
        //sysBomService.OaAdd(sysBom.getId(),param.getLindIds());
    }

    public Integer insertBatch(SysBomLineParam param,Integer multi) {

        SysBom bom = sysBomService.get(param.getBomId());

        if (!bom.getBomStatus().equals(0L) && !bom.getBomStatus().equals(2L) && !bom.getBomStatus().equals(4L) && !bom.getBomStatus().equals(7L)) {
            throw new ServiceException(500, "BOM的状态不允许关联产线");
        }

        if (bom.getBomStatus().equals(7L)) {

            List<TreeBom> treeBom = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
            
            if (StrUtil.isEmpty(treeBom.get(0).getVersion())) {
                throw new ServiceException(500, "请先导入数据");
            }
            JSONObject werkObj = JSONObject.parseObject(treeBom.get(0).getVersion());
            List<SysWerkLine> werkLines = werkLineService.getWerks(param.getLindIds());

            for (SysWerkLine item : werkLines) {
                if (!werkObj.keySet().contains(item.getWerkNo())) {
                    throw new ServiceException(500, "设置的工厂与导入的数据不匹配");
                }
            }
        }

        LambdaQueryWrapper<SysBomLine> exsitqueryWrapper = new LambdaQueryWrapper<>();
        exsitqueryWrapper.ne(SysBomLine::getBomId, param.getBomId());
        exsitqueryWrapper.eq(SysBomLine::getBomIssueId, param.getBomIssueId());
        exsitqueryWrapper.in(SysBomLine::getLineId, param.getLindIds());
        exsitqueryWrapper.eq(SysBomLine::getBomType, param.getBomType());
        List<SysBomLine> exists = this.list(exsitqueryWrapper);
        if (null != exists && exists.size() > 0 && 0 == param.getBomType() && multi == 0) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CANNOT_ADD);
        }
        

        List<SysBomLine> sysBomLines = new ArrayList<>();
        for (Long id : param.getLindIds()) {
            SysBomLine item = new SysBomLine();
            item.setId(IdWorker.getId());
            item.setBomId(param.getBomId());
            item.setBomIssueId(param.getBomIssueId());
            item.setLineId(id);
            item.setBomType(param.getBomType());
            sysBomLines.add(item);
        }
        return this.baseMapper.insertBatch(sysBomLines);
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void setWerks(SysBomLineParam param){
        if (null == param.getLindIds() || param.getLindIds().isEmpty()) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CHAN_EXIST);
        }

        this.remove(Wrappers.<SysBomLine>lambdaQuery().eq(SysBomLine::getBomId, param.getBomId()));

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getBomId());

        if (null == sysBomHistory) {
            insertBatch(param,1);
            return;
        }
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void addMultiWerks(SysBomLineParam param){

        if (null == param.getLindIds() || param.getLindIds().isEmpty()) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CHAN_EXIST);
        }

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getBomId());

        if (null == sysBomHistory) {
            insertBatch(param,1);
            return;
        }

        SysBom bom = sysBomService.get(param.getBomId());

        if (!bom.getBomStatus().equals(0L) && !bom.getBomStatus().equals(2L) && !bom.getBomStatus().equals(4L)) {
            throw new ServiceException(500, "BOM的状态不允许新增工厂");
        }

        /* LambdaQueryWrapper<SysBomLine> exsitqueryWrapper = new LambdaQueryWrapper<>();
        exsitqueryWrapper.ne(SysBomLine::getBomId, param.getBomId());
        exsitqueryWrapper.eq(SysBomLine::getBomIssueId, param.getBomIssueId());
        exsitqueryWrapper.in(SysBomLine::getLineId, param.getLindIds());
        exsitqueryWrapper.eq(SysBomLine::getBomType, param.getBomType());
        List<SysBomLine> exists = this.list(exsitqueryWrapper);
        if (null != exists && exists.size() > 0 && 0 == param.getBomType()) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CANNOT_ADD);
        } */

        

        Map<String, List<SysWerkLine>> werkLines = sysBomService.getWerkLines();

        List<Long> lines = param.getLindIds();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "  ";
                        }
                        factory += sysWerkLines.get(j).getWerkNo() + "-"
                                + sysWerkLines.get(j).getLineName();
                        break;
                    }
                }
            }

        }

        List<SysWerkLine> _lines = werkLineService
                .getWerks(param.getLindIds());

        List<SysBomLine> originLines = this.getList(SysBomLineParam.builder().bomId(bom.getId()).build());

        List<Long> originIds = originLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());
        
        List<String> werkNos = null == originLines || originLines.isEmpty() ? new ArrayList<>() : werkLineService
                .getWerks(originLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()))
                .stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        _lines.forEach(e -> {
            e.setFlag(werkNos.indexOf(e.getWerkNo()) > -1 ? 0 : 1);
        });

        String werkStrs = JSONObject.toJSONString(_lines);

        bom.setBomStatus(5L);
        bom.setBomIfAdd(1);
        bom.setBomAddWerks(werkStrs);
        bom.setBomRemark(factory);
        bom.setLineRemark(param.getLineRemark());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        bom.setBomStartdate(sdf.format(new Date()));

        this.sysBomService.updateCodeVersionFileName(bom,param.getLindIds());

        sysBomService.updateById(bom);

        List<SysBomLine> sysBomLines = new ArrayList<>();

        
        for (Long id : param.getLindIds()) {
            if (originIds.indexOf(id) != -1) {
                continue;
            }
            SysBomLine item = new SysBomLine();
            item.setId(IdWorker.getId());
            item.setBomId(param.getBomId());
            item.setBomIssueId(param.getBomIssueId());
            item.setLineId(id);
            item.setBomType(param.getBomType());
            sysBomLines.add(item);
        }
        this.baseMapper.insertBatch(sysBomLines);
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void addWerks(SysBomLineParam param) {

        if (null == param.getLindIds() || param.getLindIds().isEmpty()) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CHAN_EXIST);
        }

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getBomId());

        if (null == sysBomHistory) {
            insertBatch(param,0);
            return;
        }

        SysBom bom = sysBomService.get(param.getBomId());

        if (!bom.getBomStatus().equals(0L) && !bom.getBomStatus().equals(2L) && !bom.getBomStatus().equals(4L)) {
            throw new ServiceException(500, "BOM的状态不允许新增工厂");
        }

        LambdaQueryWrapper<SysBomLine> exsitqueryWrapper = new LambdaQueryWrapper<>();
        exsitqueryWrapper.ne(SysBomLine::getBomId, param.getBomId());
        exsitqueryWrapper.eq(SysBomLine::getBomIssueId, param.getBomIssueId());
        exsitqueryWrapper.in(SysBomLine::getLineId, param.getLindIds());
        exsitqueryWrapper.eq(SysBomLine::getBomType, param.getBomType());
        List<SysBomLine> exists = this.list(exsitqueryWrapper);
        if (null != exists && exists.size() > 0 && 0 == param.getBomType()) {
            throw new ServiceException(SysNoticeExceptionEnum.NOTICE_CANNOT_ADD);
        }

        

        Map<String, List<SysWerkLine>> werkLines = sysBomService.getWerkLines();

        List<Long> lines = param.getLindIds();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "  ";
                        }
                        factory += sysWerkLines.get(j).getWerkNo() + "-"
                                + sysWerkLines.get(j).getLineName();
                        break;
                    }
                }
            }

        }

        List<SysWerkLine> _lines = werkLineService
                .getWerks(param.getLindIds());

        List<SysBomLine> originLines = this.getList(SysBomLineParam.builder().bomId(bom.getId()).build());

        List<String> werkNos = null == originLines || originLines.isEmpty() ? new ArrayList<>() : werkLineService
                .getWerks(originLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()))
                .stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        _lines.forEach(e -> {
            e.setFlag(werkNos.indexOf(e.getWerkNo()) > -1 ? 0 : 1);
        });

        String werkStrs = JSONObject.toJSONString(_lines);

        bom.setBomStatus(5L);
        bom.setBomIfAdd(1);
        bom.setBomAddWerks(werkStrs);
        bom.setBomRemark(factory);
        bom.setLineRemark(param.getLineRemark());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        bom.setBomStartdate(sdf.format(new Date()));

        this.sysBomService.updateCodeVersionFileName(bom,param.getLindIds());

        sysBomService.updateById(bom);

        List<SysBomLine> sysBomLines = new ArrayList<>();
        for (Long id : param.getLindIds()) {
            SysBomLine item = new SysBomLine();
            item.setId(IdWorker.getId());
            item.setBomId(param.getBomId());
            item.setBomIssueId(param.getBomIssueId());
            item.setLineId(id);
            item.setBomType(param.getBomType());
            sysBomLines.add(item);
        }
        this.baseMapper.insertBatch(sysBomLines);
        // 申请提交到OA
        //sysBomService.OaAdd(bom.getId(),null);
    }

    @Override
    public void insertBatch(List<SysBomLine> bomLines) {
        if (ObjectUtil.isEmpty(bomLines)) {
            return;
        }
        this.baseMapper.insertBatch(bomLines);
    }
    
}
