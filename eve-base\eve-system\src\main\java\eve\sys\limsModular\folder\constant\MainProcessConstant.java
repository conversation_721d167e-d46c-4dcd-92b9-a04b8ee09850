package eve.sys.limsModular.folder.constant;

import java.util.Arrays;
import java.util.List;

public class MainProcessConstant {

    /****************************************************
     * 各个实验室编码
     ****************************************************/
    public static final String LABORATORYID_DLDC = "HZ_YJ_DL_CS";// 动力电池
    public static final String LABORATORYID_DLSYSJM = "JM_YJ_DL_CS";// 第六实验室(JM)
    public static final String LABORATORYID_DCXT = "HZ_YJ_DC_CS_CS";// 电池系统
    public static final String LABORATORYID_CL = "HZ_ZL_JC_CL";// 质量中心材料
    public static final String LABORATORYID_DC = "HZ_ZL_JC_DC";// 质量中心电池(或者电芯)
    public static final String LABORATORYID_JMSYS = "HZ_YJ_DL_JM";// 精密实验室

    /****************************************************
     * 管理模块
     ****************************************************/
    public static final String FOLDER_MANAGE = "委托管理";
    public static final String TASK_MANAGE = "任务管理";
    public static final String OUTSOURCE_MANAGE = "委外管理";
    public static final String ORDER_MANAGE = "样品管理";
    public static final String REPORT_MANAGE = "报告管理";
    public static final String CHANGE_MANAGE = "变更管理";

    /****************************************************
     * 页面节点名称
     ****************************************************/
    public static final String INTERFACE_DRAFT = "委托申请";
    public static final String INTERFACE_PRODUCT = "产品经理会审";
    public static final String INTERFACE_DEPART = "部门经理会审";
    public static final String INTERFACE_APPLY = "申请方部门总监审批";
    public static final String INTERFACE_CLUB = "所长审批";
    public static final String INTERFACE_LAB = "实验室经理审批";
    public static final String INTERFACE_TECH = "测试策划";//菜单名称修改(改了俩次):技术负责人审批->测试地点审核->测试策划
    public static final String INTERFACE_OUTSOURCE = "任务委外";
    public static final String INTERFACE_OUTTESTASSIGN = "委外测试任务分配";
    public static final String INTERFACE_BACKFILL = "数据回填";
    public static final String INTERFACE_RECEIVE = "样品接收";
    public static final String INTERFACE_SCHEDULE = "排程异常处理";
    public static final String INTERFACE_CFMSCHEDULE = "排程确认";
    public static final String INTERFACE_TESTASSIGN = "测试任务分配";
    public static final String INTERFACE_ASSIGN = "任务分配";
    public static final String INTERFACE_RESULT = "结果录入";
    public static final String INTERFACE_TESTPRO_REPORT = "试验过程报告";
    public static final String INTERFACE_EXAMINE = "结果审核";
    public static final String INTERFACE_REVIEW = "结果复核";
    public static final String INTERFACE_REPORT_DRAFT = "报告编制";
    public static final String INTERFACE_REPORT_APPROVE = "报告审核";
    public static final String INTERFACE_REPORT_AUDIT = "报告批准";
    public static final String INTERFACE_REPORT_RECEIVE = "报告接收";
    public static final String INTERFACE_REPORT_DONE = "报告归档";
    public static final String ALL_DONE = "完成";

    public static final String INTERFACE_FOLDER_TRANSFER = "单据转办";
    public static final String INTERFACE_REPORT_TRANSFER = "报告转办";

    //动力电池委外流程节点
    public static final String INTERFACE_DEMANDRECEIVE = "委外需求接收";
    public static final String INTERFACE_UPLOADNUMBER = "上传物流单号";
    public static final String INTERFACE_TESTREPORT = "测试报告上传";
    public static final String INTERFACE_HERBACEOUSAPPROVE = "报告草本审核";
    public static final String INTERFACE_HERBACEOUSAUDIT = "报告草本确认";
    public static final String INTERFACE_ORIGINALUPLOAD = "报告正本上传";
    public static final String INTERFACE_ORIGINALAPPROVE = "报告正本审核";
    public static final String INTERFACE_ORIGINALAUDIT = "报告正本确认";

    //用于OA移动审批相关
    public static final String INTERFACE_OASTATUS = "OA系统";//OA系统

    /****************************************************
     * 检测业务流程-folder状态
     ****************************************************/
    public static final String FOLDER_STATUS_DRAFT_WITHDRAWED = "Draft_withdrawed";//手动撤回
    public static final String FOLDER_STATUS_DRAFT_PRODUCT = "Draft_product";//产品经理退回
    public static final String FOLDER_STATUS_DRAFT_DEPART = "Draft_depart";//部门经理退回
    public static final String FOLDER_STATUS_DRAFT_APPLY = "Draft_apply";//申请总监退回
    public static final String FOLDER_STATUS_DRAFT_CLUB = "Draft_club";//所长退回
    public static final String FOLDER_STATUS_DRAFT_LAB = "Draft_lab";//实验室总监退回
    public static final String FOLDER_STATUS_DRAFT_TECH = "Draft_tech";//技术负责退回
    public static final String FOLDER_STATUS_DRAFT_RECEIVE = "Draft_receive";//样品接收退回

    public static final String FOLDER_STATUS_DRAFT = "Draft";//初始
    public static final String FOLDER_STATUS_PRODUCT = "Product";//产品经理
    public static final String FOLDER_STATUS_DEPART = "Depart";//部门经理
    public static final String FOLDER_STATUS_APPLY = "Apply";//申请总监
    public static final String FOLDER_STATUS_CLUB = "Club";//所长
    public static final String FOLDER_STATUS_LAB = "Lab";//实验室总监
    public static final String FOLDER_STATUS_TECH = "Tech";//技术负责

    public static final String FOLDER_STATUS_RECEIVE = "Receive";//样品接收

    public static final String FOLDER_STATUS_OUTSOURCE = "Outsource";//委外
    public static final String FOLDER_STATUS_BACKFILL = "Backfill";//数据回填
    public static final String FOLDER_STATUS_DEMANDRECEIVE = "Demandreceive";//委外需求接收
    public static final String FOLDER_STATUS_UPLOADNUMBER = "Uploadnumber";//上传物流单号

    public static final String FOLDER_STATUS_TESTING = "Testing";//检测中
    public static final String FOLDER_STATUS_REPORTING = "Reporting";//报告编辑
    public static final String FOLDER_STATUS_DONE = "Done";//完成

    public static final String FOLDER_STATUS_CANCEL = "Cancel";//取消
    //委托申请状态,用于OA移动审批相关
    public static final String FOLDER_STATUS_OASTATUS = "Oastatus";//OA流转中
    public static final String FOLDER_STATUS_DRAFT_OASTATUS = "Draft_oastatus";//OA退回
    public static final List<String> FOLDER_STATUS_INITDRAFT_LIST = Arrays.asList(FOLDER_STATUS_DRAFT, FOLDER_STATUS_DRAFT_OASTATUS);

    public static final List<String> FOLDER_DRAFT_STATUS_LIST = Arrays.asList(FOLDER_STATUS_DRAFT, FOLDER_STATUS_DRAFT_WITHDRAWED, FOLDER_STATUS_DRAFT_PRODUCT,
            FOLDER_STATUS_DRAFT_DEPART, FOLDER_STATUS_DRAFT_APPLY, FOLDER_STATUS_DRAFT_CLUB, FOLDER_STATUS_DRAFT_LAB, FOLDER_STATUS_DRAFT_TECH,
            FOLDER_STATUS_DRAFT_RECEIVE, FOLDER_STATUS_DRAFT_OASTATUS);

    /****************************************************
     * 检测业务流程-ordtask状态
     ****************************************************/
    public static final String ORDTASK_STATUS_RESULT_RETURN = "Result_return";// 退回

    public static final String ORDTASK_STATUS_DRAFT = "Draft";//新建状态
    public static final String ORDTASK_STATUS_PRESCHEDULE = "Preschedule";//待排程
    public static final String ORDTASK_STATUS_CFMSCHEDULE = "Cfmschedule";//排程确认
    public static final String ORDTASK_STATUS_TESTASSIGN = "Testassign";//测试任务分配
    public static final String ORDTASK_STATUS_ASSIGN = "Assign";//任务分配
    public static final String ORDTASK_STATUS_RESULT = "Result";//结果录入
    public static final String ORDTASK_STATUS_EXAMINE = "Examine";//结果审核
    public static final String ORDTASK_STATUS_REVIEW = "Review";//结果复核
    public static final String ORDTASK_STATUS_REPORT = "Report";//报告编制
    public static final String ORDTASK_STATUS_INREVIEW = "Inreview";//报告审核中
    public static final String ORDTASK_STATUS_DONE = "Done";//完成

    public static final String ORDTASK_STATUS_OUTSOURCE = "Outsource";//任务委外
    public static final String ORDTASK_STATUS_OUTTESTASSIGN = "Outtestassign";//委外测试任务分配
    public static final String ORDTASK_STATUS_BACKFILL = "Backfill";//数据回填

    public static final String ORDTASK_STATUS_TESTREPORT = "Testreport";//测试报告上传
    public static final String ORDTASK_STATUS_HERBACEOUSAPPROVE = "Herbaceousapprove";//草本审核
    public static final String ORDTASK_STATUS_HERBACEOUSAUDIT = "Herbaceousaudit";//草本审批
    public static final String ORDTASK_STATUS_ORIGINALUPLOAD = "Originalupload";//正本上传
    public static final String ORDTASK_STATUS_ORIGINALAPPROVE = "Originalapprove";//正本审核
    public static final String ORDTASK_STATUS_ORIGINALAUDIT = "Originalaudit";//正本审批

    public static final String ORDTASK_STATUS_HERBACEOUSAPPROVEREJECT = "HerbaceousapproveReject";//草本审核退回
    public static final String ORDTASK_STATUS_HERBACEOUSAUDITREJECT = "HerbaceousauditReject";//草本审批退回
    public static final String ORDTASK_STATUS_ORIGINALAPPROVEREJECT = "OriginalapproveReject";//正本审核退回
    public static final String ORDTASK_STATUS_ORIGINALAUDITREJECT = "OriginalauditReject";//正本审批退回
    public static final List<String> ORDTASK_TESTREPORT_LIST = Arrays.asList("Testreport", "HerbaceousapproveReject", "HerbaceousauditReject");
    public static final List<String> ORDTASK_ORIGINALUPLOAD_LIST = Arrays.asList("Originalupload", "OriginalapproveReject", "OriginalauditReject");
    public static final List<String> ORDTASK_DCXT_RECEIVE_LIST = Arrays.asList(ORDTASK_STATUS_BACKFILL, ORDTASK_STATUS_RESULT,
            ORDTASK_STATUS_EXAMINE, ORDTASK_STATUS_REVIEW, ORDTASK_STATUS_REPORT, ORDTASK_STATUS_DONE, "Cancel");

    public static final String ORDTASK_STATUS_CANCEL = "Cancel";//取消

    public static final List<String> ORDTASK_DONE_LIST = Arrays.asList("Done", "Cancel");
    public static final List<String> VALIDATOR_ORDTASK_STATUS_LIST = Arrays.asList("Report", "Inreview", "Done", "Cancel");
    /****************************************************
     * 检测业务流程-order状态
     ****************************************************/
    public static final String ORDER_STATUS_DRAFT = "Draft";//待入库
    public static final String ORDER_STATUS_INSTORE = "Instore";//在库
    public static final String ORDER_STATUS_OUTSTORE = "Outstore";//出库
    public static final String ORDER_STATUS_SCRAP = "Scrap";//报废
    public static final String ORDER_STATUS_DONE = "Done";//完成

    public static final String ORDERSTATUS_REST = "Rest";//未流转
    public static final String ORDERSTATUS_FLOW = "Flow";//流转中

    /****************************************************
     * 检测业务流程-样品流转类型
     ****************************************************/
    public static final String ORDER_FLOW_OPERA_COLLECT = "Collect";//收样
    public static final String ORDER_FLOW_OPERA_OUTSTORE = "Outstore";//出库
    public static final String ORDER_FLOW_OPERA_OUTGOING = "Outgoing";//样品外发
    public static final String ORDER_FLOW_OPERA_FLOWTURN = "Flowturn";//流转
    public static final String ORDER_FLOW_OPERA_RETURNSTORE = "Returnstore";//还库
    public static final String ORDER_FLOW_OPERA_RECEIVECONFIRM = "Receiveconfirm";//接收确认
    public static final String ORDER_FLOW_OPERA_SCRAP = "Scrap";//报废
    public static final String ORDER_FLOW_OPERA_RETURNCLIENT = "Returnclient";//归还委托人

    /****************************************************
     * 检测业务流程-report状态
     ****************************************************/
    public static final String REPORT_STATUS_DRAFT_APPROVE = "Draft_approve";//报告审核退回
    public static final String REPORT_STATUS_DRAFT_AUDIT = "Draft_audit";//报告审批退回

    public static final String REPORT_STATUS_DRAFT = "Draft";//新建状态
    public static final String REPORT_STATUS_APPROVE = "Approve";//报告审核
    public static final String REPORT_STATUS_AUDIT = "Audit";//报告审批
    public static final String REPORT_STATUS_RECEIVE = "Receive";//报告接收
    public static final String REPORT_STATUS_DONE = "Done";//报告归档

    public static final List<String> REPORT_EDIT_STATUS_LIST = Arrays.asList("Draft", "Draft_approve", "Draft_audit");
    public static final List<String> REPORT_APPROVE_STATUS_LIST = Arrays.asList("Approve", "Audit");
    public static final List<String> REPORT_UPDATE_ORDTASK_STATUS_LIST = Arrays.asList("Approve", "Draft_approve", "Draft_audit", "Receive");
    public static final List<String> REPORT_DONE_STATUS_LIST = Arrays.asList("Receive", "Done");
    /****************************************************
     * 菜单ID
     ****************************************************/
    public static final String INTERFACE_DRAFT_MENUID = "************";//委托申请
    public static final String INTERFACE_PRODUCT_MENUID = "************";//产品经理会审
    public static final String INTERFACE_DEPART_MENUID = "************";//部门经理会审
    public static final String INTERFACE_APPLY_MENUID = "************";//申请方部门总监审批
    public static final String INTERFACE_CLUB_MENUID = "20211101002";//所长审批
    public static final String INTERFACE_LAB_MENUID = "************";//实验室经理审批
    public static final String INTERFACE_TECH_MENUID = "************";//技术负责人审批

    public static final String INTERFACE_ORDTASK_OUTSOURCE_MENUID = "20211207001";//任务委外(测试项目)
    public static final String INTERFACE_ORDTASK_OUTTESTASSIGN_MENUID = "20230411010";//委外测试任务分配(测试项目)
    public static final String INTERFACE_ORDTASK_BACKFILL_MENUID = "20211207002";//数据回填(测试项目)
    public static final String INTERFACE_OUTSOURCE_MENUID = "************";//任务委外
    public static final String INTERFACE_BACKFILL_MENUID = "************";//数据回填
    public static final String INTERFACE_RECEIVE_MENUID = "************";//样品接收
    public static final String INTERFACE_SCHEDULE_MENUID = "************";//排程异常处理
    public static final String INTERFACE_CFMSCHEDULE_MENUID = "20211120001";//排程确认

    public static final String INTERFACE_TESTASSIGN_MENUID = "20230411038";//测试任务分配
    public static final String INTERFACE_ASSIGN_MENUID = "************";//任务分配
    public static final String INTERFACE_RESULT_MENUID = "************";//结果录入
    public static final String INTERFACE_EXAMINE_MENUID = "20230411052";//结果审核
    public static final String INTERFACE_REVIEW_MENUID = "************";//结果复核

    public static final String INTERFACE_TESTPRO_REPORT_DRAFT_MENUID = "20220209001";//试验过程报告

    public static final String INTERFACE_REPORT_DRAFT_MENUID = "************";//报告编制
    public static final String INTERFACE_REPORT_APPROVE_MENUID = "************";//报告审核
    public static final String INTERFACE_REPORT_AUDIT_MENUID = "************";//报告批准
    public static final String INTERFACE_REPORT_RECEIVE_MENUID = "************";//报告接收
    public static final String INTERFACE_REPORT_DONE_MENUID = "************";//报告归档

    public static final String INTERFACE_FOLDER_TRANSFER_MENUID = "20211105001";//单据转办
    public static final String INTERFACE_REPORT_TRANSFER_MENUID = "20211105002";//报告转办

    //动力电池委外流程节点
    public static final String INTERFACE_DEMANDRECEIVE_MENUID = "20220414010";//委外需求接收
    public static final String INTERFACE_UPLOADNUMBER_MENUID = "20220414011";//上传物流单号
    public static final String INTERFACE_TESTREPORT_MENUID = "20220414012";//测试报告上传
    public static final String INTERFACE_HERBACEOUSAPPROVE_MENUID = "20220414013";//报告草本审核
    public static final String INTERFACE_HERBACEOUSAUDIT_MENUID = "20220414014";//报告草本审批
    public static final String INTERFACE_ORIGINALUPLOAD_MENUID = "20220414015";//报告正本上传
    public static final String INTERFACE_ORIGINALAPPROVE_MENUID = "20220414016";//报告正本审核
    public static final String INTERFACE_ORIGINALAUDIT_MENUID = "20220414017";//报告正本审批

    /****************************************************
     * 测试项目委外标识
     ****************************************************/
    public static final String ORDTASK_OUTSOURCEFLAG_YES = "1";//委外
    public static final String ORDTASK_OUTSOURCEFLAG_NO = "0";//不委外
    public static final String ORDTASK_OUTSOURCEFLAG_INSIDE = "inside";//内部委外
    public static final String ORDTASK_OUTSOURCEFLAG_OUTSIDE = "outside";//外部委外

    public static final List<String> ORDTASK_OUTSOURCEFLAG_LIST = Arrays.asList(ORDTASK_OUTSOURCEFLAG_INSIDE, ORDTASK_OUTSOURCEFLAG_OUTSIDE);
    /****************************************************
     * 测试条件详情类型,第一个是全部详情类型,第二个是客户要求相关
     ****************************************************/
    public static final List<String> ORDTASK_CONDITION_DATATYPE = Arrays.asList("temSoc", "temMagnification", "temSocPower", "standardRequirement", "rohsRequirement", "limitRequirement", "temSocDischarge", "plateThickness", "safetyTest", "internalBlock","inspectionMsg");
    public static final List<String> ORDTASK_CONDITION_REQUIREMENT_DATATYPE = Arrays.asList("standardRequirement", "rohsRequirement", "limitRequirement");

    /****************************************************
     * 测试项目方法业务类型,用于分析项相关业务处理,  第一个是客户要求相关, 第二个是非Rohs判定相关
     ****************************************************/
    public static final List<String> ANALYTE_REQUIREMENT_BUSINESSTYPE = Arrays.asList("rohsRequirement", "noRohsRequirement", "sizeAnalyse", "powerOnTest");
    public static final List<String> ANALYTE_NOROHS_BUSINESSTYPE = Arrays.asList("noRohsRequirement", "sizeAnalyse");

    public static final String SIGN_GREATER_THAN = ">"; //大于号
    public static final String SIGN_LESS_THAN = "<"; //小于号
    public static final String SIGN_GREATER_EQUAL_THAN_ONE = ">="; //大于等于号
    public static final String SIGN_GREATER_EQUAL_THAN_TWO = "≥"; //大于等于号
    public static final String SIGN_LESS_EQUAL_THAN_ONE = "<="; //小于等于号
    public static final String SIGN_LESS_EQUAL_THAN_TWO = "≤"; //小于等于号
    public static final String SIGN_ADD_AND_SUBTRACT = "±"; //加减号
    public static final String SIGN_HORIZONTAL_BAR = "-"; //横杠
    public static final List<String> SIGN_GREATER_OR_EQUAL_LIST = Arrays.asList(">", ">=", "≥");
    public static final List<String> SIGN_LESS_OR_EQUAL_LIST = Arrays.asList("<", "<=", "≤");

    public static final String DCXT_ZRTYPE = "zrType"; // 主任
    public static final String DCXT_ZZTYPE = "zzType"; // 组长
    public static final String DCXT_FZTYPE = "fzType"; // 测试负责人
    public static final String DCXT_JCTYPE = "jcType"; // 检测人
    public static final String DCXT_BGTYPE = "bgType"; // 报告编制人
}
