package eve.sys.modular.competitive.competitiveAnalysisSample.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import eve.core.pojo.base.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 竞品分析样品追溯管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Getter
@Setter
@TableName("COMPETITIVE_ANALYSIS_SAMPLE")
public class CompetitiveAnalysisSample extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 竞品管理单号
     */
    private String code;

    /**
     * 样品状态 testing 在测  saving 在库 disassemble 拆解
     */
    private String cellStatus;


    /**
     * 厂商
     */
    private String factory;

    /**
     * 型号
     */
    private String model;

    /**
     * 类型
     */
    private String competitiveType;

    /**
     * 尺寸
     */
    private String competitiveSize;

    /**
     * 化学体系
     */
    private String chemicalSystem;

    /**
     * 容量
     */
    private BigDecimal capacity;

    /**
     * 应用领域
     */
    private String applicationArea;

    /**
     * 样品编号
     */
    private String cellCode;

    /**
     * 样品位置
     */
    private String cellAddress;


}
