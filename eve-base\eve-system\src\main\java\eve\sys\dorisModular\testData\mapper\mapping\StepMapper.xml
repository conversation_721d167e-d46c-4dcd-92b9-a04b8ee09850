<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.dorisModular.testData.mapper.StepMapper">

    <insert id="batchInsertSteps" parameterType="java.util.List">
        INSERT INTO sync_data.step (
        step_num, step_id, cycle_id, flow_id, step_name,
        absolute_time, step_time, begin_voltage, end_voltage,
        begin_current, end_current, capacity, energy,
        accumulate_charge_capacity, accumulate_discharge_capacity,
        accumulate_charge_energy, accumulate_discharge_energy,
        start_temp1, start_temp2, start_temp3, start_temp4,
        end_temp1, end_temp2, end_temp3, end_temp4,
        cccapacity_rate, start_pressure, end_pressure,
        unit_num, channel_id, barcode, sync_date,create_time,equ_name,query_param
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.stepNum},
            #{item.stepId},
            #{item.cycleId},
            #{item.flowId},
            #{item.stepName},
            #{item.absoluteTime},
            #{item.stepTime},
            #{item.beginVoltage},
            #{item.endVoltage},
            #{item.beginCurrent},
            #{item.endCurrent},
            #{item.capacity},
            #{item.energy},
            #{item.accumulateChargeCapacity},
            #{item.accumulateDischargeCapacity},
            #{item.accumulateChargeEnergy},
            #{item.accumulateDischargeEnergy},
            #{item.startTemp1},
            #{item.startTemp2},
            #{item.startTemp3},
            #{item.startTemp4},
            #{item.endTemp1},
            #{item.endTemp2},
            #{item.endTemp3},
            #{item.endTemp4},
            #{item.cccapacityRate},
            #{item.startPressure},
            #{item.endPressure},
            #{item.unitNum},
            #{item.channelId},
            #{item.barcode},
            #{item.syncDate},
            #{item.createTime},
            #{item.equName},
            #{item.queryParam}
            )
        </foreach>
    </insert>

    <select id="selectStepNum" resultType="int">
        select step_num from sync_data.step
        ${ew.customSqlSegment}
        ORDER BY step_num ASC
            limit #{num},1
    </select>

    <select id="selectStepPage" resultType="eve.sys.dorisModular.testData.entity.Step">
        SELECT s.*
        FROM sync_data.step s,
             ( SELECT step_num, flow_id FROM sync_data.step ${ew.customSqlSegment} ORDER BY step_num ASC LIMIT #{start}, #{pageSize} ) temp
        WHERE s.step_num = temp.step_num and s.flow_id = temp.flow_id
        ORDER BY s.step_num;
    </select>


</mapper>
