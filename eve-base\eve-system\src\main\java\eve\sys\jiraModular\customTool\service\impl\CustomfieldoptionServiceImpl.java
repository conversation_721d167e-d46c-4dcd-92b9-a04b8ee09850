package eve.sys.jiraModular.customTool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.enums.CommonStatusEnum;
import eve.sys.jiraModular.customTool.entity.Customfieldoption;
import eve.sys.jiraModular.customTool.mapper.CustomfieldoptionMapper;
import eve.sys.jiraModular.customTool.param.OptionQueryParam;
import eve.sys.jiraModular.customTool.service.ICustomfieldoptionService;
import eve.sys.modular.dict.entity.SysDictData;
import eve.sys.modular.dict.entity.SysDictType;
import eve.sys.modular.dict.service.SysDictDataService;
import eve.sys.modular.dict.service.SysDictTypeService;
import eve.sys.modular.topic.param.Cate;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
public class CustomfieldoptionServiceImpl extends ServiceImpl<CustomfieldoptionMapper, Customfieldoption> implements ICustomfieldoptionService {

    @Resource
    private SysDictDataService dictDataService;
    @Resource
    private SysDictTypeService dictTypeService;

    @Override
    public List<Customfieldoption> list(OptionQueryParam qualityProblem) {
        String fieldName = qualityProblem.getFieldName();
        //到字典获取字段id
        LambdaQueryWrapper<SysDictType> dictTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictTypeLambdaQueryWrapper.ne(SysDictType::getStatus, CommonStatusEnum.DELETED.getCode());
        dictTypeLambdaQueryWrapper.eq(SysDictType::getCode, "JIRA_CustomFieldId");
        SysDictType sysDictType = dictTypeService.getOne(dictTypeLambdaQueryWrapper);
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, sysDictType.getId());
        queryWrapper.eq(SysDictData::getCode, fieldName);
        List<SysDictData> sysDictDataList = dictDataService.list(queryWrapper);
        List<Customfieldoption> optionList = new ArrayList<>();
        Optional<SysDictData> first = sysDictDataList.stream().filter(e -> e.getCode().equals(fieldName)).findFirst();
        if (first.isPresent()) {
            SysDictData sysDictData = first.get();
            LambdaQueryWrapper<Customfieldoption> optionQueryWrapper = new LambdaQueryWrapper<>();
            optionQueryWrapper.eq(Customfieldoption::getCustomfield, Long.parseLong(sysDictData.getValue()));
            optionQueryWrapper.eq(Customfieldoption::getDisabled, "N");
            optionQueryWrapper.orderByAsc(Customfieldoption::getSequence);
            optionList = this.list(optionQueryWrapper);
        }
        return optionList;
    }

    @Override
    public Map<String, List<Customfieldoption>> listByNameList(OptionQueryParam optionQueryParam) {
        List<String> fieldNameList = optionQueryParam.getFieldNameList();
        Map<String, List<Customfieldoption>> customfieldoptionListMap = new HashMap<>();
        for (String fieldName : fieldNameList) {
            optionQueryParam.setFieldName(fieldName);
            customfieldoptionListMap.put(fieldName, this.list(optionQueryParam));
        }
        return customfieldoptionListMap;
    }

    @Override
    public List<Customfieldoption> listAsCascading(OptionQueryParam optionQueryParam) {
        List<Customfieldoption> allOptionList = this.list(optionQueryParam);
        List<Customfieldoption> customfieldoptionList = allOptionList.stream()
                .filter(e -> e.getParentoptionid() == null)
                .sorted(Comparator.comparing(Customfieldoption::getSequence))
                .collect(Collectors.toList());
        customfieldoptionList.forEach(customfieldoption->{
            Long id = customfieldoption.getId();
            List<Customfieldoption> subOptionList = allOptionList.stream().filter(e -> id.equals(e.getParentoptionid()))
                    .sorted(Comparator.comparing(Customfieldoption::getSequence))
                    .collect(Collectors.toList());
            customfieldoption.setSubOptionList(subOptionList);
        });
        return customfieldoptionList;
    }

    @Override
    public Map<String, List<Customfieldoption>> listAllOption() {
        //到字典获取字段id
        LambdaQueryWrapper<SysDictType> dictTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictTypeLambdaQueryWrapper.ne(SysDictType::getStatus, CommonStatusEnum.DELETED.getCode());
        dictTypeLambdaQueryWrapper.eq(SysDictType::getCode, "JIRA_CustomFieldId");
        SysDictType sysDictType = dictTypeService.getOne(dictTypeLambdaQueryWrapper);
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, sysDictType.getId());
        List<SysDictData> sysDictDataList = dictDataService.list(queryWrapper);
        Map<String, List<Customfieldoption>> customfieldoptionListMap = new HashMap<>();

        for (SysDictData sysDictData : sysDictDataList) {
            LambdaQueryWrapper<Customfieldoption> optionQueryWrapper = new LambdaQueryWrapper<>();
            optionQueryWrapper.eq(Customfieldoption::getCustomfield, Long.parseLong(sysDictData.getValue()));
            optionQueryWrapper.eq(Customfieldoption::getDisabled, "N");
            List<Customfieldoption> optionList = this.list(optionQueryWrapper);
            customfieldoptionListMap.put(sysDictData.getCode(), optionList);
        }

        return customfieldoptionListMap;
    }

    @Override
    public List<Cate> cateList(String fieldName){

        List<Customfieldoption> lists = list(OptionQueryParam.builder().fieldName(fieldName).build());
        return lists.stream().map(e->{
            return Cate.builder().value(e.getCustomvalue()).pid(null == e.getParentoptionid() ? 1L : e.getParentoptionid()).id(e.getId()).sequence(e.getSequence()).build();
        }).collect(Collectors.toList());

    }

    @Override
    public List<Customfieldoption> listDepartment(OptionQueryParam optionQueryParam) {
        String fieldName = optionQueryParam.getFieldName();
        //到字典获取字段id
        String departmentField = fieldName != null&&fieldName.startsWith("JM")?"jmDepartment":"department";
        List<Customfieldoption> optionList = this.listAsCascading(OptionQueryParam.builder().fieldName(departmentField).build());
        if (fieldName == null) {
            return optionList;
        }
        //获取部门过滤规则进行过滤
        LambdaQueryWrapper<SysDictType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(SysDictType::getStatus, CommonStatusEnum.DELETED.getCode());
        lambdaQueryWrapper.eq(SysDictType::getCode, "JIRA_Department");
        SysDictType jiraDepartment = dictTypeService.getOne(lambdaQueryWrapper);
        LambdaQueryWrapper<SysDictData> jiraDepartmentQueryWrapper = new LambdaQueryWrapper<>();
        jiraDepartmentQueryWrapper.eq(SysDictData::getTypeId, jiraDepartment.getId());
        List<SysDictData> jiraDepartmentFilterDataList = dictDataService.list(jiraDepartmentQueryWrapper);
        Optional<SysDictData> first1 = jiraDepartmentFilterDataList.stream().filter(e -> e.getCode().equals(fieldName)).findFirst();
        if (first1.isPresent()) {
            //需要过滤
            SysDictData sysDictData1 = first1.get();
            String value = sysDictData1.getValue();
            List<String> split = Arrays.asList(value.split(","));
//            optionList = optionList.stream().filter(e -> split.contains(e.getId().toString())).collect(Collectors.toList());
            List<Customfieldoption> allOptionList = this.list(OptionQueryParam.builder().fieldName(departmentField).build());
            optionList = allOptionList.stream()
                    .filter(e -> split.contains(e.getId().toString()))
                    .sorted(Comparator.comparing(Customfieldoption::getParentoptionid,Comparator.nullsFirst(Long::compareTo)).thenComparing(Customfieldoption::getSequence))
                    .collect(Collectors.toList());
            optionList.forEach(customfieldoption->{
                Long id = customfieldoption.getId();
                List<Customfieldoption> subOptionList = allOptionList.stream().filter(e -> id.equals(e.getParentoptionid()))
                        .sorted(Comparator.comparing(Customfieldoption::getSequence))
                        .collect(Collectors.toList());
                customfieldoption.setSubOptionList(subOptionList);
            });
        }
        return optionList;
    }
}
