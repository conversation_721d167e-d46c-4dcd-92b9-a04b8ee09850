package eve.sys.modular.bom.params;

import java.util.List;

import eve.core.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysBomParam extends BaseParam{
    private Long id;



    private String bomName;

    private String bomVersion;

    private String bomTransport;

    private Long bomParentId;

    private String bomRemark;

    private Long bomStatus;

    private String bomProductType;

    private Long bomSourceId;

    private String bomFactory;

    private String bomData;

    private String bomNo;

    private Long bomIssueId;

    private String bomUse;

    private String bomCtype;

    private Long bomUpmen;

    private String bomStartdate;

    private int productState;

    private String manager;

    private int bomType;

    private String remark;

    /* 变更分类 */
    private Integer alterType;

    private String bomCode;

    private String bomPartName;

    private int pageNo;
    private int pageSize;

    private String werk;
    private String treeBomId;
    private String version;

    private Long fileId;

    private String num;

    //JIRA_ID使用状态  1：已使用  0 未使用
    private Integer jiraIdStatus;
    private List<Long> bomlineIds;

    private String bomIds;

    private List<BomSapProof> proofs;
}
