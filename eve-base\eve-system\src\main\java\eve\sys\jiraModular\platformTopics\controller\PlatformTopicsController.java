package eve.sys.jiraModular.platformTopics.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.jiraModular.platformTopics.entity.PlatformTopics;
import eve.sys.jiraModular.platformTopics.service.IPlatformTopicsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * VIEW 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@RestController
@RequestMapping("/platformTopics")
public class PlatformTopicsController {

    @Resource
    private IPlatformTopicsService platformTopicsService;

    @PostMapping("/list")
    @BusinessLog(title = "课题管理-视图查询课题", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(@RequestBody PlatformTopics platformTopics) {
        return new SuccessResponseData(platformTopicsService.list(platformTopics));
    }


}

