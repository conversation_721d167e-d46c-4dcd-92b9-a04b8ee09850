package eve.sys.modular.bom.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.*;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.techdoc.entity.TechDoc;
import eve.sys.modular.werkline.entity.SysWerkLine;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
public interface ISysBomService extends IService<SysBom> {

    SysBom add( SysBomParam sysBomParam);
    SysBom addBom( SysBomParam sysBomParam);

    Long edit(SysBomParam sysBomParam);

    SysBom get(Long id);

    List<SysBom> getLists(BomParam param);

    List<SapWerks> getWerks();

    List<BuConfig> getBuConfig();

    Map<String, List<SysWerkLine>> getWerkLines();

    void ABEndPushToJIRA(Long id, String token) throws IOException;

    JSONObject CDPushToJIRA(Long id) throws ServiceException, IOException;

    void OaAdd(SysBomParam param,Long id,List<Long> lineIds);

    Boolean endBomOaAdd(SysBomEnd param,Long id,List<SysWerkLine> sysWerkLines);

    void upgrade( SysBomParam sysBomParam);

    Map pdfUpdate(Long id, Boolean deleteFile) throws IOException;

    void bom2Sap(SysBom sysBom,ReceiveNameParam nameFromJIRA) throws ServiceException;

    Map pdfUpdateLine(Long id) throws IOException;

    Map endBomPdfUpdate(Long id) throws IOException;

    Map pdfUpdateAndCode( Long id, Boolean update, String remark,String alterType,ReceiveNameParam receiveNameParam) throws IOException;

    Map excelExport(Long id, Boolean update)
            throws IOException;

    Map excelExportByHistory(Long id, Boolean update)
            throws IOException;

    ReturnOaResult receiveStatusFromOA(ReceiveOaParam receiveStatusFromOA) throws IOException;

    Boolean receiveFromJIRA(ReceiveJIRAParam param) throws IOException;

    ReceiveNameParam getNameFromJIRA(SysBom bom);

    Boolean ABPushToJIRA(Long id, String token) throws IOException;

    void commit(SysBomParam param) throws IOException;

    List<OaAddOrEditParam> getLastHistory(Long bomId);

    Long copy(BomCopyParam param);

    List<SysBom> getBomFiles(Long issueId,boolean statusFlag,boolean statusFlag2);

    PageResult<SysBom> page(SysBomParam param);

    void updateCodeVersionFileName(SysBom bom,List<Long> addLines);

    void addWerk2BOM2Sap(SysBom sysBom,List<SysWerkLine> werkLines);

    void deleteWerk2BOM2Sap(SysBom sysBom, List<SysWerkLine> werkLines);

    void updateImportCodeVersionFileName(SysBom bom);

    Map<String,String> getTechVersionAndCode(SysBom packBom, TechDoc doc,int productStage);
    Map<String,String> getTechVersionAndCodeAB(TechDoc doc, String productName);

    void sapImportVerify(SysBomParam param);

    List<SapWerks> getWerksOptions();

    void sapImport(SysBomParam param);

    List<BomSapProof> sapVerify(SysBomParam param) throws ParseException;

    void delBom(SysBomParam param);

    void updateBomData(Long bomId);

    void adminConfirm(SysBom param);

    void withDraw(SysBomParam param);

    void werkALter2Oa(SysBomParam param);

    List<TreeBom> initBaseBom(SysBomParam param);
}
