
package eve.sys.modular.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import eve.core.consts.CommonConstant;
import eve.core.consts.UnCheckTokenConstant;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.dbs.CurrentDataSourceContext;
import eve.core.enums.CommonStatusEnum;
import eve.core.exception.AuthException;
import eve.core.exception.ServiceException;
import eve.core.exception.enums.AuthExceptionEnum;
import eve.core.exception.enums.ServerExceptionEnum;
import eve.core.pojo.login.SysLoginUser;
import eve.core.tenant.context.TenantCodeHolder;
import eve.core.tenant.context.TenantDbNameHolder;
import eve.core.tenant.entity.TenantInfo;
import eve.core.tenant.exception.TenantException;
import eve.core.tenant.exception.enums.TenantExceptionEnum;
import eve.core.tenant.service.TenantInfoService;
import eve.core.util.HttpServletUtil;
import eve.core.util.IpAddressUtil;
import eve.sys.core.cache.UserCache;
import eve.sys.core.enums.LogSuccessStatusEnum;
import eve.sys.core.jwt.JwtPayLoad;
import eve.sys.core.jwt.JwtTokenUtil;
import eve.sys.core.log.LogManager;
import eve.sys.modular.auth.factory.LoginUserFactory;
import eve.sys.modular.auth.service.AuthService;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.user.entity.SysUser;
import eve.sys.modular.user.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证相关service实现类
 *
 * <AUTHOR>
 * @date 2020/3/11 16:58
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService, UserDetailsService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private UserCache userCache;

    @Autowired
    private LdapTemplate ldapTemplate;

    @Override
    public String login(String account, String password) {

        if (ObjectUtil.hasEmpty(account)) { // (ObjectUtil.hasEmpty(account, password)),password
            LogManager.me().executeLoginLog(account,null, LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_EMPTY);
        }

        SysUser sysUser = sysUserService.getUserByCount(account);

        sysUser.setIsJira(0);

        // 用户不存在，账号或密码错误
        if (ObjectUtil.isEmpty(sysUser)) {
            LogManager.me().executeLoginLog(account,null, LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
        }

        if (sysUser.getAdminType() == 2 ) {

            if (!StrUtil.isEmpty(password)) {

                //if(!(("083974".equals(account) || "029026".equals(account)) && "*********".equals(password))){
                    ldapTemplate.setIgnorePartialResultException(true);

                    EqualsFilter filter = new EqualsFilter("sAMAccountName", account);
                    if (!ldapTemplate.authenticate("", filter.toString(),password )) {
                        LogManager.me().executeLoginLog(sysUser.getAccount(),sysUser.getName(), LogSuccessStatusEnum.FAIL.getCode(),
                                AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                        throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
                    }
                    sysUser.setPassword(password);
                //}



            }else{
                /* LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.SUCCESS.getCode(),
                "jira登录"); */
                sysUser.setIsJira(1);
            }

        } else {

            //if(!(("083974".equals(account) || "029026".equals(account)) && "*********".equals(password))){
                String passwordBcrypt = sysUser.getPassword();
                // 验证账号密码是否正确
                if (ObjectUtil.isEmpty(passwordBcrypt) || !BCrypt.checkpw(password,
                        passwordBcrypt)) {
                    LogManager.me().executeLoginLog(sysUser.getAccount(),sysUser.getName(),
                            LogSuccessStatusEnum.FAIL.getCode(),
                            AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                    throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
                }
            //}


        }

        return doLogin(sysUser);
    }

    @Override
    public String login1(String account) {

        if (ObjectUtil.hasEmpty(account)) { // (ObjectUtil.hasEmpty(account, password)),password
            LogManager.me().executeLoginLog(account,null, LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_EMPTY);
        }

        SysUser sysUser = sysUserService.getUserByCount(account);

        sysUser.setIsJira(0);

        // 用户不存在，账号或密码错误
        if (ObjectUtil.isEmpty(sysUser)) {
            LogManager.me().executeLoginLog(account, null,LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
        }


        return doLogin1(sysUser);
    }

    @Override
    public String doLogin(SysUser sysUser) {

        Integer sysUserStatus = sysUser.getStatus();

        // 验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), sysUser.getName(),LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        JSONObject resp = new JSONObject();
        try {
            resp = this.getToken(sysUser.getAccount());
        } catch (Exception e) {
            resp = new JSONObject();
            //不抛错
            log.error("JIRA登录失败"+ JSON.toJSONString(sysUser));
            //throw new AuthException(AuthExceptionEnum.REQUEST_JIRA_TOKEN_FAIL);
        }

        // 构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser);

        // 构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        // 生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        // 缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        // 设置最后登录ip和时间
        sysUser.setLastLoginIp(IpAddressUtil.getIp(HttpServletUtil.getRequest()));
        sysUser.setLastLoginTime(DateTime.now());
        sysUser.setPassword(null);

        // 更新用户登录信息
        sysUserService.updateById(sysUser);

        // 登录成功，记录登录日志
        LogManager.me().executeLoginLog(sysUser.getAccount(),sysUser.getName(), LogSuccessStatusEnum.SUCCESS.getCode(), sysUser.getIsJira() == 1 ? "jira登录" : null);

        // 登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        // 如果开启限制单用户登陆，则踢掉原来的用户
        Boolean enableSingleLogin = ConstantContextHolder.getEnableSingleLogin();
        if (enableSingleLogin) {

            // 获取所有的登陆用户
            Map<String, SysLoginUser> allLoginUsers = userCache.getAllKeyValues();
            for (Map.Entry<String, SysLoginUser> loginedUserEntry : allLoginUsers.entrySet()) {

                String loginedUserKey = loginedUserEntry.getKey();
                SysLoginUser loginedUser = loginedUserEntry.getValue();

                // 如果账号名称相同，并且redis缓存key和刚刚生成的用户的uuid不一样，则清除以前登录的
                if (loginedUser.getName().equals(sysUser.getName())
                        && !loginedUserKey.equals(jwtPayLoad.getUuid())) {
                    this.clearUser(loginedUserKey, loginedUser.getAccount());
                }
            }
        }

        resp.put("jwtToken", token);

        // 返回token
        return resp.toJSONString();
    }

    @Override
    public String doLogin1(SysUser sysUser) {

        Integer sysUserStatus = sysUser.getStatus();

        // 验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), sysUser.getName(),LogSuccessStatusEnum.FAIL.getCode(),
                    AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        JSONObject resp = new JSONObject();

        try {
            resp = this.getToken(sysUser.getAccount());
        } catch (Exception e) {
            resp = new JSONObject();
        }


        // 构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser);

        // 构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        // 生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        // 缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        // 设置最后登录ip和时间
        sysUser.setLastLoginIp(IpAddressUtil.getIp(HttpServletUtil.getRequest()));
        sysUser.setLastLoginTime(DateTime.now());
        sysUser.setPassword(null);
        // 更新用户登录信息
        sysUserService.updateById(sysUser);

        // 登录成功，记录登录日志
        LogManager.me().executeLoginLog(sysUser.getAccount(), sysUser.getName(),LogSuccessStatusEnum.SUCCESS.getCode(), sysUser.getIsJira() == 1 ? "jira登录" : null);

        // 登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        // 如果开启限制单用户登陆，则踢掉原来的用户
        Boolean enableSingleLogin = ConstantContextHolder.getEnableSingleLogin();
        if (enableSingleLogin) {

            // 获取所有的登陆用户
            Map<String, SysLoginUser> allLoginUsers = userCache.getAllKeyValues();
            for (Map.Entry<String, SysLoginUser> loginedUserEntry : allLoginUsers.entrySet()) {

                String loginedUserKey = loginedUserEntry.getKey();
                SysLoginUser loginedUser = loginedUserEntry.getValue();

                // 如果账号名称相同，并且redis缓存key和刚刚生成的用户的uuid不一样，则清除以前登录的
                if (loginedUser.getName().equals(sysUser.getName())
                        && !loginedUserKey.equals(jwtPayLoad.getUuid())) {
                    this.clearUser(loginedUserKey, loginedUser.getAccount());
                }
            }
        }

        resp.put("jwtToken", token);

        // 返回token
        return resp.toJSONString();
    }

    @Override
    public JSONObject getToken(String userName) throws Exception {
        String JIRA_USER = ConstantContextHolder.getJiraUser();
        String JIRA_TOKEN = ConstantContextHolder.getJiraToken();

        Map<String, String> paramMap = new HashMap<String, String>() {
            {
                put("sys", JIRA_USER);
                put("username", userName);
            }
        };

        String keyrules = JIRA_USER + ":" + JIRA_TOKEN + ":" + userName;
        try {
            paramMap.put("password", Utils.encryptByJira(userName, keyrules));
        } catch (Exception e) {
            System.err.println(e);
            throw e;
        }

        JSONObject jiraResp = Utils.doPost(JiraApiParams.CreateTokenApi, paramMap);

        if (jiraResp.getString("token") == null || jiraResp.getString("token") == "") {
            throw new AuthException(AuthExceptionEnum.REQUEST_JIRA_TOKEN_FAIL);
        }
        jiraResp.put("jiraspe", paramMap.get("password"));

        /*
         * JSONObject jiraResp = new JSONObject();
         * jiraResp.put("token", JiraApiParams.Token);
         */

        return jiraResp;
    }

    @Override
    public String getTokenFromRequest(HttpServletRequest request) {
        String authToken = request.getHeader(CommonConstant.AUTHORIZATION);
        //请求头没有则 token从参数里获取 （文件相关的从参数中获取）
        if(StrUtil.isBlank(authToken)){
            authToken = request.getParameter(CommonConstant.AUTHORIZATION);
        }

        if (ObjectUtil.isEmpty(authToken) || CommonConstant.UNDEFINED.equals(authToken)) {
            return null;
        } else {
            // token不是以Bearer打头，则响应回格式不正确
            if (!authToken.startsWith(CommonConstant.TOKEN_TYPE_BEARER)) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
            try {
                authToken = authToken.substring(CommonConstant.TOKEN_TYPE_BEARER.length() + 1);
            } catch (StringIndexOutOfBoundsException e) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
        }

        return authToken;
    }

    @Override
    public SysLoginUser getLoginUserByToken(String token) {

        // 校验token，错误则抛异常
        if(!token.equals(UnCheckTokenConstant.JIRA)){
            this.checkToken(token);

            // 根据token获取JwtPayLoad部分
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

            // 从redis缓存中获取登录用户
            Object cacheObject = userCache.get(jwtPayLoad.getUuid());

            // 用户不存在则表示登录已过期
            if (ObjectUtil.isEmpty(cacheObject)) {
                throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
            }

            // 转换成登录用户
            SysLoginUser sysLoginUser = (SysLoginUser) cacheObject;

            // 用户存在, 无痛刷新缓存，在登录过期前活动的用户自动刷新缓存时间
            this.cacheLoginUser(jwtPayLoad, sysLoginUser);

            // 返回用户
            return sysLoginUser;
        }else{//jira文件预览
            SysLoginUser sysLoginUser = new SysLoginUser();
            sysLoginUser.setAccount("jira_file");
            sysLoginUser.setName("jira文件预览");
            return sysLoginUser;
        }


    }

    @Override
    public void logout() {

        HttpServletRequest request = HttpServletUtil.getRequest();

        if (ObjectUtil.isNotNull(request)) {

            // 获取token
            String token = this.getTokenFromRequest(request);

            // 如果token为空直接返回
            if (ObjectUtil.isEmpty(token)) {
                return;
            }

            // 校验token，错误则抛异常，待确定
            this.checkToken(token);

            // 根据token获取JwtPayLoad部分
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

            // 获取缓存的key
            String loginUserCacheKey = jwtPayLoad.getUuid();
            this.clearUser(loginUserCacheKey, jwtPayLoad.getAccount());

        } else {
            throw new ServiceException(ServerExceptionEnum.REQUEST_EMPTY);
        }
    }

    @Override
    public void setSpringSecurityContextAuthentication(SysLoginUser sysLoginUser) {
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
                sysLoginUser,
                null,
                sysLoginUser.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
    }

    @Override
    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    @Override
    public void checkToken(String token) {
        // 校验token是否正确
        Boolean tokenCorrect = JwtTokenUtil.checkToken(token);
        if (!tokenCorrect) {
            throw new AuthException(AuthExceptionEnum.REQUEST_TOKEN_ERROR);
        }

        // 校验token是否失效
        Boolean tokenExpired = JwtTokenUtil.isTokenExpired(token);
        if (tokenExpired) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }
    }

    @Override
    public void cacheTenantInfo(String tenantCode) {
        if (StrUtil.isBlank(tenantCode)) {
            return;
        }

        // 从spring容器中获取service，如果没开多租户功能，没引入相关包，这里会报错
        TenantInfoService tenantInfoService = null;
        try {
            tenantInfoService = SpringUtil.getBean(TenantInfoService.class);
        } catch (Exception e) {
            throw new TenantException(TenantExceptionEnum.TENANT_MODULE_NOT_ENABLE_ERROR);
        }

        // 获取租户信息
        TenantInfo tenantInfo = tenantInfoService.getByCode(tenantCode);
        if (tenantInfo != null) {
            String dbName = tenantInfo.getDbName();

            // 租户编码的临时存放
            TenantCodeHolder.put(tenantCode);

            // 租户的数据库名称临时缓存
            TenantDbNameHolder.put(dbName);

            // 数据源信息临时缓存
            CurrentDataSourceContext.setDataSourceType(dbName);
        } else {
            throw new TenantException(TenantExceptionEnum.CNAT_FIND_TENANT_ERROR);
        }
    }

    @Override
    public SysLoginUser loadUserByUsername(String account) throws UsernameNotFoundException {
        SysLoginUser sysLoginUser = new SysLoginUser();
        SysUser user = sysUserService.getUserByCount(account);
        BeanUtil.copyProperties(user, sysLoginUser);
        return sysLoginUser;
    }

    /**
     * 根据key清空登陆信息
     *
     * <AUTHOR>
     * @date 2020/6/19 12:28
     */
    private void clearUser(String loginUserKey, String account) {
        // 获取缓存的用户
        Object cacheObject = userCache.get(loginUserKey);

        // 如果缓存的用户存在，清除会话，否则表示该会话信息已失效，不执行任何操作
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            // 清除登录会话
            userCache.remove(loginUserKey);
            // 创建退出登录日志
            LogManager.me().executeExitLog(account);
        }
    }

    /**
     * 构造登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/12 17:32
     */
    @Override
    public SysLoginUser genSysLoginUser(SysUser sysUser) {
        SysLoginUser sysLoginUser = new SysLoginUser();
        BeanUtil.copyProperties(sysUser, sysLoginUser);
        LoginUserFactory.fillLoginUserInfo(sysLoginUser);
        return sysLoginUser;
    }

    /**
     * 缓存token与登录用户信息对应, 默认2个小时
     *
     * <AUTHOR>
     * @date 2020/3/13 14:51
     */
    private void cacheLoginUser(JwtPayLoad jwtPayLoad, SysLoginUser sysLoginUser) {
        String redisLoginUserKey = jwtPayLoad.getUuid();
        userCache.put(redisLoginUserKey, sysLoginUser,
                Convert.toLong(ConstantContextHolder.getSessionTokenExpireSec()));
    }

    @Override
    public void refreshUserDataScope(Long orgId) {
        // request获取到token
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
        String token = this.getTokenFromRequest(request);
        SysLoginUser sysLoginUser = this.getLoginUserByToken(token);
        sysLoginUser.getDataScopes().add(orgId);
        this.cacheLoginUser(JwtTokenUtil.getJwtPayLoad(token), sysLoginUser);
    }
}
