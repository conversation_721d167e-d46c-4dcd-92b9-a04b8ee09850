 
package eve.core.consts;

/**
 * SpringSecurity相关常量
 *
 * <AUTHOR>
 * @date 2020/3/18 17:49
 */
public interface SpringSecurityConstant {

    /**
     * 放开权限校验的接口
     */
    String[] NONE_SECURITY_URL_PATTERNS = {

            //前端的
            "/favicon.ico",

            //swagger相关的
            "/doc.html",
            "/webjars/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v2/api-docs-ext",
            "/configuration/ui",
            "/configuration/security",

            //后端的
            "/",
            "/login",
            "/login1",
            "/login2",
            "/gateway/product/params",
            "/logout",
            "/gateway/product/params/**",
            "/oauth/**",
            //开放接口
            "/open/**",

            //文件的
            "/sysFileInfo/delete",
            "/sysFileInfo/upload",
            /*"/sysFileInfo/download",*/
            /*"/sysFileInfo/downloadWithAuth",*/
            /*"/sysFileInfo/preview",
            "/sysFileInfo/previewPdf",*/
            /*"/previewFromJIRA",*/
            "/fileType",
            "/sysFileInfo/previewFromJira",
            "/testDataExportTask/jmFileUpload",

            //druid的
            "/druid/**",

            //获取验证码
            "/captcha/**",
            "/getCaptchaOpen",

    };

}
