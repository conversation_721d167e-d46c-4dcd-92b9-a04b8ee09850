package eve.sys.modular.bom.params;

import java.io.Serializable;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class TreeBom implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private boolean open;

    private int order;

    private String parent_id;

    private String parentPartDescription;

    private String parentSapNumber;

    private String parentPartUnit;

    private double parentPartUse;

    private String parentVersion;

    private String partClass;

    private String partDescription;

    private String partName;

    private String partNumber;

    private String partUnit;

    private String sapNumber;

    private double partUse;
    
    private double sapPartUse;

    private double partLoss;

    private double baseUse;

    private String posnr;

    private String desc;

    private String partGroup;

    private int index;

    private Integer base;

    private Integer seq;

    private String partMaybe;

    private int partPriority;

    private String version;

    private boolean validate;

    private List<TreeBom> substitute;

    private List<TreeBom> lists;


    private Integer level;

    @Override
    public String toString() {
        return getSapPartUse() + "" + getPartLoss() + getPartUnit();
    }

    public boolean isLeaf() {
        return lists == null || lists.isEmpty();
    }

    public boolean noSub() {
        return substitute == null || substitute.isEmpty();
    }

    public String IDNRK(String werk,String version) {
        StringBuilder sb = new StringBuilder();
        sb.append(werk).append(getSapNumber()).append(version).append("1").append(getPartUnit())
        .append(String.format("%.3f", getSapPartUse()))
        .append(String.format("%.2f", getPartLoss()))
        .append(getPosnr());
        return sb.toString();
    }

    public String endIDNRK(String werk) {
        StringBuilder sb = new StringBuilder();
        sb.append(werk).append(getSapNumber())/* .append(version) */.append("1").append(getPartUnit())
        .append(String.format("%.3f", getSapPartUse()))
        .append(String.format("%.2f", getPartLoss()))
        //.append(getPosnr())
        ;
        return sb.toString();
    }

    public String MATNR(String werk,String version){
        StringBuilder sb = new StringBuilder();
        sb.append(werk).append(getSapNumber()).append(version).append(getPartUnit())
        .append(String.format("%.3f", 1000.000));
        return sb.toString();
    }

    public String endMATNR(String werk){
        StringBuilder sb = new StringBuilder();
        sb.append(werk).append(getSapNumber()).append(getPartUnit())
        .append(String.format("%.3f", 1000.000));
        return sb.toString();
    }

    public TreeBom newClone(){
        TreeBom one = new TreeBom();
        one.setId(this.id);
        one.setOpen(this.open);
        one.setOrder(this.order);
        one.setParent_id(this.parent_id);
        one.setPartClass(this.partClass);
        one.setPartDescription(this.partDescription);
        one.setPartName(this.partName);
        one.setPartNumber(this.partNumber);
        one.setPartUnit(this.partUnit);
        one.setSapNumber(this.sapNumber);
        one.setPartUse(this.partUse);
        one.setSapPartUse(this.sapPartUse);
        one.setPartLoss(this.partLoss);
        one.setBaseUse(this.baseUse);
        one.setPosnr(this.posnr);
        one.setDesc(this.desc);
        one.setPartGroup(this.partGroup);
        one.setIndex(this.index);
        one.setPartMaybe(this.partMaybe);
        one.setPartPriority(this.partPriority);
        one.setVersion(this.version);
        one.setValidate(this.validate);
        one.setLevel(this.level);
        one.setLists(null);
        one.setSubstitute(null);
        return one;
    }

}
