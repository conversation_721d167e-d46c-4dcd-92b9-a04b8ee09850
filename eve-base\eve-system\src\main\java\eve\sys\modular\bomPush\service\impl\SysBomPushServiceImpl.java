package eve.sys.modular.bomPush.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.context.login.LoginContextHolder;
import eve.core.factory.PageFactory;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bomPush.entity.SysBomPush;
import eve.sys.modular.bomPush.mapper.SysBomPushMapper;
import eve.sys.modular.bomPush.param.BomPushQueryParam;
import eve.sys.modular.bomPush.param.BomPushQueryParam2;
import eve.sys.modular.bomPush.service.ISysBomPushService;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomend.service.ISysBomEndService;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.techdoc.entity.TechDoc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * bom推送状态 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@Service
public class SysBomPushServiceImpl extends ServiceImpl<SysBomPushMapper, SysBomPush> implements ISysBomPushService {

    @Resource
    private ISysBomService bomService;

    @Resource
    private ISysBomEndService bomEndService;

    @Resource
    private ISysBomHistoryService historyService;

    /**
     *
     * @param bom
     * @param isBegin 是否起始节点
     * @param isReturn 是否其他系统回调
     * @param isFinish  流程是否已经结束
     * @param desc 描述
     * @param operator 操作人员
     * @param nextSys 下一个系统
     * @return
     */
    @Override
    public Boolean add(SysBom bom,Boolean isBegin,Boolean isReturn,Boolean isFinish,String desc,String operator,String nextSys,String url,Long fileId,Long processId){

        if(!isBegin){
            LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomPush::getBomId,bom.getId());
            queryWrapper.eq(SysBomPush::getType,-1);
            queryWrapper.orderByDesc(SysBomPush::getCreateTime);
            List<SysBomPush> list = this.list(queryWrapper);
            if(!ObjectUtil.isEmpty(list)){
                processId = list.get(0).getProcessId();
            }

        }
        String productState = getProductState(bom.getProductState());
        SysBomPush add = SysBomPush.builder()
                .describe(desc)
                .bomId(bom.getId())
                .operator(operator)
                .pushStatus(isReturn ? 2 : 1)
                .status(0)
                .sys(nextSys)
                .processId(processId)
                .stage(productState)
                .fileId(fileId)
                .url(url)
                .type(0)
                .build();

        return this.save(add);
    }

    @Override
    public Boolean addByTechDoc(TechDoc techDoc, Boolean isBegin, Boolean isReturn,
                                Boolean isFinish, String desc, String operator, String nextSys, String url, Long fileId, Long processId){
        SysBom sysBom = bomService.get(techDoc.getBomId());

        if(!isBegin){
            LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomPush::getBomId,techDoc.getId());
            queryWrapper.eq(SysBomPush::getType,techDoc.getTechType());
            queryWrapper.orderByDesc(SysBomPush::getCreateTime);
            List<SysBomPush> list = this.list(queryWrapper);
            if(!ObjectUtil.isEmpty(list)){
                processId = list.get(0).getProcessId();
            }

        }
        String productState = getProductState(sysBom.getProductState());
        SysBomPush add = SysBomPush.builder()
                .describe(desc)
                .bomId(techDoc.getId())
                .operator(operator)
                .pushStatus(isReturn ? 2 : 1)
                .status(0)
                .sys(nextSys)
                .processId(processId)
                .stage(productState)
                .fileId(fileId)
                .url(url)
                .type(techDoc.getTechType().intValue())
                .oaLink(techDoc.getOaLink())
                .build();

        return this.save(add);
    }
    @Override
    public Boolean addEnd(SysBomEnd sysBom,SysBom bom,Boolean isBegin,Boolean isReturn,Boolean isFinish,String desc,String operator,String nextSys,String url,Long fileId,Long processId){

        if(!isBegin){
            LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomPush::getBomId,sysBom.getId());
            queryWrapper.eq(SysBomPush::getType,-1);
            queryWrapper.orderByDesc(SysBomPush::getCreateTime);
            List<SysBomPush> list = this.list(queryWrapper);
            if(!ObjectUtil.isEmpty(list)){
                processId = list.get(0).getProcessId();
            }

        }
        String productState = getProductState(bom.getProductState());
        SysBomPush add = SysBomPush.builder()
                .describe(desc)
                .bomId(sysBom.getId())
                .operator(operator)
                .pushStatus(isReturn ? 2 : 1)
                .status(0)
                .sys(nextSys)
                .processId(processId)
                .stage(productState)
                .fileId(fileId)
                .url(url)
                .type(0)
                .build();

        return this.save(add);
    }

    @Override
    public SysBomPush queryByBomId(BomPushQueryParam param) {
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,param.getBomId());
        queryWrapper.eq(SysBomPush::getDeleteStatus,0);
        queryWrapper.eq(SysBomPush::getType,param.getType());
        queryWrapper.eq(SysBomPush::getPushStatus,2);
        SysBomPush bomPush = this.getOne(queryWrapper);
        return bomPush;
    }

    @Override
    public PageResult<SysBomPush> list(Long bomId, String userName,String keyword,Integer type){
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(SysBomPush::getStatus,1);
        if(null != bomId){
            queryWrapper.eq(SysBomPush::getBomId,bomId);
        }
        if(null != userName){
            queryWrapper.eq(SysBomPush::getSubmitter,userName);
        }
        if(null != type){
            queryWrapper.eq(SysBomPush::getType,type);
        }
        if(null != keyword){
            queryWrapper.and(wrapper ->
                    wrapper.like(SysBomPush::getName, keyword)
                            .or().like(SysBomPush::getOperateType,keyword)
                            .or().like(SysBomPush::getBomType,keyword)
                            .or().like(SysBomPush::getStage,keyword));
        }
        if(null != userName){
            queryWrapper.orderByDesc(SysBomPush::getCreateTime);
        }else{
            queryWrapper.orderByAsc(SysBomPush::getCreateTime);
        }


        queryWrapper.isNotNull(SysBomPush::getSubmitterId);



        return new PageResult(this.page(PageFactory.defaultPage(), queryWrapper));
    }



    @Override
    public Boolean add(SysBom bom,String issueKey,Long processId,String type){

        SysBomHistory sysBomHistory = historyService.getLastOne(bom.getId());
        SysBomHistory countByBomId = historyService.getCountByBomId(bom.getId());


        String productState = getProductState(bom.getProductState());
        SysBomPush add = SysBomPush.builder()
                //.describe(desc)
                .bomId(bom.getId())
                //.operator(operator)
                .pushStatus(0)
                .status(0)
                .sys(bom.getProductState() == 2 || bom.getProductState() == 3 ? "JIRA":"OA")
                .stage(productState)
                //.fileId(fileId)
                .url(issueKey)
                .bomType(1 == bom.getBomType()?"包装":"电芯")
                .processId(processId)
                .name(bom.getProductProjectName())
                .operateType(null!=type?type:(null == sysBomHistory && null == countByBomId ?"新增":"修改"))
                .submitterId(LoginContextHolder.me().getSysLoginUserId())
                .submitter(LoginContextHolder.me().getSysLoginUser().getName())
                .productState(Long.valueOf(bom.getProductState()))
                .remark(bom.getBomRemark())
                .type(-1)
                .build();
        return this.save(add);
    }

    @Override
    public Boolean addByTechDoc(TechDoc techDoc, String issueKey, Long processId){

        SysBom sysBom = bomService.get(techDoc.getBomId());


        String productState = getProductState(sysBom.getProductState());
        SysBomPush add = SysBomPush.builder()
                //.describe(desc)
                .bomId(techDoc.getId())
                //.operator(operator)
                .pushStatus(0)
                .status(0)
                .sys("JIRA")
                .stage(productState)
                //.fileId(fileId)
                .url(issueKey)
                .bomType(getFileTypeName(techDoc.getTechType()))
                .processId(processId)
                .name(sysBom.getProductProjectName())
                .operateType("A".equals(techDoc.getTechVersion()) ?"新增":"修改")
                .submitterId(LoginContextHolder.me().getSysLoginUserId())
                .submitter(LoginContextHolder.me().getSysLoginUser().getName())
                .type(techDoc.getTechType().intValue())
                .productState(Long.valueOf(sysBom.getProductState()))
                .remark(techDoc.getRemark())
                .change(techDoc.getContent())
                .oaLink(techDoc.getOaLink())
                .build();
        return this.save(add);
    }

    public String getFileTypeName(Long type){
        /**
         * 1:MI  2图纸  3特殊特性清单  4测试验证
         */
        if(new Long(0).equals(type)){
            return "MI";
        }
        if(new Long(1).equals(type)){
            return "图纸";
        }
        if(new Long(2).equals(type)){
            return "特殊特性清单";
        }
        if(new Long(3).equals(type)){
            return "测试验证";
        }
        return null;
    }


    @Override
    public Boolean addEnd(SysBomEnd bom, SysBom sysBom,String issueKey, Long processId, String type){

        String productState = getProductState(sysBom.getProductState());
        SysBomPush add = SysBomPush.builder()
                //.describe(desc)
                .bomId(bom.getId())
                //.operator(operator)
                .pushStatus(0)
                .status(0)
                .sys("OA")
                .stage(productState)
                //.fileId(fileId)
                .url(issueKey)
                .bomType("成品")
                .processId(processId)
                .name(sysBom.getProductProjectName())
                .operateType(type)
                .submitterId(LoginContextHolder.me().getSysLoginUserId())
                .submitter(LoginContextHolder.me().getSysLoginUser().getName())
                .type(-1)
                .build();
        return this.save(add);
    }


    @Override
    public Boolean update(SysBom bom, Integer status, Long fileId, String url, String nextSys){
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,bom.getId());
        /**
         * 推送状态 0 提交审批   2 OA已审核  6:推送SAP失败  3 已提交SAP 4 JIRA已受控  1 驳回
         */
        queryWrapper.ne(SysBomPush::getPushStatus,4);
        queryWrapper.ne(SysBomPush::getPushStatus,1);
        //queryWrapper.ne(SysBomPush::getPushStatus,6);
        queryWrapper.isNotNull(SysBomPush::getSubmitterId);

        List<SysBomPush> list = this.list(queryWrapper);

        if(ObjectUtil.isEmpty(list)){
            log.error(bom.getId()+"查找不到审核状态");
            return false;
        }

        SysBomPush sysBomPush = list.get(0);
        sysBomPush.setPushStatus(status);
        sysBomPush.setSys(nextSys);
        sysBomPush.setUrl(url);
        sysBomPush.setFileId(fileId);
        return this.updateById(sysBomPush);
    }

    @Override
    public String updateByTech(TechDoc doc, Integer status, Long fileId, String url, String nextSys){
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,doc.getId());
        /**
         * 推送状态 0 提交审批   2 OA已审核  6:推送SAP失败  3 已提交SAP 4 JIRA已受控  1 驳回
         */
        queryWrapper.eq(SysBomPush::getPushStatus,0);
        //queryWrapper.ne(SysBomPush::getPushStatus,1);
        //queryWrapper.ne(SysBomPush::getPushStatus,6);
        queryWrapper.isNotNull(SysBomPush::getSubmitterId);

        List<SysBomPush> list = this.list(queryWrapper);

        if(ObjectUtil.isEmpty(list)){
            log.error(doc.getId()+"MI图纸等查找不到审核状态");
            return null;
        }

        SysBomPush sysBomPush = list.get(0);
        sysBomPush.setPushStatus(status);
        sysBomPush.setSys(nextSys);
        sysBomPush.setUrl(url);
        sysBomPush.setFileId(fileId);
        this.updateById(sysBomPush);
        return sysBomPush.getSubmitter();
    }

    @Override
    public Boolean updateEnd(SysBomEnd bom, Integer status, Long fileId, String url, String nextSys){
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,bom.getId());
        /**
         * 推送状态 0 提交审批   2 OA已审核  6:推送SAP失败  3 已提交SAP 4 JIRA已受控
         */
        queryWrapper.ne(SysBomPush::getPushStatus,4);
        queryWrapper.ne(SysBomPush::getPushStatus,1);
        //queryWrapper.ne(SysBomPush::getPushStatus,6);
        queryWrapper.isNotNull(SysBomPush::getSubmitterId);

        List<SysBomPush> list = this.list(queryWrapper);

        if(ObjectUtil.isEmpty(list)){
            log.error(bom.getId()+"查找不到审核状态");
            return false;
        }

        SysBomPush sysBomPush = list.get(0);
        sysBomPush.setPushStatus(status);
        sysBomPush.setSys(nextSys);
        sysBomPush.setUrl(url);
        sysBomPush.setFileId(fileId);
        return this.updateById(sysBomPush);
    }

    @Override
    public Boolean update(SysBomEnd bom, Integer status, Long fileId, String url, String nextSys){
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,bom.getId());
        /**
         * 推送状态 0 提交审批   2 OA已审核  6:推送SAP失败  3 已提交SAP 4 JIRA已受控
         */
        queryWrapper.ne(SysBomPush::getPushStatus,4);
        queryWrapper.ne(SysBomPush::getPushStatus,1);
        //queryWrapper.ne(SysBomPush::getPushStatus,6);
        queryWrapper.isNotNull(SysBomPush::getSubmitterId);

        List<SysBomPush> list = this.list(queryWrapper);

        if(ObjectUtil.isEmpty(list)){
            log.error(bom.getId()+"查找不到审核状态");
            return false;
        }

        SysBomPush sysBomPush = list.get(0);
        sysBomPush.setPushStatus(status);
        sysBomPush.setSys(nextSys);
        sysBomPush.setUrl(url);
        sysBomPush.setFileId(fileId);
        return this.updateById(sysBomPush);
    }


    @Override
    public Map query(BomPushQueryParam2 param) {

        SysBom sysBom = bomService.getById(param.getId());


        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomPush::getBomId,param.getId());

        if(null != param.getProcessId()){
            queryWrapper.eq(SysBomPush::getProcessId,param.getProcessId());
        }

        queryWrapper.isNull(SysBomPush::getSubmitterId);
        queryWrapper.orderByAsc(SysBomPush::getId);
        List<SysBomPush> list = this.list(queryWrapper);
        Map result = new LinkedHashMap<>();
        List<Map> nodes = new LinkedList<>();
        List<Map> edges = new LinkedList<>();
        String parentId = IdUtil.fastUUID();
        Map<Object, Object> parent = new LinkedHashMap<>();
        parent.put("id",parentId);
        if(null == sysBom){
            SysBomPush sysBomPush = list.get(0);
            if(sysBomPush.getType() >= 0){
                parent.put("label",getFileTypeName(sysBomPush.getType().longValue()));
            }else{
                parent.put("label","成品BOM");
            }
        }else{
            if(0 == sysBom.getBomType()){
                parent.put("label","电芯BOM");
            }
            if(1 == sysBom.getBomType()){
                parent.put("label","包装BOM");
            }
        }


        nodes.add(parent);
        Map<Long, List<SysBomPush>> collect = list.stream().collect(Collectors.groupingBy(SysBomPush::getProcessId));

        List<Long> processIds = new ArrayList<>();

        for (Long processId:collect.keySet()){
            processIds.add(processId);
        }
        List<Long> collect1 = processIds.stream().sorted(Comparator.comparing(Long::longValue)).collect(Collectors.toList());

        Map<Long, List<SysBomPush>> collectFin = new LinkedHashMap<>();
        collect1.forEach(c -> {
            collectFin.put(c,collect.get(c));
        });

        for (Long processId:collectFin.keySet()){
            List<SysBomPush> list1 = collectFin.get(processId).stream().sorted(Comparator.comparing(SysBomPush::getCreateTime)).collect(Collectors.toList());
            for (int j = 0; j < list1.size(); j++) {
                Map<Object, Object> node = new LinkedHashMap<>();
                node.put("id",list1.get(j).getId());
                node.put("label",list1.get(j).getSys());
                node.put("url",list1.get(j).getUrl());
                node.put("fileId",list1.get(j).getFileId());
                Map<Object, Object> edge = new LinkedHashMap<>();
                Map<Object, Object> data = new LinkedHashMap<>();
                if(j == 0){
                    edge.put("source",parentId);
                }else{
                    edge.put("source",list1.get(j - 1).getId());
                }
                edge.put("target",list1.get(j).getId());
                data.put("type",list1.get(j).getStage());
                data.put("amount", (StrUtil.isBlank(list1.get(j).getOperator())?"":list1.get(j).getOperator()+" ")+list1.get(j).getDescribe());
                data.put("date", DateUtil.format(list1.get(j).getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                edge.put("data",data);
                nodes.add(node);
                edges.add(edge);
            }
        }
        result.put("nodes",nodes);
        result.put("edges",edges);


        return result;



    }

    public String getProductState(int num) {
        if (2 == num) {
            return "A样";
        }
        if (3 == num) {
            return "B样";
        }
        if (4 == num) {
            return "C样";
        }
        if (5 == num) {
            return "D样";
        }
        return null;
    }
}
