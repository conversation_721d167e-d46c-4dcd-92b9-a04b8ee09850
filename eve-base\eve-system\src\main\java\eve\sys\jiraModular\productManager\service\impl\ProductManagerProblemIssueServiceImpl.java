package eve.sys.jiraModular.productManager.service.impl;

import eve.sys.jiraModular.productManager.entity.ProductManagerProblemIssue;
import eve.sys.jiraModular.productManager.mapper.ProductManagerProblemIssueMapper;
import eve.sys.jiraModular.productManager.service.IProductManagerProblemIssueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * VIEW 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@Service
public class ProductManagerProblemIssueServiceImpl extends ServiceImpl<ProductManagerProblemIssueMapper, ProductManagerProblemIssue> implements IProductManagerProblemIssueService {

}
