package eve.sys.modular.competitive.competitiveAnalysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import eve.core.pojo.base.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 竞品分析
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Getter
@Setter
@TableName("COMPETITIVE_ANALYSIS")
@AllArgsConstructor
@NoArgsConstructor
public class CompetitiveAnalysis extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 竞品管理单号
     */
    private String code;

    /**
     * 状态 finished 完成 ongoing 进行中
     */
    private String status;

    /**
     * 竞品分析报告
     */
    private String reportName;

    /**
     * 竞品分析报告文件id
     */
    private Long reportId;

    /**
     * 厂商
     */
    private String factory;

    /**
     * 型号
     */
    private String model;

    /**
     * 类型
     */
    private String competitiveType;

    /**
     * 尺寸
     */
    private String competitiveSize;

    /**
     * 化学体系
     */
    private String chemicalSystem;

    /**
     * 容量
     */
    private BigDecimal capacity;

    /**
     * 应用领域
     */
    private String applicationArea;




}
