package eve.sys.jiraModular.productManager.controller;


import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.jiraModular.customTool.param.OptionQueryParam;
import eve.sys.jiraModular.productManager.service.IProductManagerProblemIssueService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * VIEW 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@RestController
@RequestMapping("/productManagerProblemIssue")
public class ProductManagerProblemIssueController {

    @Resource
    private IProductManagerProblemIssueService iProductManagerProblemIssueService;

    @PostMapping("/list")
    public ResponseData list() {
        return new SuccessResponseData(iProductManagerProblemIssueService.list());
    }

}

