package eve.sys.limsModular.test.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 测试条件参数值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@TableName("T_LIMS_TEST_PARAM_VALUE")
public class TLimsTestParamValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 条件ID
     */
    private Long conditionid;

    /**
     * 排序
     */
    private Long orderno;

    /**
     * 选项值
     */
    private String optionvalue;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 制单人名称
     */
    private String createdbyname;

    /**
     * 制单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;


}
