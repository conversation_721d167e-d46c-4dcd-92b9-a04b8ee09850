package eve.sys.limsModular.limsFile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.*;

/**
 * <p>
 * 文件表字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Getter
@Setter
@TableName("T_LIMS_FILE")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TLimsFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件类型ID
     */
    private Long documentcategoryid;

    /**
     * 一级分类
     */
    private String firstcategory;

    /**
     * 二级分类
     */
    private String secondcategory;

    /**
     * 内部/外部
     */
    private String isinternal;

    /**
     * 是否受控
     */
    private String iscontrol;

    /**
     * 文件编码
     */
    private String documentcode;

    /**
     * 文件名称
     */
    private String documentname;

    /**
     * 文件描述
     */
    private String documentdesc;

    /**
     * 当前文件版本
     */
    private String documentversionnow;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 添加人
     */
    private String createdbyname;

    /**
     * 添加时间
     */
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;


}
