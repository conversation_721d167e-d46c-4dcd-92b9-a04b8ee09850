<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.modular.bomhistory.mapper.SysBomHistoryMapper">
    <select id="getLastOne" parameterType="java.lang.Long" resultType="eve.sys.modular.bomhistory.entity.SysBomHistory" databaseId="oracle">
        SELECT * FROM (SELECT * FROM SYS_BOM_HISTORY WHERE BOM_ID = #{bomId} AND DELETE_STATUS = 0  AND PRODUCT_STATE &gt;= 4 ORDER BY CREATE_TIME DESC) WHERE ROWNUM=1
    </select>

    <select id="getCountByBomId" parameterType="java.lang.Long" resultType="eve.sys.modular.bomhistory.entity.SysBomHistory" databaseId="oracle">
        SELECT * FROM (SELECT * FROM SYS_BOM_HISTORY WHERE BOM_ID = #{bomId}  AND DELETE_STATUS = 0 AND PRODUCT_STATE &lt; 4 ORDER BY CREATE_TIME DESC) WHERE ROWNUM=1
    </select>

    <select id="getLastHistory" parameterType="java.lang.Long" resultType="eve.sys.modular.bomhistory.entity.SysBomHistory" databaseId="oracle">
        SELECT * FROM (SELECT * FROM SYS_BOM_HISTORY WHERE BOM_ID = #{bomId} AND DELETE_STATUS = 0  ORDER BY CREATE_TIME DESC) WHERE ROWNUM=1
    </select>

    <select id="getHistoryGroupByBomId" resultType="eve.sys.modular.bomhistory.entity.SysBomHistory">
        select * FROM (
            select row_number() over(partition by BOM_ID order by CREATE_TIME desc) rn,
            ID,BOM_ID,PRODUCT_STATE,CREATE_TIME,BOM_NO,BOM_VERSION,BOM_NAME,BOM_CHANGE,FILE_ID,TYPE,SUMMITOR,IS_END,IS_ADD_WERK
            from SYS_BOM_HISTORY WHERE DELETE_STATUS = 0  AND BOM_ID in
            <foreach item="item" collection="bomIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        ) where rn = 1
    </select>


    <select id="getLastHistoryGroupByBomId" resultType="eve.sys.modular.bomhistory.entity.SysBomHistory">
        select * FROM (
            select row_number() over(partition by BOM_ID order by CREATE_TIME desc) rn,BOM_ID,BOM_NO,BOM_VERSION,BOM_NAME,BOM_DATA from SYS_BOM_HISTORY where PRODUCT_STATE &gt;= 4  AND DELETE_STATUS = 0
        ) where rn = 1
    </select>

    <select id="getBomHis" resultType="eve.sys.modular.bom.entity.SysBomVo">
        SELECT
            t.line_name,
            h.*
        FROM
            SYS_BOM_HISTORY h
                LEFT JOIN
            (
                SELECT
                    l.bom_id,
                    LISTAGG(w.line_name, ',') line_name
                FROM
                    SYS_BOM_LINE l
                        INNER JOIN SYS_WERK_LINE w ON
                        w.ID = l.LINE_ID
                GROUP BY
                    l.bom_id ) t ON
                h.BOM_ID = t.BOM_ID
        <where>
            h.BOM_ID IN
            <foreach collection="bomIds" index="index" close=")" item="e" open="(" separator=",">
               #{e}
            </foreach>
        </where>
    </select>
</mapper>