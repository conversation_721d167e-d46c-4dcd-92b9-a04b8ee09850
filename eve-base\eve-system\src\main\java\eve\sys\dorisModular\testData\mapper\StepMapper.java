package eve.sys.dorisModular.testData.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import eve.sys.dorisModular.testData.entity.Step;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@DS("b4")
public interface StepMapper extends BaseMapper<Step> {
    void batchInsertSteps(List<Step> stepList);

    Integer selectStepNum(@Param("ew") LambdaQueryWrapper queryWrapper, @Param("num")Integer num);

    List<Step> selectStepPage(@Param("ew") LambdaQueryWrapper queryWrapper, @Param("start")Integer start, @Param("pageSize")Integer pageSize);
}
