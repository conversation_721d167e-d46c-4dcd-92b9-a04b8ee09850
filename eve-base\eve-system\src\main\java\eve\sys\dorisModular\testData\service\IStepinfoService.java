package eve.sys.dorisModular.testData.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.testData.entity.Stepinfo;
import eve.sys.mongoDbModular.shenghong.bean.HisStepInfo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@DS("b4")
public interface IStepinfoService extends IService<Stepinfo> {

    PageResult<Stepinfo> stepInfoPage(HisStepInfo param);
}
