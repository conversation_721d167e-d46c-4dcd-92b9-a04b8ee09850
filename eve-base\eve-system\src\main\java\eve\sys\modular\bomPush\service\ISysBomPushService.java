package eve.sys.modular.bomPush.service;

import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bomPush.entity.SysBomPush;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.sys.modular.bomPush.param.BomPushQueryParam;
import eve.sys.modular.bomPush.param.BomPushQueryParam2;
import eve.sys.modular.bomend.entity.SysBomEnd;

import eve.sys.modular.techdoc.entity.TechDoc;

import java.util.Map;

/**
 * <p>
 * bom推送状态 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
public interface ISysBomPushService extends IService<SysBomPush> {

    /**
     *
     * @param bom
     * @param isBegin 是否起始节点
     * @param isReturn 是否其他系统回调
     * @param isFinish  流程是否已经结束
     * @param desc 描述
     * @param operator 操作人员
     * @param nextSys 下一个系统
     * @param url 跳转url
     * @return
     */
    Boolean add(SysBom bom, Boolean isBegin, Boolean isReturn, Boolean isFinish, String desc, String operator, String nextSys,String url,Long fileId,Long processId);

    PageResult<SysBomPush> list(Long bomId, String userName,String keyword,Integer type);

    Boolean add(SysBom bom,String issueKey,Long processId,String type);

    Boolean addByTechDoc(TechDoc techDoc, String issueKey, Long processId);

    Boolean addEnd(SysBomEnd bom,SysBom sysBom, String issueKey, Long processId, String type);

    Boolean update(SysBom bom, Integer status, Long fileId, String url, String nextSys);

    String updateByTech(TechDoc doc, Integer status, Long fileId, String url, String nextSys);

    Boolean updateEnd(SysBomEnd bom, Integer status, Long fileId, String url, String nextSys);

    Boolean update(SysBomEnd bom, Integer status, Long fileId, String url, String nextSys);

    Map query(BomPushQueryParam2 param);

    Boolean addByTechDoc(TechDoc techDoc, Boolean isBegin, Boolean isReturn,
                         Boolean isFinish, String desc, String operator, String nextSys, String url, Long fileId, Long processId);

    Boolean addEnd(SysBomEnd sysBom, SysBom batteryBom, Boolean isBegin, Boolean isReturn, Boolean isFinish, String desc, String operator, String nextSys, String url, Long fileId, Long processId);

    SysBomPush queryByBomId(BomPushQueryParam param);
}
