<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.dorisModular.testData.mapper.CycleMapper">

    <insert id="batchInsertCycles" parameterType="java.util.List">
        INSERT INTO sync_data.cycle (
        cycle_id, flow_id, absolute_time, charge_capacity,
        discharge_capacity, charge_energy, discharge_energy,
        charge_capacity_ratio, charge_time, discharge_time,
        unit_num, channel_id, barcode, sync_date
        ) VALUES
        <foreach collection="list" item="cycle" index="index" separator=",">
            (
            #{cycle.cycleId},          <!-- 使用驼峰命名法 -->
            #{cycle.flowId},
            #{cycle.absoluteTime},
            #{cycle.chargeCapacity},
            #{cycle.dischargeCapacity},
            #{cycle.chargeEnergy},
            #{cycle.dischargeEnergy},
            #{cycle.chargeCapacityRatio},
            #{cycle.chargeTime},
            #{cycle.dischargeTime},
            #{cycle.unitNum},
            #{cycle.channelId},
            #{cycle.barcode},
            #{cycle.syncDate}
            )
        </foreach>
    </insert>




</mapper>
