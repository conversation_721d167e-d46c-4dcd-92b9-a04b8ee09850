package eve.sys.config;


import io.minio.MinioClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class MinioConfig {

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint("http://10.100.1.99:9000")
                .credentials("0OPH9lNuTC2EnLHF4Di6", "kGIHqQkEJBBmkIMR4wWy0sYSTI5cyOtkY0srRI1E")
                .build();
    }
 
}