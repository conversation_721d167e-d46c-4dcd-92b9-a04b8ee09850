package eve.sys.modular.bom.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import eve.sys.modular.bom.params.OaAddOrEditParam;
import eve.sys.modular.bom.params.OaApiParam;
import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.bomend.entity.SysBomEnd;

import eve.sys.modular.node.params.NodeItem;
import eve.sys.modular.node.service.ISysNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;

import javax.annotation.PostConstruct;

@Component
public class SysBomEndChangeUtil {

    public static SysBomEndChangeUtil sysBomChangeUtil;

    @Autowired
    private ISysNodeService nodeService;

    @PostConstruct
    public void init(){
        sysBomChangeUtil = this;
        sysBomChangeUtil.nodeService = this.nodeService;
    }
    public List<OaAddOrEditParam> loopTreeBom(SysBomEnd sysBom, List<TreeBom> treeBoms, Map<String, TreeBom> mapTreeBoms) {



        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        Map<String, Long> mapAdd = new HashMap<>();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);

                List<String> keys = mapTreeBoms.keySet().stream().filter(e -> e.startsWith(tempItem.getId()))
                        .collect(Collectors.toList());
                if (null != keys && !keys.isEmpty()) {

                    if (!tempItem.isLeaf()) {
                        queqeBoms.addAll(tempItem.getLists());
                    }
                    continue;
                }

                if (mapAdd.containsKey(tempItem.getId())) {
                    continue;
                }

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {

                        oaAddOrEditParams.add(addParam(sysBom, tempItem, _item));

                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {

                                oaAddOrEditParams.add(addParam(sysBom, tempItem, param));
                            }
                        }
                    }
                    mapAdd.put(tempItem.getId(), 0L);
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return oaAddOrEditParams;
    }

    public OaAddOrEditParam addParam(SysBomEnd sysBom, TreeBom item, TreeBom _item) {
        OaAddOrEditParam vo = new OaAddOrEditParam();
        vo.setFlag("新增");
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_item.getPartName());
        vo.setPartDescription(_item.getPartDescription());
        vo.setMSapNumber(item.getSapNumber());
        vo.setSapNumber(_item.getSapNumber());
        vo.setPartUnit(_item.getPartUnit());
        vo.setBomStartdate("");
        vo.setVersion(sysBom.getBomVersion());
        vo.setPartUse(_item.getPartUse());
        vo.setSapPartUse(_item.getSapPartUse());//
        vo.setPartLoss(_item.getPartLoss());
        vo.setDesc(_item.getDesc());
        return vo;
    }

    public Map<String, TreeBom> loopOldTreeBoms(List<TreeBom> treeBoms,Map<String, TreeBom> flagTreeMap) {

        Map<String, TreeBom> mapTreeBoms = new HashMap<>();
        if (null == treeBoms || treeBoms.isEmpty()) {
            return mapTreeBoms;
        }
        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                flagTreeMap.put(tempItem.getId(), tempItem);
                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        mapTreeBoms.put(tempItem.getId() + _item.getId(), _item);
                        flagTreeMap.put(_item.getId(), _item);
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                mapTreeBoms.put(tempItem.getId() + param.getId(), param);
                                flagTreeMap.put(param.getId(), param);
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return mapTreeBoms;
    }

    public List<OaAddOrEditParam> loopNewTreeBoms(SysBomEnd sysBom, List<TreeBom> treeBoms,
            Map<String, TreeBom> mapTreeBoms, List<TreeBom> newBoms,Map<String, TreeBom> flagTreeMap) {

        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        List<String> ids = new ArrayList<String>();
        List<NodeItem> allNodeList = sysBomChangeUtil.nodeService.getAll();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);
                Optional<NodeItem> first = allNodeList.stream().filter(e -> e.getNodeId().equals(tempItem.getPartClass())).findFirst();
                tempItem.setPartName(first.isPresent()?first.get().getNodeName():tempItem.getPartName());

                List<String> keys = mapTreeBoms.keySet().stream().filter(e -> e.startsWith(tempItem.getId()))
                        .collect(Collectors.toList());
                if ((null == keys || keys.isEmpty()) && !tempItem.isLeaf()) {

                    queqeBoms.addAll(tempItem.getLists());
                    newBoms.add(tempItem);

                    continue;
                }

                if (!tempItem.isLeaf()) {

                    for (TreeBom _item : tempItem.getLists()) {

                        addOrEditParam(sysBom, tempItem, _item, ids, mapTreeBoms, oaAddOrEditParams);

                        if (!_item.noSub()) {

                            for (TreeBom param : _item.getSubstitute()) {

                                addOrEditParam(sysBom, tempItem, param, ids, mapTreeBoms, oaAddOrEditParams);
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }

                for (Map.Entry<String, TreeBom> e : mapTreeBoms.entrySet()) {
                    if (e.getKey().startsWith(tempItem.getId()) && ids.indexOf(e.getKey()) == -1) {
                        OaAddOrEditParam _vo = deleteParams(sysBom, tempItem, e.getValue());
                        oaAddOrEditParams.add(0, _vo);
                        ids.add(e.getKey());
                    }
                }
            }
        }

        for (Map.Entry<String, TreeBom> e : mapTreeBoms.entrySet()) {

            if (ids.indexOf(e.getKey()) == -1) {

                TreeBom item = flagTreeMap.get(e.getValue().getParent_id());

                OaAddOrEditParam _vo = deleteParams(sysBom, item, e.getValue());
                oaAddOrEditParams.add(0, _vo);

            }
        }
        
        return oaAddOrEditParams;

    }

    private void addOrEditParam(
            SysBomEnd sysBom,
            TreeBom item,
            TreeBom _item,
            List<String> ids,
            Map<String, TreeBom> mapTreeBoms,
            List<OaAddOrEditParam> IT_DATA) {
        ids.add(item.getId() + _item.getId());
        OaAddOrEditParam vo = new OaAddOrEditParam();
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_item.getPartName());
        vo.setPartDescription(_item.getPartDescription());
        vo.setMSapNumber(item.getSapNumber());
        vo.setSapNumber(_item.getSapNumber());
        vo.setPartUnit(_item.getPartUnit());
        vo.setBomStartdate("");
        vo.setVersion(sysBom.getBomVersion());
        vo.setPartUse(_item.getPartUse());
        vo.setSapPartUse(_item.getSapPartUse());//division(_item.getPartUse(),item.getPartUse(),3)
        vo.setPartLoss(_item.getPartLoss());
        vo.setId(_item.getId());
        vo.setDesc(_item.getDesc());

        TreeBom _tempItem = mapTreeBoms.containsKey(item.getId() + _item.getId())
                ? mapTreeBoms.get(item.getId() + _item.getId())
                : null;
        if (_tempItem == null) {
            vo.setFlag("新增");

            IT_DATA.add(vo);
            return;
        }
        if ((_item.toString() + item.getSapPartUse()).equals(_tempItem.toString() + item.getSapPartUse())) {
            return;
        }

        vo.setFlag("修改");
        vo.setPrePartUse(_tempItem.getPartUse());// 变更前用量
        vo.setSapPrePartUse(_tempItem.getSapPartUse());
        vo.setPrePartLoss(_tempItem.getPartLoss());
        vo.setPrePartUnit(_tempItem.getPartUnit());
        IT_DATA.add(vo);
        _tempItem = null;
    }

    public OaAddOrEditParam deleteParams(SysBomEnd sysBom, TreeBom item, TreeBom value) {
        OaAddOrEditParam vo = null;
        TreeBom _tempItem = value;
        vo = new OaAddOrEditParam();
        vo.setFlag("删除");
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_tempItem.getPartName());
        vo.setPartDescription(_tempItem.getPartDescription());
        vo.setMSapNumber(item.getSapNumber());
        vo.setSapNumber(_tempItem.getSapNumber());
        vo.setPartUnit(_tempItem.getPartUnit());
        vo.setBomStartdate("");
        vo.setVersion(sysBom.getBomVersion());
        vo.setPartUse(0);
        vo.setSapPartUse(0);
        vo.setPartLoss(_tempItem.getPartLoss());
        vo.setPrePartUse(_tempItem.getPartUse());
        vo.setSapPrePartUse(_tempItem.getSapPartUse());
        vo.setId(_tempItem.getId());
        vo.setDesc(_tempItem.getDesc());
        _tempItem = null;
        return vo;
    }

    public String pushToOA(MultiValueMap<String, Object> valueMap) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("appkey", OaApiParam.AppKey);
        headers.add("Content-Type", "multipart/form-data");
        HttpEntity<Object> httpEntity = new HttpEntity<>(valueMap,
                headers);

        ResponseEntity<String> response = restTemplate.exchange(
                OaApiParam.BaseUrl + OaApiParam.AddReview,
                HttpMethod.POST,
                httpEntity,
                String.class);
        return response.getBody();

    }

    public List<OaAddOrEditParam> deleteBoms(SysBomEnd sysBom, List<TreeBom> treeBoms) {

        List<TreeBom> queqeBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
        List<NodeItem> allNodeList = sysBomChangeUtil.nodeService.getAll();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                Optional<NodeItem> first = allNodeList.stream().filter(e -> e.getNodeId().equals(tempItem.getPartClass())).findFirst();
                tempItem.setPartName(first.isPresent()?first.get().getNodeName():tempItem.getPartName());

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        oaAddOrEditParams.add(deleteParams(sysBom, tempItem, _item));
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                oaAddOrEditParams.add(deleteParams(sysBom, tempItem, param));
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return oaAddOrEditParams;
    }

    public void loopDeleteVersion(List<TreeBom> treeBoms, List<String> werks) {

        List<TreeBom> queqeBoms = new ArrayList<>();
        queqeBoms.addAll(treeBoms);
        while (queqeBoms.size() > 0) {

            TreeBom tempItem = queqeBoms.remove(0);

            if (!tempItem.getVersion().equals("")) {
                JSONObject obj = JSONObject.parseObject(tempItem.getVersion());

                for (String item : werks) {
                    if (obj.containsKey(item)) {
                        obj.remove(item);
                    }
                }
                tempItem.setVersion(obj.isEmpty() ? "" : JSONObject.toJSONString(obj));
            }

            if (!tempItem.noSub()) {
                queqeBoms.addAll(tempItem.getSubstitute());
            }
            if (!tempItem.isLeaf()) {
                queqeBoms.addAll(tempItem.getLists());
            }
        }
    }

    public Map<String, String> getVersionMap(List<TreeBom> treeBoms) {
        Map<String, String> versionMap = new HashMap<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        queqeBoms.addAll(treeBoms);
        while (queqeBoms.size() > 0) {

            TreeBom tempItem = queqeBoms.remove(0);

            versionMap.put(tempItem.getSapNumber(), tempItem.getVersion());

            if (!tempItem.noSub()) {
                for (TreeBom param : tempItem.getSubstitute()) {
                    versionMap.put(param.getSapNumber(), param.getVersion());
                }
            }
            if (!tempItem.isLeaf()) {
                queqeBoms.addAll(tempItem.getLists());
            }
        }
        return versionMap;
    }

    /* public double division(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));//子用量
        BigDecimal b2 = new BigDecimal(Double.toString(v2));//父用量
        BigDecimal b3 = b2.divide(new BigDecimal(1000));//父用量比例
        return b1.divide(b3, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    } */

    public List<OaAddOrEditParam> deleteEndBoms(SysBomEnd sysBom, List<TreeBom> treeBoms) {

        List<TreeBom> queqeBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
        List<NodeItem> allNodeList = sysBomChangeUtil.nodeService.getAll();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                Optional<NodeItem> first = allNodeList.stream().filter(e -> e.getNodeId().equals(tempItem.getPartClass())).findFirst();
                tempItem.setPartName(first.isPresent()?first.get().getNodeName():tempItem.getPartName());
                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        oaAddOrEditParams.add(deleteEndParams(sysBom, tempItem, _item));
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                oaAddOrEditParams.add(deleteEndParams(sysBom, tempItem, param));
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return oaAddOrEditParams;
    }

    public OaAddOrEditParam deleteEndParams(SysBomEnd sysBom, TreeBom item, TreeBom value) {
        OaAddOrEditParam vo = null;
        TreeBom _tempItem = value;
        vo = new OaAddOrEditParam();
        vo.setFlag("删除");
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_tempItem.getPartName());
        vo.setPartDescription(_tempItem.getPartDescription());
        vo.setMSapNumber(item.getSapNumber());
        vo.setSapNumber(_tempItem.getSapNumber());
        vo.setPartUnit(_tempItem.getPartUnit());
        vo.setBomStartdate("");
        vo.setVersion("");
        vo.setPartUse(0);
        vo.setSapPartUse(0);
        vo.setPartLoss(_tempItem.getPartLoss());
        vo.setPrePartUse(_tempItem.getPartUse());
        vo.setSapPrePartUse(_tempItem.getSapPartUse());
        vo.setId(_tempItem.getId());
        vo.setDesc(_tempItem.getDesc());
        _tempItem = null;
        return vo;
    }


    public List<OaAddOrEditParam> addEndBoms(SysBomEnd sysBom, List<TreeBom> treeBoms) {

        List<TreeBom> queqeBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        oaAddOrEditParams.add(addEndParam(sysBom, tempItem, _item));
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                oaAddOrEditParams.add(addEndParam(sysBom, tempItem, param));
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return oaAddOrEditParams;
    }

    public OaAddOrEditParam addEndParam(SysBomEnd sysBom, TreeBom item, TreeBom _item) {
        OaAddOrEditParam vo = new OaAddOrEditParam();
        vo.setFlag("新增");
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_item.getPartName());
        vo.setPartDescription(_item.getPartDescription());
        vo.setMSapNumber(item.getSapNumber());
        vo.setSapNumber(_item.getSapNumber());
        vo.setPartUnit(_item.getPartUnit());
        vo.setBomStartdate("");
        vo.setVersion("");
        vo.setPartUse(_item.getPartUse());
        vo.setSapPartUse(_item.getSapPartUse());//division(_item.getPartUse(),item.getPartUse(),3)
        vo.setPartLoss(_item.getPartLoss());
        vo.setDesc(_item.getDesc());
        return vo;
    }
}
