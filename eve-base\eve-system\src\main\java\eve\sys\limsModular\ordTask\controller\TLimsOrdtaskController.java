package eve.sys.limsModular.ordTask.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试验项目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/tLimsOrdtask")
public class TLimsOrdtaskController {

    @Autowired
    ITLimsOrdtaskService tLimsOrdtaskService;

    @PostMapping("/getLimsOrdtaskList")
    @BusinessLog(title = "任务分配-查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLimsOrdtaskList(@RequestBody Map param) throws Exception{
        return new SuccessResponseData(tLimsOrdtaskService.getLimsOrdtaskList(param));
    }

    @PostMapping("/getLimsOrdtaskListOfSafety")
    @BusinessLog(title = "任务分配-查询-第四实验室", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLimsOrdtaskListOfSafety(@RequestBody Map param) throws Exception{
        return new SuccessResponseData(tLimsOrdtaskService.getLimsOrdtaskListOfSafety(param));
    }

    @PostMapping("/getLimsOrdtasksOfPreView")
    @BusinessLog(title = "结果复核-查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLimsOrdtasksOfPreView(@RequestBody Map param) throws Exception{
        return ResponseData.success(tLimsOrdtaskService.getLimsOrdtasksOfPreView(param));
    }

    @PostMapping("/getLimsOrdtaskListByParam")
    @BusinessLog(title = "测试管理-反选LIMS测试项目", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLimsOrdtaskList(@RequestBody TLimsOrdtask param) {
        return new SuccessResponseData(tLimsOrdtaskService.getLimsOrdtaskListByParam(param));
    }

    @PostMapping("/getFolderInfoByParam")
    @BusinessLog(title = "工作台-查询委托单信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getFolderInfoByParam(@RequestBody TLimsOrdtask param) {
        return new SuccessResponseData(tLimsOrdtaskService.getFolderInfoByParam(param));
    }

    @PostMapping("/getTestPerson")
    @BusinessLog(title = "任务分配-获取测试员", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getTestPerson(@RequestBody Map param) {
        return new SuccessResponseData(tLimsOrdtaskService.getTestPerson(param));
    }

    @GetMapping("/getOrdTasksByTesterCode/{testerCode}")
    @BusinessLog(title = "任务分配-根据测试员获取对应测试项目", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getOrdTasksByTesterCode(@PathVariable("testerCode") String testerCode) {
        return new SuccessResponseData(tLimsOrdtaskService.getOrdTasksByTesterCode(testerCode));
    }

    @PostMapping("/updateOrdtaskStatus")
    @BusinessLog(title = "任务分配-执行任务分配", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData updateOrdtaskStatus(@RequestBody Map param) throws Exception {
        return tLimsOrdtaskService.updateOrdtaskStatus(param);
    }

    @PostMapping("/assignTaskOfAq")
    @BusinessLog(title = "任务分配-执行任务分配-第四实验室", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData assignTaskOfAq(@RequestBody Map param) throws Exception {
        return tLimsOrdtaskService.assignTaskOfAq(param);
    }

    @PostMapping("/InputDataSubmit")
    @BusinessLog(title = "MI附图版本-查询", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData InputDataSubmit(@RequestBody Map param){
        return tLimsOrdtaskService.InputDataSubmit(param);
    }

    @PostMapping("/reviewSubmit")
    @BusinessLog(title = "结果复核-提交数据", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData reviewSubmit(@RequestBody List<TLimsOrdtask> ordTaskList){
        return tLimsOrdtaskService.reviewSubmit(ordTaskList);
    }

    @PostMapping("/reviewSendBack")
    @BusinessLog(title = "结果复核-回退", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData reviewSendBack(@RequestBody Map param){
        return tLimsOrdtaskService.reviewSendBack(param);
    }
}

