package eve.sys.limsModular.folder.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 委托单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/tLimsFolder")
public class TLimsFolderController {

    @Resource
    private ITLimsFolderService folderService;

    @PostMapping("/listPage")
    @BusinessLog(title = "委托单-分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData listPage(@RequestBody TLimsFolder param) {
        return new SuccessResponseData(folderService.listPage(param));
    }

    @PostMapping("/getByFolderNo")
    @BusinessLog(title = "委托单-根据委托单号查询委托单信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getByFolderNo(@RequestBody TLimsFolder param) {
        return new SuccessResponseData(folderService.getByFolderNo(param));
    }
}

