package eve.sys.dorisModular.testData.service.impl;

import com.alibaba.fastjson.JSON;
import eve.sys.dorisModular.testData.service.TestDataSyncService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@Slf4j
public class TestDataSyncServiceImpl implements TestDataSyncService {

    /**
     * 同步所有表数据
     */
    @Override
    public void syncAllTableData(List<TLimsTestdataPrimary> list) {
        RestTemplate restTemplate = new RestTemplate();

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(list), headers);

        ResponseEntity<Boolean> response = restTemplate.exchange(
                "http://10.100.1.99:8099/open/sync/syncMongodb",
                HttpMethod.POST,
                entity,
                Boolean.class);

        if (response.getStatusCode() == HttpStatus.OK) {
            log.info("数据同步请求成功：" + response.getBody());
        } else {
            log.info("数据同步请求失败：" + response.getStatusCode());
        }

    }


}
