 
package eve.core.pojo.base.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通用基础字段，需要此通用字段的实体可继承此类
 *
 * <AUTHOR>
 * @date 2020/3/10 16:02
 */
@Data
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Excel(name = "创建时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人 废弃字段
     */
    @TableField(exist = false)
    private Long createUser;

    @TableField(exist = false)
    private Boolean notInsert;

    @TableField(exist = false)
    private Boolean notUpdate;

    /**
     * 创建人工号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createAccount;

    /**
     * 创建人姓名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @Excel(name = "更新时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人 废弃字段
     */
    @TableField(exist = false)
    private Long updateUser;

    /**
     * 更新人工号
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateAccount;

    /**
     * 更新人姓名
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateName;

    /**
     * 逻辑删除标识，1:已删除，0:正常
     */
    private Integer deleteStatus;

    @TableField(exist = false)
    private Integer pageSize = 20;

    @TableField(exist = false)
    private Integer pageNo = 1;

}
