package eve.sys.modular.competitive.competitiveAnalysis.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.competitive.competitiveAnalysis.entity.CompetitiveAnalysis;
import eve.sys.modular.competitive.competitiveAnalysis.mapper.CompetitiveAnalysisMapper;
import eve.sys.modular.competitive.competitiveAnalysis.service.ICompetitiveAnalysisService;
import eve.sys.modular.competitive.competitiveAnalysisFile.entity.CompetitiveAnalysisFile;
import eve.sys.modular.competitive.competitiveAnalysisFile.service.ICompetitiveAnalysisFileService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 竞品分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class CompetitiveAnalysisServiceImpl extends ServiceImpl<CompetitiveAnalysisMapper, CompetitiveAnalysis> implements ICompetitiveAnalysisService {

    @Resource
    private ICompetitiveAnalysisFileService competitiveAnalysisFileService;

    @Override
    public PageResult<CompetitiveAnalysis> pageList(CompetitiveAnalysis param) {
        LambdaQueryWrapper<CompetitiveAnalysis> queryWrapper = new LambdaQueryWrapper<>();

        if(StrUtil.isNotBlank(param.getCompetitiveType())){
            queryWrapper.eq(CompetitiveAnalysis::getCompetitiveType,param.getCompetitiveType());
        }

        if(StrUtil.isNotBlank(param.getFactory())){
            queryWrapper.eq(CompetitiveAnalysis::getFactory,param.getFactory());
        }
        if(StrUtil.isNotBlank(param.getCode())){
            queryWrapper.like(CompetitiveAnalysis::getCode,param.getCode());
        }
        if(StrUtil.isNotBlank(param.getStatus())){
            queryWrapper.like(CompetitiveAnalysis::getStatus,param.getStatus());
        }
        if(StrUtil.isNotBlank(param.getModel())){
            queryWrapper.like(CompetitiveAnalysis::getModel,param.getModel());
        }

        if(StrUtil.isNotBlank(param.getChemicalSystem())){
            queryWrapper.like(CompetitiveAnalysis::getChemicalSystem,param.getChemicalSystem());
        }


        queryWrapper.orderByAsc(CompetitiveAnalysis::getCode);

        Page<CompetitiveAnalysis> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        return new PageResult<>(page);

    }

    public static double divideAndMultiply(long a, long b) {
        // 将两个长整数相除
        BigDecimal result = new BigDecimal(a).divide(new BigDecimal(b), 4, RoundingMode.HALF_UP);

        // 将结果乘以100
        result = result.multiply(new BigDecimal(100));

        // 将结果保留两位小数
        result = result.setScale(2, RoundingMode.HALF_UP);

        return result.doubleValue();
    }

    @Override
    public Map echartsData(String echartsType){

        //查询所有数据
        List<CompetitiveAnalysis> allList = this.list();
        //查询厂商集合
        List<String> factoryList = allList.stream().map(l -> l.getFactory()).distinct().sorted().collect(Collectors.toList());

        //图表分类 echartsType chemicalSystem 化学体系  type 类型
        List<Map> series = new ArrayList<>();
        if("chemicalSystem".equals(echartsType)){
            //查询总共有多少种化学体系
            List<String> chemicalSystemList = allList.stream().map(l -> l.getChemicalSystem()).distinct().sorted().collect(Collectors.toList());

            for (String chemicalSystem:chemicalSystemList){
                Map serie = new HashMap();
                serie.put("name",chemicalSystem);
                serie.put("type","bar");
                serie.put("yAxisIndex",0);
                List<Long> data = new ArrayList<>();
                for (String factory:factoryList){
                    Long chemicalSystemCount = allList.stream().filter(a -> factory.equals(a.getFactory()) && chemicalSystem.equals(a.getChemicalSystem())).count();
                    data.add(chemicalSystemCount);
                }
                serie.put("data",data);
                series.add(serie);
            }

        }
        if("type".equals(echartsType)){
            //查询总共有多少种类型
            List<String> typeList = allList.stream().map(l -> l.getCompetitiveType()).distinct().sorted().collect(Collectors.toList());

            for (String type:typeList){
                Map serie = new HashMap();
                serie.put("name",type);
                serie.put("type","bar");
                serie.put("yAxisIndex",0);
                List<Long> data = new ArrayList<>();
                for (String factory:factoryList){
                    Long typeCount = allList.stream().filter(a -> factory.equals(a.getFactory()) && type.equals(a.getCompetitiveType())).count();
                    data.add(typeCount);
                }
                serie.put("data",data);
                series.add(serie);
            }

        }

        Map serie = new HashMap();
        serie.put("name","竞品分析报告完成率");
        serie.put("type","line");
        serie.put("yAxisIndex",1);
        List<Double> data = new ArrayList<>();
        //分析报告完成率
        for (String factory:factoryList){
            Long factoryCount = allList.stream().filter(a -> factory.equals(a.getFactory())).count();
            //已完成分析报告
            Long factoryReportCount = allList.stream().filter(a -> factory.equals(a.getFactory()) && "finished".equals(a.getStatus())).count();
            double v = divideAndMultiply(factoryReportCount, factoryCount);
            data.add(v);
        }
        serie.put("data",data);
        series.add(serie);

        Map result = new HashMap<>();
        result.put("xData",factoryList);
        result.put("series",series);

        return result;
    }

    @Override
    public Map treeData(String queryParam){

        Map result = new HashMap();

        LambdaQueryWrapper<CompetitiveAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        /*if(StrUtil.isNotBlank(queryParam)){
            queryWrapper.like(CompetitiveAnalysis::getCode,queryParam).or()
                    .like(CompetitiveAnalysis::getReportName,queryParam).or()
                    .like(CompetitiveAnalysis::getFactory,queryParam).or()
                    .like(CompetitiveAnalysis::getModel,queryParam).or()
                    .like(CompetitiveAnalysis::getChemicalSystem,queryParam).or()
                    .like(CompetitiveAnalysis::getApplicationArea,queryParam);
        }*/

        queryWrapper.orderByAsc(CompetitiveAnalysis::getCode);
        //查询数据
        List<CompetitiveAnalysis> allList = this.list(queryWrapper);
        //查询已上传文件
        LambdaQueryWrapper<CompetitiveAnalysisFile> fileQueryWrapper = new LambdaQueryWrapper<>();
        //竞品分析id
        List<Long> idList = allList.stream().map(l -> l.getId()).distinct().sorted().collect(Collectors.toList());
        fileQueryWrapper.in(CompetitiveAnalysisFile::getCompetitiveId,idList);
        if(StrUtil.isNotBlank(queryParam)){
//            fileQueryWrapper.like(CompetitiveAnalysisFile::getFileName,queryParam);
        }
        List<CompetitiveAnalysisFile> fileList = competitiveAnalysisFileService.list(fileQueryWrapper);

        List<Map> competitiveAnalysisDataList = new ArrayList<>();
        for (CompetitiveAnalysis competitiveAnalysis:allList){
            Map competitiveAnalysisData = new HashMap();
            List<CompetitiveAnalysisFile> disassembleList = fileList.stream().filter(f -> f.getCompetitiveId().equals(competitiveAnalysis.getId()) && "disassemble".equals(f.getFileType())).collect(Collectors.toList());
            List<CompetitiveAnalysisFile> electricalList = fileList.stream().filter(f -> f.getCompetitiveId().equals(competitiveAnalysis.getId()) && "electrical".equals(f.getFileType())).collect(Collectors.toList());
            List<CompetitiveAnalysisFile> safeList = fileList.stream().filter(f -> f.getCompetitiveId().equals(competitiveAnalysis.getId()) && "safe".equals(f.getFileType())).collect(Collectors.toList());
            competitiveAnalysisData.put("disassembleList",disassembleList);
            competitiveAnalysisData.put("electricalList",electricalList);
            competitiveAnalysisData.put("safeList",safeList);
            competitiveAnalysisData.put("code",competitiveAnalysis.getCode());
            competitiveAnalysisData.put("id",competitiveAnalysis.getId());
            competitiveAnalysisDataList.add(competitiveAnalysisData);
        }

        List<CompetitiveAnalysis> list = this.list();
        result.put("total",list.size());
        result.put("finished",list.stream().filter(l -> "finished".equals(l.getStatus())).count());
        result.put("ongoing",list.stream().filter(l -> "ongoing".equals(l.getStatus())).count());
        result.put("tree",competitiveAnalysisDataList);

        return result;

    }



    @Override
    public Boolean remove(CompetitiveAnalysis param){
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(CompetitiveAnalysis param){
        return this.updateById(param);
    }

    @Override
    public Boolean add(CompetitiveAnalysis param){
        return this.save(param);
    }

}
