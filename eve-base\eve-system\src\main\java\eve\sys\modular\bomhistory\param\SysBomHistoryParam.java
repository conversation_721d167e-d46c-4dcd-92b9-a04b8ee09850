package eve.sys.modular.bomhistory.param;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class SysBomHistoryParam implements Serializable{

    private Long bomId;

    private int productState;

    private String bomData;

    private Date createTime;

    private String remark;

    private String bomNo;

    private String bomVersion;

    private String bomName;

    private String bomChange;

    private Long fileId;

    private Integer type;

    //提交人
    private String summitor;

    private Integer isEnd;

    private Integer isAddWerk; // 1 新增  2 删除

    private String bomLines;
    
    private String bomTransport;

    private Long cellBomId;

    private Long packBomId;

    /* 变更分类 */
    private Integer alterType;
}
