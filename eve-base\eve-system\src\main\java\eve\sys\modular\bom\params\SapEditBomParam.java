package eve.sys.modular.bom.params;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SapEditBomParam {
    @JSONField(name = "IV_MATNR")
    private String IV_MATNR; // 物料编号(SAP编号

    @JSONField(name = "IV_WERKS")
    private String IV_WERKS; // 工厂（1091,4060调用2次）

    @JSONField(name = "IV_STLAL")
    private String IV_STLAL; // BOM版本号 (默认不填)

    @JSONField(name = "IV_AENNR")
    private String IV_AENNR; // 变更编号（创建sap dcn编号）

    @JSONField(name = "IV_BMENG")
    private Double IV_BMENG; // 基本数量（3位小数）

    @JSONField(name = "IV_STKTX")
    private String IV_STKTX ;//产线备注

    @JSONField(name = "IT_DATA")
    private List<SapEditBomItOrEtParam> IT_DATA;// 输入表

    public boolean noItData() {
        return IT_DATA == null || IT_DATA.isEmpty();
    }

    public SapEditBomParam newClone() {
        SapEditBomParam sapEditBomParam = new SapEditBomParam(
                this.IV_MATNR,
                this.IV_WERKS,
                this.IV_STLAL,
                this.IV_AENNR,
                this.IV_BMENG,
                this.IV_STKTX,
                new ArrayList<>());

        for (SapEditBomItOrEtParam item : this.IT_DATA) {
            sapEditBomParam.getIT_DATA().add(item.newClone());
        }
        return sapEditBomParam;
    }

    public List<SapEditBomParam> flat2List(){
        List<SapEditBomParam> list = new ArrayList<>();
        for (SapEditBomItOrEtParam item : this.IT_DATA) {
            SapEditBomParam sapEditBomParam = new SapEditBomParam(
                this.IV_MATNR,
                this.IV_WERKS,
                this.IV_STLAL,
                this.IV_AENNR,
                this.IV_BMENG,
                this.IV_STKTX,
                new ArrayList<>());
            sapEditBomParam.getIT_DATA().add(item);
            list.add(sapEditBomParam);
        }
        return list;
    }

}
