package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sync_data.step")
public class Step implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 工步序号
     */
    private Long stepNum;

    /**
     * 工步号
     */
    private Long stepId;

    /**
     * 循环号
     */
    private Long cycleId;

    /**
     * 数据关联id
     */
    private String flowId;

    /**
     * 工步名称
     */
    private String stepName;

    /**
     * 数据生成时间
     */
    private String absoluteTime;

    /**
     * 工步时间
     */
    private String stepTime;

    /**
     * 起始电压
     */
    private BigDecimal beginVoltage;

    /**
     * 结束电压
     */
    private BigDecimal endVoltage;

    /**
     * 起始电流
     */
    private BigDecimal beginCurrent;

    /**
     * 结束电流
     */
    private BigDecimal endCurrent;

    /**
     * 容量
     */
    private BigDecimal capacity;

    /**
     * 能量
     */
    private BigDecimal energy;

    /**
     * 累计充电容量
     */
    private BigDecimal accumulateChargeCapacity;

    /**
     * 累计放电容量
     */
    private BigDecimal accumulateDischargeCapacity;

    /**
     * 累计充电能量
     */
    private BigDecimal accumulateChargeEnergy;

    /**
     * 累计放电容量
     */
    private BigDecimal accumulateDischargeEnergy;

    /**
     * 起始温度1
     */
    private BigDecimal startTemp1;

    /**
     * 起始温度2
     */
    private BigDecimal startTemp2;

    /**
     * 起始温度3
     */
    private BigDecimal startTemp3;

    /**
     * 起始温度4
     */
    private BigDecimal startTemp4;

    /**
     * 结束温度1
     */
    private BigDecimal endTemp1;

    /**
     * 结束温度2
     */
    private BigDecimal endTemp2;

    /**
     * 结束温度3
     */
    private BigDecimal endTemp3;

    /**
     * 结束温度4
     */
    private BigDecimal endTemp4;

    /**
     * 横流比
     */
    private BigDecimal cccapacityRate;

    /**
     * 起始膨胀力
     */
    private String startPressure;

    /**
     * 结束膨胀力
     */
    private String endPressure;

    /**
     * 最大膨胀力
     */
    private String maxPressure;

    /**
     * 最小膨胀力
     */
    private String minPressure;

    /**
     * 起始soc
     */
    private String startSocCalculate;

    /**
     * 结束soc
     */
    private String endSocCalculate;

    /**
     * 设备编号
     */
    private String unitNum;

    /**
     * 通道编号
     */
    private String channelId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 同步doris时间
     */
    private Date syncDate;

    /**
     * 数据上传时间
     */
    private String createTime;

    /**
     * 设备名称 盛弘、新威等
     */
    private String equName;

    /**
     * 查询参数
     */
    private String queryParam;


}
