package eve.sys.limsModular.folder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.context.login.LoginContextHolder;
import eve.core.pojo.login.SysLoginUser;
import eve.core.pojo.page.PageResult;
import eve.sys.limsModular.coreRoleUser.entity.TCoreRoleUser;
import eve.sys.limsModular.coreRoleUser.service.ITCoreRoleUserService;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.mapper.TLimsFolderMapper;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.roleDimension.entity.TLimsRoleDimension;
import eve.sys.limsModular.roleDimension.service.ITLimsRoleDimensionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 委托单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class TLimsFolderServiceImpl extends ServiceImpl<TLimsFolderMapper, TLimsFolder> implements ITLimsFolderService {

    @Resource
    private ITLimsRoleDimensionService roleDimensionService;

    @Resource
    private ITCoreRoleUserService roleUserService;

    @Override
    public PageResult<TLimsFolder> listPage(TLimsFolder param) {
        LambdaQueryWrapper<TLimsFolder> queryWrapper = new LambdaQueryWrapper<>();
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();

        //权限过滤
        if(!"superAdmin".equals(sysLoginUser.getAccount()) && !"029026".equals(sysLoginUser.getAccount()) && !"059288".equals(sysLoginUser.getAccount()) &&
                !"071716".equals(sysLoginUser.getAccount()) && !"032745".equals(sysLoginUser.getAccount()) ){
            //查询角色
            LambdaQueryWrapper<TCoreRoleUser> roleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            roleUserLambdaQueryWrapper.eq(TCoreRoleUser::getUserid,sysLoginUser.getAccount());
            List<TCoreRoleUser> roleUserList = this.roleUserService.list(roleUserLambdaQueryWrapper);

            //是否有委托单查询权限
            Boolean haveQuery = false;
            List<TCoreRoleUser> adminList = roleUserList.stream().filter(r -> r.getRoleid() == 1).collect(Collectors.toList());
            if(adminList.size() > 0){
                haveQuery = true;
            }

           if(roleUserList.size() > 0) {
                List<Long> roleIds = roleUserList.stream().map(TCoreRoleUser::getRoleid).collect(Collectors.toList());
                LambdaQueryWrapper<TLimsRoleDimension> dimensionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                dimensionLambdaQueryWrapper.in(TLimsRoleDimension::getRoleid, roleIds);
                dimensionLambdaQueryWrapper.eq(TLimsRoleDimension::getMenuid, 20210825002L);
                List<TLimsRoleDimension> dimensionList = this.roleDimensionService.list(dimensionLambdaQueryWrapper);
                //角色过滤
                if (dimensionList.size() > 0) {
                    List<String> dimensionTypes = dimensionList.stream().map(TLimsRoleDimension::getDimensiontype).distinct().collect(Collectors.toList());
                    queryWrapper.and((wrapper) -> {
                        wrapper.exists("SELECT 1 FROM T_LIMS_CIRCULATE C WHERE T_LIMS_FOLDER.ID = C.FOLDERID AND C.CIRCULATEID = '" + sysLoginUser.getAccount() + "'")
                                .or().exists("SELECT 1 FROM T_LIMS_USERINFO_LEADER D WHERE D.USERACCOUNT = T_LIMS_FOLDER.CREATEDBYID AND D.LEADERACCOUNT = '" + sysLoginUser.getAccount() + "'")
                                .or().eq(TLimsFolder::getCreatedbyid, sysLoginUser.getAccount());
                        dimensionTypes.forEach(d -> {
                            if ("clientLeaderRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_USER_UNIT_AUTHORITY UA WHERE UA.USERID = '" + sysLoginUser.getAccount() + "'AND UA.ORGID = T_LIMS_FOLDER.CREATEDBYORGID");
                            }
                            if ("labUserRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_USER_UNIT_AUTHORITY UA WHERE UA.USERID = '" + sysLoginUser.getAccount() + "'AND UA.ORGID = T_LIMS_FOLDER.LABORATORYID");
                            }
                            if ("testerRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_ORDTASK O WHERE T_LIMS_FOLDER.ID = O.FOLDERID AND O.TESTERCODE  = '" + sysLoginUser.getAccount() + "'");
                            }
                            if ("productManagerRange".equals(d)) {
                                wrapper.or().eq(TLimsFolder::getProjectleaderid, sysLoginUser.getAccount());
                            }

                        });
                    });
                }else{
                    //只看自己
                    if(!haveQuery){
                        queryWrapper.and((wrapper) -> {
                            wrapper.exists("SELECT 1 FROM T_LIMS_CIRCULATE C WHERE T_LIMS_FOLDER.ID = C.FOLDERID AND C.CIRCULATEID = '" + sysLoginUser.getAccount() + "'")
                                    .or().exists("SELECT 1 FROM T_LIMS_USERINFO_LEADER D WHERE D.USERACCOUNT = T_LIMS_FOLDER.CREATEDBYID AND D.LEADERACCOUNT = '" + sysLoginUser.getAccount() + "'")
                                    .or().eq(TLimsFolder::getCreatedbyid, sysLoginUser.getAccount());
                        });
                    }
                }
            }
        }

        if (StrUtil.isNotBlank(param.getFolderno())) {
            queryWrapper.like(TLimsFolder::getFolderno,param.getFolderno());

        }
        if (StrUtil.isNotBlank(param.getCreatedbyname())) {
            queryWrapper.like(TLimsFolder::getCreatedbyname,param.getCreatedbyname());

        }
        if (StrUtil.isNotBlank(param.getTheme())) {
            queryWrapper.like(TLimsFolder::getTheme,param.getTheme());

        }
        if (StrUtil.isNotBlank(param.getProducttype())) {
            queryWrapper.like(TLimsFolder::getProducttype,param.getProducttype());

        }
        if (StrUtil.isNotBlank(param.getTestproducttype())) {
            queryWrapper.like(TLimsFolder::getTestproducttype,param.getTestproducttype());

        }

        if (StrUtil.isNotBlank(param.getLaboratory())) {
            queryWrapper.like(TLimsFolder::getLaboratory,param.getLaboratory());
        }

        queryWrapper.orderByDesc(TLimsFolder::getFolderno);
        queryWrapper.ne(TLimsFolder::getStatus, "Draft");

        return new PageResult(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));
    }

    @Override
    public List<TLimsFolder> getByFolderNo(TLimsFolder param) {
        LambdaQueryWrapper<TLimsFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsFolder::getFolderno, param.getFolderno());
        return this.list(queryWrapper);
    }
}
