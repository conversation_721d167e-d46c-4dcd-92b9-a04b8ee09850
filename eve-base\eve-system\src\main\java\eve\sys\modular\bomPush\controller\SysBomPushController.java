package eve.sys.modular.bomPush.controller;


import eve.core.annotion.BusinessLog;
import eve.core.context.login.LoginContextHolder;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bomPush.param.BomPushQueryParam;
import eve.sys.modular.bomPush.param.BomPushQueryParam2;
import eve.sys.modular.bomPush.service.ISysBomPushService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * bom推送状态 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@RestController
@RequestMapping("/sysBomPush")
public class SysBomPushController {

    @Resource
    private ISysBomPushService bomPushService;

    @PostMapping("/query")
    @BusinessLog(title = "BOM-查询BOM审核记录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData query(@RequestBody BomPushQueryParam2 param) {
        return new SuccessResponseData(bomPushService.query(param));
    }

    @GetMapping("/list")
    @BusinessLog(title = "产品管理-文档审核列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(BomPushQueryParam param) {
        return new SuccessResponseData(bomPushService.list(param.getBomId(),param.getIsOut()? LoginContextHolder.me().getSysLoginUser().getName():null
                , param.getKeyword(),param.getType()));
    }

    @PostMapping("/queryByBomId")
    public ResponseData queryByBomId(@RequestBody BomPushQueryParam param) {
        return new SuccessResponseData(bomPushService.queryByBomId(param));
    }
}

