package eve.sys.modular.ai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import eve.sys.modular.aiQuestion.entity.AiQuestion;
import eve.sys.modular.aiQuestion.service.IAiQuestionService;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ChatApiService {

    private static final String API_URL = "http://172.30.1.100:11434/api/chat";

    @Resource
    private IAiQuestionService aiQuestionService;

    public String askQuestion(String question){
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> message = new HashMap<>();
        List<Map<String, Object>> messages = new ArrayList<>();
        message.put("role","user");
        message.put("content",question);
        messages.add(message);

        params.put("model","qwen2:7b");
        params.put("stream",false);
        Map<String, Object> options = new HashMap<>();
        options.put("num_ctx",40960);
        params.put("options",options);
        params.put("messages",messages);

        HttpEntity<Object> entity = new HttpEntity<>(params);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(API_URL, HttpMethod.POST, entity, JSONObject.class);

        //保存问题及结果
        this.aiQuestionService.save(AiQuestion.builder().question(question).result(JSON.toJSONString(exchange.getBody())).build());

        return exchange.getBody().getJSONObject("message").getString("content")
                .replaceAll("json","")
                .replaceAll("`","")
                .replaceAll("\n","");

    }



}