 
package eve.sys.config;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.file.FileOperator;
import eve.core.file.modular.local.LocalFileOperator;
import eve.core.file.modular.local.prop.LocalFileProperties;

/**
 * 文件存储的配置
 * <p>
 * 默认激活本地文件存储
 *
 * <AUTHOR>
 * @date 2020/6/6 22:27
 */
@Configuration
public class FileConfig {

    /**
     * 默认文件存储的位置
     */
    public static final String DEFAULT_BUCKET = "defaultBucket";

    public static final String BOM_BUCKET_PDF = "bomPDF";

    public static final String BOM_TMP_BUCKET = "bomTmp";

    public static final String BATTERY_DESIGN_MODEL = "batteryDesignModel";
    public static final String REPORT_MODEL = "reportModel";
    public static final String TEST_MANAGER_MODEL = "testManagerModel";

    public static final String BATTERY_DESIGN_FILE = "batteryDesignFile";

    public static final String G26_FILE = "g26File";
    public static final String G26_MODEL = "g26Model";


    public static final String REPORT_FILE = "reportFile";

    public static final String BATTERY_DESIGN_BOM = "batteryDesignBomFile";

    public static final String TEST_DATA_EXPORT = "testDataExport";

    public static final String TEST_DATA_EXPORT_JM = "testDataExportJM";

    public static final String TEST_DATA_EXPORT_JM_UPLOAD = "testDataExportJMUpload";

    public static final String TEST_MANAGER_CENTER = "testManagerCenter";

    public static final String BATTERY_DESIGN_MI = "batteryDesignMI";

    public static final String BOM_TMP_MODEL = "model";

    public static final String BOM_IMG = "bomImg";

    public static final String BOM_JIRA = "jira";

    public static final String MI_VERSION_LIB = "miVersionLib";

    public static final String MI_VERSION_LIB_TEMP = "miVersionLibTemp";

    public static final String CALENDAR_LIFE_MODEL = "calendarLifeModel";

    public static final String JIRA_PREVIEW = "jira_preview";

    public static final String QUALITY_DATA_BARCODE_MODEL = "qualityDataBarcodeModel";

    public static final String QUALITY_DATA_BARCODE_FILE = "qualityDataBarcodeFile";

    public static final String FA_REPORT_TMP = "faReportTmp";
    public static final String FAILURE_CELL_OCV = "failureCellOcv";
    public static final String FA_BREAK_REPORT = "faBreakReport";
    public static final String FA_ANALYSIS_REPORT = "faAnalysisReport";
    public static final String TEST_FAILURE_NOTICE_FILE = "testFailureNoticeFile";

    public static final String SHENGHONG_MF4 = "shengHongMf4";



    public static final String DESIGN_SOR = "designSor";

    public static final String TEST_PROJECT_STRUCTURE_TEMPLATE = "testProjectStructureTemplate";

    public static final String QUALITY_LESSONS_LEARNED_FILE = "lessonsLearnedFile";

    /**
     * 本地文件操作客户端
     *
     * <AUTHOR>
     * @date 2020/6/9 21:39
     */
    @Bean
    public FileOperator fileOperator() {
        LocalFileProperties localFileProperties = new LocalFileProperties();
        String fileUploadPathForWindows = ConstantContextHolder.getDefaultFileUploadPathForWindows();
        if (ObjectUtil.isNotEmpty(fileUploadPathForWindows)) {
            localFileProperties.setLocalFileSavePathWin(fileUploadPathForWindows);
        }

        String fileUploadPathForLinux = ConstantContextHolder.getDefaultFileUploadPathForLinux();
        if (ObjectUtil.isNotEmpty(fileUploadPathForLinux)) {
            localFileProperties.setLocalFileSavePathLinux(fileUploadPathForLinux);
        }
        return new LocalFileOperator(localFileProperties);
    }

}
