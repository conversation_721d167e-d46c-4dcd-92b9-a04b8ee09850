package eve.sys.modular.bom.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReceiveJIRAParam implements Serializable {

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件编号")
    private String fileCode;

    @ApiModelProperty("文件版本")
    private String fileVersion;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("样品阶段")
    private String sampleStage;

    @ApiModelProperty("流水号")
    private String serialNumber;

    @ApiModelProperty("问题关键字")
    private String issueKey;

    @ApiModelProperty("问题ID")
    private String issueId;

    @ApiModelProperty("父问题关键字")
    private String parentIssueKey;

    @ApiModelProperty("父问题ID")
    private String parentIssueId;

    @ApiModelProperty("物料代码")
    private String materialCode;

    @ApiModelProperty("BOMID")
    private String bomId;

    @ApiModelProperty("文件链接")
    private String fileLink;

    @ApiModelProperty("approve reject")
    private String processStatus;

    @ApiModelProperty("审核人id")
    private String reviewerId;

    @ApiModelProperty("审核人")
    private String reviewer;

    @ApiModelProperty("批准人id")
    private String approverId;

    @ApiModelProperty("批准人")
    private String approver;

    @ApiModelProperty("审核时间")
    private String reviewerDate;

    @ApiModelProperty("受控时间")
    private String controlledDate;

    @ApiModelProperty("工厂文控id")
    private String factoryFileControlId;

}
