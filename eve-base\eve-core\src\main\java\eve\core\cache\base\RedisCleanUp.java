package eve.core.cache.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class RedisCleanUp implements CommandLineRunner {

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public void run(String... args) throws Exception {
        redisTemplate.getConnectionFactory().getConnection().flushAll();
        log.info("redis缓存已清空");
    }
}
