package eve.sys.modular.competitive.competitiveAnalysisFile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.sys.modular.competitive.competitiveAnalysisFile.entity.CompetitiveAnalysisFile;
import eve.sys.modular.competitive.competitiveAnalysisFile.mapper.CompetitiveAnalysisFileMapper;
import eve.sys.modular.competitive.competitiveAnalysisFile.service.ICompetitiveAnalysisFileService;
import eve.sys.modular.competitive.competitiveAnalysisSample.entity.CompetitiveAnalysisSample;
import eve.sys.modular.file.entity.SysFileInfo;
import eve.sys.modular.minio.service.MinioService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 竞品分析数据包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class CompetitiveAnalysisFileServiceImpl extends ServiceImpl<CompetitiveAnalysisFileMapper, CompetitiveAnalysisFile> implements ICompetitiveAnalysisFileService {

    @Resource
    private MinioService minioService;

    @Override
    public Boolean add(MultipartFile file, CompetitiveAnalysisFile param) throws Exception {

        SysFileInfo fileInfo = minioService.uploadForFile("competitiveanalysis", file, null, null);
        param.setFileId(fileInfo.getId());
        param.setFileName(fileInfo.getFileOriginName());
        param.setFileSizeInfo(fileInfo.getFileSizeInfo());
        param.setFileSuffix(fileInfo.getFileSuffix());

        return this.save(param);
    }

    @Override
    public Boolean remove(CompetitiveAnalysisFile param) {
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(CompetitiveAnalysisFile param) {
        return this.updateById(param);
    }

    /**
     * 查询文件夹最最里面的文件
     * @param param
     * @return
     */
    @Override
    public List<CompetitiveAnalysisFile> list(CompetitiveAnalysisFile param) {
        LambdaQueryWrapper<CompetitiveAnalysisFile> queryWrapper = new LambdaQueryWrapper<>();

        if(StrUtil.isNotBlank(param.getFileType())){
            queryWrapper.eq(CompetitiveAnalysisFile::getFileType,param.getFileType());
        }

        if(null != param.getCompetitiveId()){
            queryWrapper.eq(CompetitiveAnalysisFile::getCompetitiveId,param.getCompetitiveId());
        }

        return this.list(queryWrapper);
    }
}
