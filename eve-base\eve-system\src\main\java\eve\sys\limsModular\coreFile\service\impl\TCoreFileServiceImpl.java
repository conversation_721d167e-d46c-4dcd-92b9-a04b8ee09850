package eve.sys.limsModular.coreFile.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.sys.limsModular.coreFile.entity.TCoreFile;
import eve.sys.limsModular.coreFile.mapper.TCoreFileMapper;
import eve.sys.limsModular.coreFile.service.ITCoreFileService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@DS("b3")
@Service
public class TCoreFileServiceImpl extends ServiceImpl<TCoreFileMapper, TCoreFile> implements ITCoreFileService {

}
