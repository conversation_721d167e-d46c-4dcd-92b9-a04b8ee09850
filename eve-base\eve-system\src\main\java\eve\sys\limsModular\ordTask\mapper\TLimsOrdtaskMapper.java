package eve.sys.limsModular.ordTask.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.modular.test.testProgressDetail.entity.TestProgressDetail;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试验项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@DS("b3")
@Repository
public interface TLimsOrdtaskMapper extends BaseMapper<TLimsOrdtask> {

   List<Map> getLimsOrdtaskList(Map param);

   List<Map> getLimsOrdtaskOfPreviewList(Map param);

   List<Map> getTestPerson(Map param);

   List<String> getTestPersonNamesByOrgId(Map<String, Object> param);

   List<Map> getTestNameOrderNumber(Map param);

   List<String> getCalendarTestProjects(Map param);

   List<TestProgressDetail> getCalLifeTestContentByOrdTaskId(String ordTaskId);
}
