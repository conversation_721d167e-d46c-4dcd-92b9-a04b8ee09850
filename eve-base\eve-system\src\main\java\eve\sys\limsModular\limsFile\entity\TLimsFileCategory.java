package eve.sys.limsModular.limsFile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.*;

/**
 * <p>
 * 文件分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Getter
@Setter
@TableName("T_LIMS_FILE_CATEGORY")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TLimsFileCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 一级分类
     */
    private String firstcategory;

    /**
     * 二级分类
     */
    private String secondcategory;

    /**
     * 启用标志
     */
    private String activatedflag;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 添加人
     */
    private String createdbyname;

    /**
     * 添加时间
     */
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;

    /**
     * 启用人ID
     */
    private String activatedbyid;

    /**
     * 启用人名称
     */
    private String activatedbyname;

    /**
     * 启用时间
     */
    private Date activatedtime;


}
