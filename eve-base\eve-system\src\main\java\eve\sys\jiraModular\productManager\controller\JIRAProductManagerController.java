package eve.sys.jiraModular.productManager.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.jiraModular.productManager.entity.ProductManager;
import eve.sys.jiraModular.productManager.service.IJIRAProductManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * VIEW 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@RestController
@RequestMapping("/productManager")
public class JIRAProductManagerController {

    @Autowired
    IJIRAProductManagerService ijiraProductManagerService;

    @PostMapping("/list")
    @BusinessLog(title = "JIRA视图_产品列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(@RequestBody ProductManager param) {
        return new SuccessResponseData(ijiraProductManagerService.list());
    }
}

