package eve.sys.modular.bookcorner.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import eve.core.pojo.base.entity.BaseVo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 图书角
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Builder(toBuilder = true)
@TableName("BOOK_CORNER")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BookCorner extends BaseVo {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(exist = false)
    private List<Long> bookCateIds;

    /**
     * 分类id
     */
    private Long bookCateId;

    /**
     * 书名
     */
    private String bookName;

    private String author;

    private Long nums;


    private String brief;

    private String remark;

    /**
     * 存放位置
     */
    private String storePos;

    private String bookFile;

    private String bookImg;

}
