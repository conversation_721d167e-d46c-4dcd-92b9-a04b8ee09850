package eve.sys.modular.bomline.service;

import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.param.SysBomLineParam;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
public interface ISysBomLineService extends IService<SysBomLine> {
    List<SysBomLine> getList(SysBomLineParam param);
    JSONObject getFilterList(SysBomLineParam param);
    void deleteWerks(SysBomLineParam param);
    void addWerks(SysBomLineParam param);
    void deleteByLineIds(SysBomLineParam param,List<Long> delIds);
    void deleteByBomId(SysBomLineParam param);
    void setWerks(SysBomLineParam param);
    void addMultiWerks(SysBomLineParam param);
    void insertBatch(List<SysBomLine> bomLines);
}
