<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.limsModular.ordTask.mapper.TLimsOrdtaskMapper">

    <select id="getLimsOrdtaskList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT T.*
        FROM (select T.*,
                     F.REMARK FOLDERREMARK,
                     F.THEME,
                     F.TESTPRODUCTTYPE,
                     F.<PERSON>RGENTFLAG,
                     F.REPORTFLAG,
                     F.<PERSON>LEREPORTFLAG,
                     F.<PERSON>TORYID,
                     F.CREATEDBYID    WTRID,
                     F.CREATEDBYNAME  WTRNAME,
                     F.CREATEDBYORGID WTRORGID,
                     F.<PERSON>LA<PERSON>ETIM<PERSON>,
                     F<PERSON>IME,
                     F.TESTTYPE,
                     F.APPOIN<PERSON>ERSONID,
                     F.LOGI<PERSON>IC<PERSON>O,
                     F<PERSON>  WTOUTSOURCEFLAG,
                     F.<PERSON>ME,
                     F.PR<PERSON>TTYPE,
                     M.BUSINESSTYPE,
                     M.ELNTMPLID,
                     M.TESTID,
                     M.TESTCYCLE,
                     U.USERNAME AS AUDITOR,
                     (select count(1) from T_LIMS_TESTMATRIX where ORDTASKID = T.Id and CHECKSTATUS = '1') TESTNUMBER,
                     M.DESCRIPTION
              FROM T_LIMS_ORDTASK T
                       LEFT JOIN T_LIMS_FOLDER F ON T.FOLDERID = F.ID
                       LEFT JOIN T_LIMS_TEST_METHOD M ON T.TESTMETHODID = M.ID
                       LEFT JOIN T_CORE_USER U ON U.ID = M.IDEA) T
        WHERE
        <if test="status != null and status != '' and laboratoryId != null and laboratoryId != ''">
              STATUS = #{status} AND LABORATORYID = #{laboratoryId}
        </if>
        <if test="queryTestName != null and queryTestName != ''">
            AND TESTNAME like CONCAT(CONCAT('%',#{queryTestName}),'%')
        </if>
        <if test="queryAuditMan != null and queryAuditMan != ''">
            AND AUDITOR like CONCAT(CONCAT('%',#{queryAuditMan}),'%')
        </if>
        <if test="queryFolderno != null and queryFolderno != ''">
            AND FOLDERNO like CONCAT(CONCAT('%',#{queryFolderno}),'%')
        </if>
        <if test="searchAssignDateParam == 'alreadyAssign'">
            AND T.PLANSTARTTIME is not null AND T.PLANENDTIME is not null
        </if>
        <if test="searchAssignDateParam == 'alreadyAssignOfRL'">
            AND T.PLANSTARTTIME is not null
        </if>
        <if test="searchAssignDateParam == 'notAssign'">
            AND T.PLANSTARTTIME is null AND T.PLANENDTIME is null
        </if>
        <if test="testName != null and testName.size() > 0">
            AND TESTNAME in
            <foreach item="item" collection="testName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids != null and ids.size() > 0">
           ID in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY FOLDERNO desc NULLS LAST, SORTER asc NULLS LAST
    </select>

    <select id="getLimsOrdtaskOfPreviewList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT T.*
        FROM (select T.*,
        F.REMARK FOLDERREMARK,
        F.THEME,
        F.TESTPRODUCTTYPE,
        F.URGENTFLAG,
        F.REPORTFLAG,
        F.WHOLEREPORTFLAG,
        F.LABORATORYID,
        F.CREATEDBYID    WTRID,
        F.CREATEDBYNAME  WTRNAME,
        F.CREATEDBYORGID WTRORGID,
        F.PLANSAMPLETIME,
        F.OAPASSTIME,
        F.TESTTYPE,
        F.APPOINTPERSONID,
        F.LOGISTICSNO,
        F.OUTSOURCEFLAG  WTOUTSOURCEFLAG,
        F.PROJECTNAME,
        F.PRODUCTTYPE,
        M.BUSINESSTYPE,
        M.ELNTMPLID,
        M.TESTID,
        M.TESTCYCLE,
        U.USERNAME AS AUDITOR,
        (select count(1) from T_LIMS_TESTMATRIX where ORDTASKID = T.Id and CHECKSTATUS = '1') TESTNUMBER,
        M.DESCRIPTION
        FROM T_LIMS_ORDTASK T
        LEFT JOIN T_LIMS_FOLDER F ON T.FOLDERID = F.ID
        LEFT JOIN T_LIMS_TEST_METHOD M ON T.TESTMETHODID = M.ID
        LEFT JOIN T_CORE_USER U ON U.ID = M.IDEA) T
        WHERE
        <if test="status != null and status != '' and laboratoryId != null and laboratoryId != ''">
            STATUS = #{status} AND LABORATORYID = #{laboratoryId}
        </if>
        <if test="queryTestName != null and queryTestName != ''">
            AND TESTNAME like '%${queryTestName}%'
        </if>
        <if test="queryAuditMan != null and queryAuditMan != ''">
            AND AUDITOR like '%${queryAuditMan}%'
        </if>
        ORDER BY FOLDERNO desc NULLS LAST, SORTER asc NULLS LAST
    </select>

    <select id="getTestPerson" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT T.ID,T.USERNAME,T.ORGNAME,T.ORDTASKNUMBER,T.ORDERNUMBER,T.FOLDERNUMBER
        FROM (SELECT U.*,
        (SELECT COUNT(1)
        FROM T_LIMS_ORDTASK K
        WHERE K.STATUS IN ('Result', 'Result_return') AND (K.TESTERCODE = U.ID OR K.PARTICIPATORCODE LIKE CONCAT(CONCAT('%',U.ID),'%'))) AS ORDTASKNUMBER,
        (SELECT COUNT(DISTINCT M.ORDERID)
        FROM T_LIMS_TESTMATRIX M
        LEFT JOIN T_LIMS_ORDTASK K ON M.ORDTASKID = K.ID
        WHERE K.STATUS IN ('Result', 'Result_return')
        AND (K.TESTERCODE = U.ID OR K.PARTICIPATORCODE LIKE CONCAT(CONCAT('%',U.ID),'%'))
        AND M.CHECKSTATUS = '1') AS ORDERNUMBER,
        (SELECT COUNT(1)
        FROM T_LIMS_FOLDER F
        WHERE F.STATUS = 'Testing'
        AND EXISTS (SELECT 1
        FROM T_LIMS_ORDTASK K
        WHERE K.FOLDERID = F.ID AND (K.TESTERCODE = U.ID OR K.PARTICIPATORCODE LIKE CONCAT(CONCAT('%',U.ID),'%')))) AS FOLDERNUMBER
        FROM T_CORE_USER U) T
        WHERE STATUS &lt;&gt; #{status}
        AND ORGID = #{orgId}
        AND EXISTS (SELECT 1 FROM T_CORE_ROLE_USER R WHERE R.ROLEID = #{roleId} AND R.USERID = T.ID)
        ORDER BY T.ORGID, T.ID, T.ORDTASKNUMBER DESC, T.ORDERNUMBER DESC, T.FOLDERNUMBER DESC
    </select>

    <select id="getTestPersonNamesByOrgId" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT T.USERNAME
        FROM (SELECT U.* FROM T_CORE_USER U) T
        WHERE STATUS &lt;&gt; 'deprecated'
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND ORGID in
            <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">
            AND ID = #{userId}
        </if>
        AND EXISTS (SELECT 1 FROM T_CORE_ROLE_USER R WHERE R.ROLEID = '699586598579968' AND R.USERID = T.ID)
        ORDER BY T.ORGID, T.ID
    </select>

    <select id="getTestNameOrderNumber" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT K.TESTNAME as testName,COUNT(Distinct M.ORDERID) AS testSampleCount
        FROM T_LIMS_TESTMATRIX M
                 LEFT JOIN T_LIMS_ORDTASK K ON M.ORDTASKID = K.ID
        WHERE K.STATUS IN ('Result', 'Result_return')
          AND (K.TESTERCODE = #{testerCode} OR K.PARTICIPATORCODE LIKE CONCAT(CONCAT('%',#{testerCode}),'%'))
          AND M.CHECKSTATUS = '1' group by K.TESTNAME
    </select>

    <select id="getCalendarTestProjects" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT T.TESTNAME
        FROM (SELECT T.*,
                     TE.TESTNAME,
                     TE.ORGID,
                     TC.FIRSTCODE,
                     TC.SECONDCODE
              FROM T_LIMS_TEST_METHOD T
                       LEFT JOIN T_LIMS_METHOD M ON T.METHODID = M.ID
                       LEFT JOIN T_LIMS_TEST TE ON TE.ID = T.TESTID
                       LEFT JOIN T_LIMS_TEST_CATEGORY TC ON TC.ID = TE.TESTCATEGORYID) T
        WHERE
            ("SECONDCODE" = 'STORAGE')
          AND ("ORGID" = #{laboratoryId})
          AND T.FIRSTCODE NOT IN ('WWCS', 'WWRZ')
        ORDER BY T.ID DESC
    </select>

    <select id="getCalLifeTestContentByOrdTaskId" resultType="eve.sys.modular.test.testProgressDetail.entity.TestProgressDetail"
            parameterType="java.lang.String">
        select g.ORDERNO orderNumber,
               g.INNERRES,
               g.VOLTAGE,
               g.VOLUME,
               g.WEIGHT,
               g.ISOLATERES,
               g.HEIGHT,
               g.LARGEINSPECTION,
               g.SMALLINSPECTION
        from T_LIMS_ORDTASK t
                 left join T_LIMS_ORDTASK_CONDITION c on t.id = c.ORDTASKID
                 left join T_LIMS_TEM_GRADIENT g on c.id = g.ORDTASKCONDITIONID
        where t.id = #{ordTaskId}
          and c.DATATYPE = 'inspectionMsg'
        order by g.ORDERNO
    </select>
</mapper>
