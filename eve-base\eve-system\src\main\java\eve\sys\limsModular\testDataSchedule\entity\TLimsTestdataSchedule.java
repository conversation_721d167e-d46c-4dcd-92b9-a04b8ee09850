package eve.sys.limsModular.testDataSchedule.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.sys.mongoDbModular.shenghong.bean.FlowInfo;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 测试数据定时查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("T_LIMS_TESTDATA_SCHEDULE")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLimsTestdataSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 委托单主键
     */
    private Long folderid;

    /**
     * 委托单号
     */
    private String folderno;

    /**
     * 测试项主键
     */
    private Long ordtaskid;

    /**
     * 测试项编码
     */
    private String testcode;

    /**
     * 测试项名称
     */
    private String testname;

    /**
     * 样品主键
     */
    private Long orderid;

    /**
     * 样品编号
     */
    private String orderno;

    /**
     * 试验矩阵主键
     */
    private Long testmatrixid;

    /**
     * 测试设备编号
     */
    private String testequipcode;

    /**
     * 测试设备名称
     */
    private String testequipname;

    /**
     * 测试设备通道
     */
    private String testequipchannel;

    /**
     * 测试内容
     */
    private String testcontent;

    /**
     * 查询参数
     */
    private String queryparam;

    /**
     * 测试项集合名称
     */
    private String testdatacollectionname;

    /**
     * 工步数据集合名称
     */
    private String stepdatacollectionname;

    /**
     * 记录数据集合名称
     */
    private String detaildatacollectionname;

    /**
     * 是否开始
     */
    private String started;

    /**
     * 开始时间
     */
    private Date starttime;

    /**
     * 是否结束
     */
    private String ended;

    /**
     * 结束时间
     */
    private Date endtime;

    /**
     * 结束标识
     */
    private String endstatusflag;

    /**
     * 执行任务应用主机
     */
    private String hostname;

    /**
     * 是否间续测试
     */
    private String intervaltest;

    /**
     * 关联的已测完的ID
     */
    private String relationids;

    /**
     * 关联的上次测试id
     */
    private Long prerelationid;

    /**
     * 厂商名称
     */
    private String eptfactoryname;

    /**
     * 设备编号
     */
    private String equiptcode;

    /**
     * 通道编号
     */
    private String channelno;

    /**
     * 通道状态监控主键
     */
    private String channelmonitorid;

    /**
     * 所属实验室编号
     */
    private String laboratoryid;

    /**
     * 所属实验室
     */
    private String laboratory;

    /**
     * 创建时间
     */
    private Date createdtime;

    /**
     * 更新时间
     */
    private Date updatetime;

    @TableField(exist = false)
    private LocalDateTime startTime;

    @TableField(exist = false)
    private LocalDateTime endTime;

    /**
     * 测试项目别名
     */
    @TableField(exist = false)
    private String alias;

    /**
     * 主题
     */
    @TableField(exist = false)
    private String theme;

    @TableField(exist = false)
    private List<FlowInfo> flowInfoList;

    @TableField(exist = false)
    private List<TLimsTestdataSchedule> children;

    @TableField(exist = false)
    private String flowId;

    @TableField(exist = false)
    private Boolean isChild = false;

    /**
     * 测试编码
     */
    private String celltestcode;

    /**
     * 是否已发送下柜提醒 1 已发送
     */
    @TableField(exist = false)
    private Integer isSent = 0 ;

    @TableField(exist = false)
    private Integer pageNo = 1;

    @TableField(exist = false)
    private Integer pageSize = 20;

    @TableField(exist = false)
    private String keyword;

    @TableField(exist = false)
    private String uuid;

    @TableField(exist = false)
    private String dataPath;

    /**
     * sorDcr报告工步1
     */
    @TableField(exist = false)
    private Integer socDcrStepId1 ;
    /**
     * sorDcr报告工步2
     */
    @TableField(exist = false)
    private Integer socDcrStepId2 ;

    /**
     * 温度
     */
    @TableField(exist = false)
    public String tem;



    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
