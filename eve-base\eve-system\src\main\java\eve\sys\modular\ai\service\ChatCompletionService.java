package eve.sys.modular.ai.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Service
public class ChatCompletionService {

    private static final String API_URL = "http://172.30.1.100:11434/v1/chat/completions";

    public Flux<String> getCompletions(String inputText) {

        WebClient client = WebClient.create(API_URL);
        return client.post()
                .header("Content-Type", "application/json")
                .bodyValue(createRequestBody(inputText))
                .retrieve()
                .bodyToFlux(String.class)
                .map(this::extractContent)
                .onErrorResume(e -> Flux.error(e, false))
                .doOnNext(content -> System.err.println(content))
                .log();
    }

    private String createRequestBody(String inputText) {
        JSONObject data = new JSONObject()
                .put("messages", new JSONArray()
                        .put(new JSONObject().put("role", "system").put("content", ""))
                        .put(new JSONObject().put("role", "user").put("content", inputText))).put("temperature", 0.3)
                .put("top_p", 0.95)
                .put("max_tokens", 512)
                .put("model", "gemma2:27b")
                .put("stream", true)
                .put("n", 1)
                .put("best_of", 1)
                .put("presence_penalty", 1.2)
                .put("frequency_penalty", 0.2)
                .put("top_k", 50)
                .put("use_beam_search", false)
                .put("stop", new JSONArray())
                .put("ignore_eos", false)
                .put("logprobs", JSONObject.DEFAULT_CAPACITY);
                ;
        return data.toString();
    }

    private String extractContent(String line) {
        if (line.isEmpty() || line.equals("[DONE]") || !line.startsWith("data: ")) {
            return "";
        }
        line = line.substring(6).trim(); // 去除 "data: "
        try {
            JSONObject result = new JSONObject(line);
            JSONArray choices = result.getJSONArray("choices");
            JSONObject delta = choices.getJSONObject(0).getJSONObject("delta");
            return delta.getStr("content");
        } catch (Exception e) {
            return "";
        }
    }
}