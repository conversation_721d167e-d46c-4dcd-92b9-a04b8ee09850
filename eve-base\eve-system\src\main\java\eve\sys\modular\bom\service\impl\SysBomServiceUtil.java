package eve.sys.modular.bom.service.impl;

import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.node.params.NodeItem;
import eve.sys.modular.node.service.ISysNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class SysBomServiceUtil  {

    public static SysBomServiceUtil sysBomServiceUtil;

    @PostConstruct
    public void init(){
        sysBomServiceUtil = this;
        sysBomServiceUtil.nodeService = this.nodeService;
    }

    @Autowired
    private ISysNodeService nodeService;


    public SysBomServiceUtil() {
        super();
    }

    public Map<TreeBom, List<TreeBom>> loopTreeBom1(List<TreeBom> treeBoms) {

        List<TreeBom> queqeBoms = new ArrayList<>();

        Map<TreeBom, List<TreeBom>> map = new LinkedHashMap<>();

        List<NodeItem> allNodeList = sysBomServiceUtil.nodeService.getAll();


        for (TreeBom item : treeBoms) {

            queqeBoms.add(item);

            //Integer i = 0;
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                Optional<NodeItem> first = allNodeList.stream().filter(e -> e.getNodeId().equals(tempItem.getPartClass())).findFirst();
                tempItem.setPartName(first.isPresent()?first.get().getNodeName():tempItem.getPartName());
                if (!tempItem.isLeaf()) {
                    queqeBoms.addAll(tempItem.getLists());
                    map.put(tempItem,tempItem.getLists());
                }
            }
        }

        return map;
    }

    public void putLevel(List<TreeBom> treeBoms){

        for (TreeBom item : treeBoms) {
            item.setLevel(item.getLevel() == null?1000:item.getLevel() - 1);
            if(null != item.getLists() || item.getLists().size() != 0){
                List<TreeBom> lists = item.getLists();
                for (int i = 0; i < lists.size(); i++) {
                    lists.get(i).setLevel(item.getLevel() - 1);
                }
                putLevel(lists);
            }
        }

    }



   
}
