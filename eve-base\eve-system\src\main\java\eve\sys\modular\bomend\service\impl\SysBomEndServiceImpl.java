package eve.sys.modular.bomend.service.impl;

import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.BomChangeParam;
import eve.sys.modular.bom.params.OaAddOrEditParam;
import eve.sys.modular.bom.params.ReceiveNameParam;
import eve.sys.modular.bom.params.SapAddBomItOrEtParam;
import eve.sys.modular.bom.params.SapAddBomParam;
import eve.sys.modular.bom.params.SapAddBomRespParam;
import eve.sys.modular.bom.params.SapApiParam;
import eve.sys.modular.bom.params.SapEditBomParam;
import eve.sys.modular.bom.params.SapEditBomRespParam;
import eve.sys.modular.bom.params.SapWerks;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bom.service.impl.SysBomChangeUtil;
import eve.sys.modular.bom.service.impl.SysBomEndChangeUtil;
import eve.sys.modular.bom.service.impl.SysBomSapServiceUtil;
import eve.sys.modular.bom.utils.BomUtils;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomend.mapper.SysBomEndMapper;
import eve.sys.modular.bomend.service.ISysBomEndService;
import eve.sys.modular.bomerror.entity.SysBomError;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.param.SysBomHistoryParam;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;
import eve.sys.modular.consts.entity.SysConfig;
import eve.sys.modular.consts.param.SysConfigParam;
import eve.sys.modular.consts.service.SysConfigService;
import eve.sys.modular.part.entity.SysPart;
import eve.sys.modular.part.service.ISysPartService;
import eve.sys.modular.part.utils.GenerateUniqueIdUtil;
import eve.sys.modular.product.param.ProjectDetail;
import eve.sys.modular.product.param.request.DocParams;
import eve.sys.modular.product.service.ProductJiraService;
import eve.sys.modular.werkline.entity.SysWerkLine;
import eve.sys.modular.werkline.service.ISysWerkLineService;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Slf4j
@Service
public class SysBomEndServiceImpl extends ServiceImpl<SysBomEndMapper, SysBomEnd> implements ISysBomEndService {

    @Resource
    private ISysBomService sysBomService;

    @Resource
    private ISysPartService partService;

    @Resource
    private ISysWerkLineService werkLineService;

    @Resource
    private ISysBomLineService bomLineService;

    @Resource
    private ProductJiraService productJiraService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Override
    public Long saveBomEnd(SysBomEnd param) {

        List<SysBomLine> bomLines = bomLineService.getList(SysBomLineParam.builder().bomId(param.getBomId()).build());
        List<SysBomLine> bomPackLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(param.getBomPackId()).build());

        List<Long> bomLineIds = bomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());
        List<Long> bomPackLineIds = bomPackLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        // 判断是否有交集
        bomLineIds.retainAll(bomPackLineIds);
        if (null == bomLineIds || bomLineIds.isEmpty()) {
            throw new ServiceException(500, "包装BOM产线与电芯BOM产线不匹配,无法关联");
        }

        if (!StrUtil.isEmpty(param.getBomCode())) {

            SysBom bom = sysBomService.get(param.getBomId());
            SysBom packBom = sysBomService.get(param.getBomPackId());
            SysPart sysPart = partService.getBySapNumber(param.getBomCode());
            String parentId = GenerateUniqueIdUtil.getGuid();

            List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomId(bom.getId()).build());

            if (null == sysBomLines || sysBomLines.isEmpty()) {
                throw new ServiceException(500, "产线未选");
            }

            TreeBom orgTreeBom = TreeBom.builder()
                    .id(parentId)
                    .open(true)
                    .partName(sysPart.getPartName())
                    .partDescription(sysPart.getPartDescription())
                    .partUnit(sysPart.getPartUnit())
                    .partClass(sysPart.getPartClass())
                    .sapNumber(sysPart.getSapNumber())
                    .partUse(1000)
                    .sapPartUse(1000)
                    .partLoss(0)
                    .partNumber(sysPart.getPartNumber())
                    .partGroup("")
                    .posnr("")
                    .desc("")
                    .version("")
                    .substitute(new ArrayList<>())
                    .validate(true)
                    .lists(new ArrayList<>()).build();

            List<TreeBom> _treeBoms = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
            List<TreeBom> _treePackBoms = JSONObject.parseArray(packBom.getBomData(), TreeBom.class);

            _treeBoms.get(0).setParent_id(parentId);
            _treeBoms.get(0).setLists(null);
            orgTreeBom.getLists().add(_treeBoms.get(0));

            _treePackBoms.get(0).setLists(null);
            _treePackBoms.get(0).setParent_id(parentId);
            orgTreeBom.getLists().add(_treePackBoms.get(0));

            List<TreeBom> treeBoms = new ArrayList<TreeBom>(1) {
                {
                    add(orgTreeBom);
                }
            };

            param.setBomData(JSONObject.toJSONString(treeBoms));
        }

        param.setAddFails("[]");
        param.setEditFails("[]");
        param.setEdits("[]");
        param.setAdds("[]");

        if (null == param.getId()) {
            this.save(param);
            return param.getId();
        }
        this.updateById(param);
        return param.getId();
    }

    @Override
    public Long editBomEnd(SysBomEnd param) {

        SysBomEnd bomEnd = this.getOne(Wrappers.<SysBomEnd>lambdaQuery().eq(SysBomEnd::getId, param.getId()));

        if (param.getBomId().equals(bomEnd.getBomId()) && param.getBomPackId().equals(bomEnd.getBomPackId())) {
            return 1L;
        }

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bomEnd.getId());
        if (null != sysBomHistory && bomEnd.getBomRelateStatus() == 0
                && param.getBomId().equals(sysBomHistory.getCellBomId())
                && param.getBomPackId().equals(sysBomHistory.getPackBomId())) {
            throw new ServiceException(500, "选择的BOM与上次变更的版本一致，禁止修订");
        }

        List<Long> bomLines = bomLineService.getList(SysBomLineParam.builder().bomId(bomEnd.getBomId()).build())
                .stream().map(SysBomLine::getLineId).collect(Collectors.toList());
        List<Long> bomPackLines = bomLineService.getList(SysBomLineParam.builder().bomId(bomEnd.getBomPackId()).build())
                .stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        List<Long> cellBomLines = bomLineService.getList(SysBomLineParam.builder().bomId(param.getBomId()).build())
                .stream().map(SysBomLine::getLineId).collect(Collectors.toList());
        List<Long> packBomLines = bomLineService.getList(SysBomLineParam.builder().bomId(param.getBomPackId()).build())
                .stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        // 判断是否有交集
        bomLines.retainAll(cellBomLines);
        if (null == bomLines || bomLines.isEmpty()) {
            throw new ServiceException(500, "更改电芯BOM产线不匹配,无法关联");
        }

        bomPackLines.retainAll(packBomLines);
        if (null == bomPackLines || bomPackLines.isEmpty()) {
            throw new ServiceException(500, "更改包装BOM产线不匹配,无法关联");
        }

        if (!StrUtil.isEmpty(bomEnd.getBomCode())) {

            SysBom bom = sysBomService.get(param.getBomId());
            SysBom packBom = sysBomService.get(param.getBomPackId());

            List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomId(bom.getId()).build());

            if (null == sysBomLines || sysBomLines.isEmpty()) {
                throw new ServiceException(500, "电芯BOM产线未选");
            }

            List<TreeBom> orgTreeBom = JSONObject.parseArray(bomEnd.getBomData(), TreeBom.class);

            List<TreeBom> _treeBoms = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
            List<TreeBom> _treePackBoms = JSONObject.parseArray(packBom.getBomData(), TreeBom.class);

            List<TreeBom> _tempBoms = orgTreeBom.get(0).getLists();

            orgTreeBom.get(0).setLists(new ArrayList<>());

            _treeBoms.get(0).setParent_id(orgTreeBom.get(0).getId());
            _treeBoms.get(0).setLists(null);
            if (bomEnd.getBomId().equals(param.getBomId())) {
                String posnr = _tempBoms.stream().filter(e -> e.getId().equals(_treeBoms.get(0).getId()))
                        .map(TreeBom::getPosnr).findFirst().orElse("");
                _treeBoms.get(0).setPosnr(posnr);
            }
            orgTreeBom.get(0).getLists().add(_treeBoms.get(0));

            _treePackBoms.get(0).setLists(null);
            _treePackBoms.get(0).setParent_id(orgTreeBom.get(0).getId());
            if (bomEnd.getBomPackId().equals(param.getBomPackId())) {
                String posnr = _tempBoms.stream().filter(e -> e.getId().equals(_treePackBoms.get(0).getId()))
                        .map(TreeBom::getPosnr).findFirst().orElse("");
                _treePackBoms.get(0).setPosnr(posnr);
            }
            orgTreeBom.get(0).getLists().add(_treePackBoms.get(0));

            bomEnd.setBomData(JSONObject.toJSONString(orgTreeBom));
        }

        bomEnd.setAddFails("[]");
        bomEnd.setEditFails("[]");
        bomEnd.setEdits("[]");
        bomEnd.setAdds("[]");
        bomEnd.setBomRelateStatus(0);
        bomEnd.setBomPackId(param.getBomPackId());
        bomEnd.setBomId(param.getBomId());

        this.updateById(bomEnd);
        return bomEnd.getId();
    }

    @Override
    public void delBomLines(SysBomEnd param) {

        SysBomEnd bomEnd = this.getById(param.getId());

        String bomAddWerks = param.getBomAddWerks();
        List<SysWerkLine> sysWerkLines = JSONObject.parseArray(bomAddWerks, SysWerkLine.class);

        List<Long> _bomLineIds = JSONObject.parseArray(param.getBomLines(), Long.class);

        List<Long> bomEndLineIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);

        if (null == _bomLineIds || _bomLineIds.isEmpty()) {
            throw new ServiceException(500, "请选择产线");
        }

        int originCount = bomEndLineIds.size();
        int delCount = _bomLineIds.size();

        bomEndLineIds.removeAll(_bomLineIds);

        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.put(Number.class, ToStringSerializer.instance);
        serializeConfig.put(Long.class, ToStringSerializer.instance);
        serializeConfig.put(Long.TYPE, ToStringSerializer.instance);
        serializeConfig.put(BigDecimal.class, ToStringSerializer.instance);

        bomEnd.setBomLines(
                null == bomEndLineIds || bomEndLineIds.isEmpty() ? "[]"
                        : JSONObject.toJSONString(bomEndLineIds, serializeConfig, SerializerFeature.PrettyFormat));

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bomEnd.getId());

        if (null == sysBomHistory) {
            this.updateById(bomEnd);
            return;
        }

        if (originCount == delCount) {
            throw new ServiceException(500, "请至少保留一个产线");
        }

        // 删除的产线
        List<SysWerkLine> _lines = werkLineService
                .getWerks(_bomLineIds);

        // 保存的产线
        List<String> werkNos = null == bomEndLineIds || bomEndLineIds.isEmpty() ? new ArrayList<>()
                : werkLineService
                        .getWerks(bomEndLineIds)
                        .stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        _lines.forEach(e -> {
            e.setFlag(werkNos.indexOf(e.getWerkNo()) > -1 ? 0 : 1);
        });

        bomEnd.setBomAddWerks(JSONObject.toJSONString(_lines));
        bomEnd.setBomIfAdd(2);
        bomEnd.setBomRelateStatus(6);
        this.updateById(bomEnd);
        // this.sysBomService.endBomOaAdd(bomEnd.getId(), sysWerkLines);

    }

    @Override
    public void addBomLines(SysBomEnd param) {

        SysBomEnd bomEnd = this.getById(param.getId());

        List<SysBomLine> bomLines = bomLineService.getList(SysBomLineParam.builder().bomId(bomEnd.getBomId()).build());
        List<Long> bomLineIds = bomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        List<Long> _bomLineIds = JSONObject.parseArray(param.getBomLines(), Long.class);

        if (null == _bomLineIds || _bomLineIds.isEmpty()) {
            throw new ServiceException(500, "请选择产线");
        }

        _bomLineIds.removeAll(bomLineIds);

        if (!_bomLineIds.isEmpty()) {
            throw new ServiceException(500, "所选的产线与电芯BOM产线不匹配,操作失败");
        }

        List<SysBomLine> bomPackLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(bomEnd.getBomPackId()).build());
        List<Long> bomPackLineIds = bomPackLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        List<Long> _bomPackLineIds = JSONObject.parseArray(param.getBomLines(), Long.class);

        _bomPackLineIds.removeAll(bomPackLineIds);

        if (!_bomPackLineIds.isEmpty()) {
            throw new ServiceException(500, "所选的产线与包装BOM产线不匹配,操作失败");
        }

        // 获取原来设置的bom的产线
        List<Long> bomEndLineIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);
        List<Long> paramIds = JSONObject.parseArray(param.getBomLines(), Long.class);

        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.put(Number.class, ToStringSerializer.instance);
        serializeConfig.put(Long.class, ToStringSerializer.instance);
        serializeConfig.put(Long.TYPE, ToStringSerializer.instance);
        serializeConfig.put(BigDecimal.class, ToStringSerializer.instance);

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bomEnd.getId());

        if (null == sysBomHistory) {
            bomEndLineIds.addAll(paramIds);
            bomEnd.setBomLines(JSONObject.toJSONString(bomEndLineIds, serializeConfig, SerializerFeature.PrettyFormat));
            this.updateById(bomEnd);
            return;
        }

        List<SysWerkLine> _lines = werkLineService
                .getWerks(paramIds);

        List<String> werkNos = null == bomEndLineIds || bomEndLineIds.isEmpty() ? new ArrayList<>()
                : werkLineService
                        .getWerks(bomEndLineIds)
                        .stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        _lines.forEach(e -> {
            e.setFlag(werkNos.indexOf(e.getWerkNo()) > -1 ? 0 : 1);
        });

        bomEndLineIds.addAll(paramIds);
        bomEnd.setBomAddWerks(JSONObject.toJSONString(_lines));
        bomEnd.setBomIfAdd(1);
        bomEnd.setBomRelateStatus(5);
        bomEnd.setBomLines(JSONObject.toJSONString(bomEndLineIds, serializeConfig, SerializerFeature.PrettyFormat));
        this.updateById(bomEnd);
        // this.sysBomService.endBomOaAdd(bomEnd.getId(), null);
    }

    @Override
    public void delBomEnd(SysBomEnd param) {
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(param.getId());

        if (null != sysBomHistory) {
            throw new ServiceException(500, "BOM存在变更记录,不可删除");
        }

        this.baseMapper.deleteById(param.getId());
    }

    @Override
    public JSONObject getBomPackLineIds(SysBomEnd param) {

        SysBomEnd bomEnd = this.getById(param.getId());

        List<SysBomLine> bomLines = bomLineService.getList(SysBomLineParam.builder().bomId(bomEnd.getBomId()).build());
        List<Long> bomLineIds = bomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        List<SysBomLine> bomPackLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(bomEnd.getBomPackId()).build());
        List<Long> bomPackLineIds = bomPackLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        List<Long> paramIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);

        bomLineIds.removeAll(paramIds);

        JSONObject rep = new JSONObject();

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bomEnd.getId());

        SysBomHistory bomHistory = bomHistoryService.getLastHistory(bomEnd.getBomId());

        SysBomHistory packBomHistory = bomHistoryService.getLastHistory(bomEnd.getBomPackId());

        String[] bomNoArr = null != packBomHistory ? packBomHistory.getBomNo().split("-") : new String[] { "" };

        rep.put("bomlines", null == bomLineIds ? new ArrayList<>() : bomLineIds);
        rep.put("packlines", null == bomPackLineIds ? new ArrayList<>() : bomPackLineIds);
        rep.put("lineflag", null == sysBomHistory ? 0 : 1);
        rep.put("endBomVersoin", null == bomHistory ? "" : bomHistory.getBomNo() + "-" + bomNoArr[bomNoArr.length - 1]);

        return rep;
    }

    @Override
    public JSONObject getBomLineIds(SysBomEnd param) {

        SysBomEnd bomEnd = this.getById(param.getId());
        List<Long> bomLineIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);// bomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList());

        JSONObject rep = new JSONObject();
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bomEnd.getId());

        SysBomHistory bomHistory = bomHistoryService.getLastHistory(bomEnd.getBomId());

        SysBomHistory packBomHistory = bomHistoryService.getLastHistory(bomEnd.getBomPackId());

        String[] bomNoArr = null != packBomHistory ? packBomHistory.getBomNo().split("-") : new String[] { "" };

        rep.put("bomlines", null == bomLineIds ? new ArrayList<>() : bomLineIds);
        rep.put("lineflag", null == sysBomHistory ? 0 : 1);
        rep.put("endBomVersoin", null == bomHistory ? "" : bomHistory.getBomNo() + "-" + bomNoArr[bomNoArr.length - 1]);
        return rep;
    }

    @Override
    public List<SysBomEnd> getBomPacks(List<Long> bomIds) {
        LambdaQueryWrapper<SysBomEnd> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(SysBomEnd::getBomId, SysBomEnd::getBomPackId).in(SysBomEnd::getBomId, bomIds);
        List<SysBomEnd> bomPacks = this.list(lambdaQueryWrapper);
        return bomPacks;
    }

    @Override
    public PageResult<SysBom> bomPage(SysBomParam param) {
        LambdaQueryWrapper<SysBomEnd> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(param.getBomType() == 1 ? SysBomEnd::getBomId : SysBomEnd::getBomPackId)
                .eq(param.getBomType() == 1 ? SysBomEnd::getBomPackId : SysBomEnd::getBomId, param.getId());
        List<Object> bomIds = this.baseMapper.selectObjs(lambdaQueryWrapper);// .stream().map(o->(Long)o).collect(Collectors.toList());

        if (null == bomIds || bomIds.isEmpty()) {
            bomIds.add(0L);
        }

        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getBomType, param.getBomType() == 1 ? 0 : 1);
        queryWrapper.in(SysBom::getId, bomIds);

        PageResult<SysBom> sysBoms = new PageResult<SysBom>(
                sysBomService.page(new Page<SysBom>(param.getPageNo(), param.getPageSize()), queryWrapper));

        List<SysBomLine> sysBomLines = bomLineService
                .getList(SysBomLineParam.builder().bomIssueId(param.getBomIssueId()).build());

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));

        for (SysBom item : sysBoms.getRows()) {

            item.setLines(new ArrayList<>());

            if (lineMap.containsKey(item.getId())) {
                item.getLines().addAll(
                        lineMap.get(item.getId()).stream().map(SysBomLine::getLineId).collect(Collectors.toList()));
            }
        }

        return sysBoms;
    }

    @Override
    public SysBomEnd getBomEndError(SysBomEnd param) {
        return this.getById(param.getId());
    }

    @Override
    public List<SysBomEnd> getList(SysBomEnd param) {
        LambdaQueryWrapper<SysBomEnd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomEnd::getBomId, param.getBomId());
        queryWrapper.orderByAsc(SysBomEnd::getId);
        return this.list(queryWrapper);
    }

    @Override
    public PageResult<SysBomEnd> page(SysBomEnd param) {
        LambdaQueryWrapper<SysBomEnd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomEnd::getBomIssueId, param.getBomIssueId());
        queryWrapper.orderByAsc(SysBomEnd::getId);

        Page<SysBomEnd> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);

        List<SysBomEnd> records = page.getRecords();
        for (int i = 0; i < records.size(); i++) {

            SysBomEnd sysBom = records.get(i);

            List<Long> bomLinesIds = JSON.parseArray(sysBom.getBomLines(), Long.class);
            List<String> _bomLinesIds = bomLinesIds.stream().map(String::valueOf).collect(Collectors.toList());

            sysBom.setBomLines(JSON.toJSONString(_bomLinesIds));


            // 电芯bom变更
            SysBomHistory lastCellBomHistory = bomHistoryService.getLastOne(sysBom.getBomId());

            // 包装bom变更
            SysBomHistory lastPackBomHistory = bomHistoryService.getLastOne(sysBom.getBomPackId());

            // 成品bom变更

            if (null != lastCellBomHistory && null != lastPackBomHistory) {
                String packBomVersion = Arrays.stream(lastPackBomHistory.getBomNo().split("-"))
                        .reduce((first, second) -> second)
                        .orElse("");

                records.get(i).setBomVersion(lastCellBomHistory.getBomNo() + "-" + packBomVersion);
                
                records.get(i).setCellBomCode(JSONObject.parseArray(lastCellBomHistory.getBomData(), TreeBom.class).get(0).getSapNumber());

                records.get(i).setPackBomCode(JSONObject.parseArray(lastPackBomHistory.getBomData(), TreeBom.class).get(0).getSapNumber());
            }

            SysBomHistory lastEndBomHistory = bomHistoryService.getLastOne(sysBom.getId());

            if (null != lastEndBomHistory) {
                records.get(i).setLastBomId(lastEndBomHistory.getCellBomId());
                records.get(i).setLastBomPackId(lastEndBomHistory.getPackBomId());
                records.get(i).setIsCheck(true);
                records.get(i).setSapDate(lastEndBomHistory.getCreateTime());
            } else {
                records.get(i).setIsCheck(false);
            }



        }

        return new PageResult<SysBomEnd>(page);
    }

    @Override
    public void relate(String token, SysBomEnd param) throws IOException {

        SysBomEnd end = this.getById(param.getId());

        if (StrUtil.isEmpty(end.getBomCode())) {
            throw new ServiceException(500, "请设置成品代号");
        }

        if (end.getBomLines().equals("[]")) {
            throw new ServiceException(500, "请设置工厂");
        }

        // SysBom bom = sysBomService.get(end.getBomId());

        SysBomHistory bomHistory = bomHistoryService.getLastOne(end.getBomId());

        SysBomHistory packBomHistory = bomHistoryService.getLastOne(end.getBomPackId());

        if (null == bomHistory) {
            throw new ServiceException(500, "电芯BOM未推送到SAP,提交失败");
        }

        if (null == packBomHistory) {
            throw new ServiceException(500, "包装BOM未推送到SAP,提交失败");
        }

        this.sysBomService.endBomOaAdd(param,param.getId(), null);

    }

    public Map<String, List<String>> getLineMap(List<SysWerkLine> werkLines) {

        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("WERK_CODE").build());

        Map<String, String> werkMap = new HashMap<>();

        if (null != sysconfig.getText()) {

            List<SapWerks> werks = JSONObject.parseArray(sysconfig.getText(), SapWerks.class);

            werks.forEach(e -> {
                werkMap.put(e.getWERKS(), e.getNAMECODE());
            });
        }

        werkLines.forEach(_e -> {
            _e.setNamecode(werkMap.containsKey(_e.getWerkNo()) ? werkMap.get(_e.getWerkNo()) : "");
        });

        return werkLines.stream().collect(Collectors.groupingBy(e -> e.getWerkNo(),
                Collectors.mapping(_e -> _e.getNamecode() + ";" + _e.getLineName(), Collectors.toList())));

    }

    @Override
    public void deleteWerk2BOM2Sap(SysBomEnd sysBom) {

        if (3 == sysBom.getBomRelateStatus()) {
            reDelWerk2BOM2Sap(sysBom);
            return;
        }

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();
        List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.updateLoopTreeBom(treeBoms, "");

        List<SysWerkLine> werkLines = werkLineService.getWerks(JSONObject.parseArray(sysBom.getBomLines(), Long.class));
        List<SysWerkLine> _werkLines = JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);
        Map<String, List<String>> _delWerklinesMap = getLineMap(_werkLines);
        Map<String, List<String>> werklinesMap = getLineMap(werkLines);
        List<String> editWerkNos = _werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        Map<String, String> vMap = new HashMap<String, String>();
        String resp = null;

        for (String _item : editWerkNos) {
            String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getId() + "");
            List<String> delLines = _delWerklinesMap.get(_item);
            List<String> lines = werklinesMap.containsKey(_item) ? new ArrayList<>() : werklinesMap.get(_item);
            if (!lines.isEmpty()) {
                lines.removeAll(delLines);
            }
            String linesStr;

            if (null == lines || lines.isEmpty()) {
                linesStr = "";
            } else {
                String nameCode = lines.get(0).split(";")[0];
                lines = lines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
                linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                        + lines.stream().map(String::valueOf).collect(Collectors.joining(","));
            }

            sapEditBomParams.forEach(e -> {
                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (editWerkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);
                e.setIV_STKTX(linesStr);
            });

            for (SapEditBomParam item : sapEditBomParams) {
                log.info("成品BOM变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("成品BOM变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }
        }

        delWerk2Bom2Sap(sysBom, treeBoms, _werkLines, editFails, edits, errors);
    }

    public void reDelWerk2BOM2Sap(SysBomEnd sysBom) {

        List<SapEditBomParam> sapEditBomParams = JSONObject.parseArray(sysBom.getEditFails(),
                SapEditBomParam.class);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = JSONObject.parseArray(sysBom.getEdits(), SapEditBomRespParam.class);
        List<String> errors = new ArrayList<>();
        String resp = null;

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();
        String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getId() + "");

        if (sapEditBomParams != null && !sapEditBomParams.isEmpty())
            for (SapEditBomParam item : sapEditBomParams) {
                item.setIV_AENNR(IV_AENNR);
                log.info("成品BOM工厂删除重试:" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("成品BOM工厂删除重试回复:" + resp);
                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

        List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
        List<SysWerkLine> _werkLines = JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);

        delWerk2Bom2Sap(sysBom, treeBoms, _werkLines, editFails, edits, errors);
    }

    public void delWerk2Bom2Sap(SysBomEnd sysBom, List<TreeBom> treeBoms, List<SysWerkLine> _werkLines,
            List<SapEditBomParam> editFails, List<SapEditBomRespParam> edits, List<String> errors) {

        SysBomChangeUtil bomChangeUtil = new SysBomChangeUtil();
        List<OaAddOrEditParam> oaAddOrEditParams = null;

        if (3 != sysBom.getBomRelateStatus()) {
            oaAddOrEditParams = bomChangeUtil.deleteEndBoms(sysBom, treeBoms);
        }

        if (null != editFails && !editFails.isEmpty()) {
            sysBom.setEditFails(JSONObject.toJSONString(editFails));
            sysBom.setEdits(JSONObject.toJSONString(edits));
            sysBom.setErros(StringUtil.join(errors.toArray(), ";"));
            sysBom.setBomChange(JSONObject.toJSONString(oaAddOrEditParams));
            sysBom.setBomRelateStatus(3);
            this.updateById(sysBom);
            throw new ServiceException(505, "sap推送失败");
        }

        // 移除工厂
        // SysBomChangeUtil bomChangeUtil = new SysBomChangeUtil();
        bomChangeUtil.loopDeleteVersion(treeBoms,
                _werkLines.stream().filter(e -> 1 == e.getFlag()).map(SysWerkLine::getWerkNo).distinct()
                        .collect(Collectors.toList()));

        sysBom.setAddFails("[]");
        sysBom.setEditFails("[]");
        sysBom.setEdits("[]");
        sysBom.setAdds("[]");
        sysBom.setBomData(JSONObject.toJSONString(treeBoms));
        sysBom.setBomRelateStatus(2);
        sysBom.setBomIfAdd(0);
        sysBom.setBomAddWerks("");
        this.updateById(sysBom);

        addHistory(sysBom, oaAddOrEditParams, _werkLines, 2);
    }

    private List<SapAddBomParam> getVersionIfSapError(SysBomEnd sysBom){

        List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        SysBomEnd bom = this.getOne(
            Wrappers.lambdaQuery(SysBomEnd.class)
            .eq(SysBomEnd::getBomIssueId, sysBom.getBomIssueId())
            .eq(SysBomEnd::getBomRelateStatus, 3)
            .ne(SysBomEnd::getId, sysBom.getId())
            .like(SysBomEnd::getBomData, treeBoms.get(0).getSapNumber())
            .orderByDesc(SysBomEnd::getCreateTime)
        );
        
        String addFails = Optional.ofNullable(bom).map(SysBomEnd::getAddFails).orElse("[]");
        List<SapAddBomParam> sapAddBomParams = addFails.equals("[]") ? new ArrayList<>() : JSON.parseArray(addFails, SapAddBomParam.class);

        return sapAddBomParams;
        
    }

    @Override
    public void addWerk2Bom2Sap(SysBomEnd end) throws ServiceException {

        if (3 == end.getBomRelateStatus()) {
            reBom2Sap(end);
            return;
        }

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();
        List<TreeBom> treeBoms = JSONObject.parseArray(end.getBomData(), TreeBom.class);
        List<SysWerkLine> _werkLines = JSONObject.parseArray(end.getBomAddWerks(), SysWerkLine.class);
        String resp = null;
        List<String> errors = new ArrayList<>();

        // 变更请求
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        Map<String, String> vMap = new HashMap<String, String>();
        List<String> editWerkNos = _werkLines.stream().filter(e -> 0 == e.getFlag()).map(SysWerkLine::getWerkNo)
                .distinct()
                .collect(Collectors.toList());

        List<SysWerkLine> werkLines = werkLineService
                .getWerks(JSONObject.parseArray(end.getBomLines(), Long.class));

        Map<String, List<String>> werkLineMap = getLineMap(werkLines);
        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.updateLoopTreeBom(treeBoms, "");

        for (String _item : editWerkNos) {

            String IV_AENNR = sysBomServiceUtil.callDcnAennr(end.getId() + "");

            List<String> strLines = werkLineMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapEditBomParams.forEach(e -> {
                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (editWerkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);
                e.setIV_STKTX(linesStr);
            });

            for (SapEditBomParam item : sapEditBomParams) {
                log.info("BOM成品工厂可选文本变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("BOM成品工厂可选文本变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }
        }

        // 新增请求
        List<String> addWerkNos = _werkLines.stream().filter(e -> 1 == e.getFlag()).map(SysWerkLine::getWerkNo)
                .distinct()
                .collect(Collectors.toList());

        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(end, treeBoms, "");

        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });

        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(end);

        for (String _item : addWerkNos) {

            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);

            List<String> strLines = werkLineMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(linesStr);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }
            for (SapAddBomParam item : sapAddBomParams) {
                log.info("新增工厂到BOM请求：" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增工厂到BOM回复：" + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);
                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());

                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }
        }

        endBom2Save(end, addFails, adds, editFails, edits, errors, _werkLines);

    }

    public void editBom2Sap(SysBomEnd sysBom, SysBomHistory sysBomHistory, List<String> werkNos,
            List<SysWerkLine> werkLines)
            throws ServiceException {

        if (3 == sysBom.getBomRelateStatus()) {
            reBom2Sap(sysBom);
            return;
        }

        List<TreeBom> newTreeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();

        /*
         * if (sysBomServiceUtil.ifMengeEmpty(newTreeBoms)) {
         * throw new ServiceException(500, "BOM使用量不为0");
         * }
         */

        Map<String, TreeBom> flagTreeMap = new HashMap<>();
        List<TreeBom> oldTreeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        Map<String, TreeBom> mapTreeBoms = sysBomServiceUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        String resp = null;

        List<TreeBom> newBoms = new ArrayList<>();

        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.loopNewTreeBoms(sysBom, newTreeBoms, mapTreeBoms,
                newBoms, "", flagTreeMap);

        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(sysBom, newBoms, "");

        Map<String, String> vMap = new HashMap<String, String>();

        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });

        Map<String, List<String>> werklinesMap = getLineMap(werkLines);

        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(sysBom);

        for (String _item : werkNos) {

            String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomCode());

            sapEditBomParams.forEach(e -> {

                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (werkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);

            });

            log.info("成品bom变更请求数据：" + JSONObject.toJSONString(sapEditBomParams));

            if (sapEditBomParams == null || sapEditBomParams.isEmpty()) {
                continue;
            }
            for (SapEditBomParam item : sapEditBomParams) {
                log.info("成品bom变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("成品bom变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

            if (newBoms == null || newBoms.isEmpty()) {
                continue;
            }

            log.info("成品bom变更新增请求数据: " + JSONObject.toJSONString(sapAddBomParams));
            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);

            List<String> strLines = werklinesMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String lines = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(lines);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }

            for (SapAddBomParam item : sapAddBomParams) {
                log.info("成品bom变更新增申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("成品bom变更新增申请回复: " + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }

        }

        endBom2Save(sysBom, addFails, adds, editFails, edits, errors, werkLines);

        /*
         * bom2SapStore(sysBom, newTreeBoms, oldTreeBoms, sysBomServiceUtil, addFails,
         * adds, editFails, edits, errors,
         * new SysBomError(), werkLines, nameFromJIRA);
         */

    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void withDraw(SysBomParam param) {

        SysBomEnd bom = this.getOne(Wrappers.<SysBomEnd>lambdaQuery().eq(SysBomEnd::getId, param.getId()));

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bom.getId());
        if (null == sysBomHistory) {
            throw new ServiceException(500, "新搭建的成品BOM无需撤回");
        }
        /*
         * bomLineService.remove(
         * Wrappers.<SysBomLine>lambdaQuery()
         * .eq(SysBomLine::getBomId, bom.getId())
         * );
         */

        /*
         * List<SysBomLine> sysBomLines = JSON.parseArray(sysBomHistory.getBomLines(),
         * SysBomLine.class);
         * bomLineService.insertBatch(sysBomLines);
         */
        bom.setBomRelateStatus(2);
        // bom.setProductState(sysBomHistory.getProductState().intValue());
        bom.setBomData(sysBomHistory.getBomData());
        // bom.setBomNo(sysBomHistory.getBomNo());
        bom.setBomLines(sysBomHistory.getBomLines());
        bom.setBomId(sysBomHistory.getCellBomId());
        bom.setBomPackId(sysBomHistory.getPackBomId());
        this.updateById(bom);
    }

    @Override
    public void addBom2Sap(SysBomEnd end) throws ServiceException {

        if (3 == end.getBomRelateStatus()) {
            reBom2Sap(end);
            return;
        }

        /*
         * SysBom bom = sysBomService.get(end.getBomId());
         * 
         * List<SysBomLine> sysBomLines =
         * bomLineService.getList(SysBomLineParam.builder().bomId(bom.getId()).build());
         * 
         * if (null == sysBomLines || sysBomLines.isEmpty()) {
         * throw new ServiceException(500, "产线未选");
         * }
         */

        List<TreeBom> treeBoms = JSONObject.parseArray(end.getBomData(), TreeBom.class);

        List<SysWerkLine> werkLines = werkLineService
                .getWerks(JSONObject.parseArray(end.getBomLines(), Long.class));
        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();

        String resp = null;
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(end, treeBoms, "");

        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });

        Map<String, List<String>> werkLineMap = getLineMap(werkLines);

        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(end);

        for (String _item : werkNos) {

            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);

            List<String> strLines = werkLineMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(linesStr);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }
            for (SapAddBomParam item : sapAddBomParams) {
                log.info("新增BOM请求：" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增BOM回复：" + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);
                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());

                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }
        }
        endBom2Save(end, addFails, adds, new ArrayList<>(), new ArrayList<>(), errors, werkLines);
    }

    public void reBom2Sap(SysBomEnd end) {

        // 变更请求
        List<SapEditBomParam> sapEditBomParams = JSONObject.parseArray(end.getEditFails(),
                SapEditBomParam.class);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = JSONObject.parseArray(end.getEdits(), SapEditBomRespParam.class);
        List<String> errors = new ArrayList<>();
        String resp = null;

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();
        String IV_AENNR = sysBomServiceUtil.callDcnAennr(end.getId() + "");

        if (sapEditBomParams != null && !sapEditBomParams.isEmpty())
            for (SapEditBomParam item : sapEditBomParams) {
                item.setIV_AENNR(IV_AENNR);
                log.info("成品BOM变更重试:" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("成品BOM变更重试回复:" + resp);
                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

        // 新增请求
        List<SapAddBomParam> sapAddBomParams = JSONObject.parseArray(end.getAddFails(), SapAddBomParam.class);
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = JSONObject.parseArray(end.getAdds(), SapAddBomRespParam.class);

        if (sapAddBomParams != null && !sapAddBomParams.isEmpty())
            for (SapAddBomParam item : sapAddBomParams) {

                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));

                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }

        endBom2Save(end, addFails, adds, editFails, edits, errors, null);
    }

    public void endBom2Save(SysBomEnd end, List<SapAddBomParam> addFails,
            List<SapAddBomRespParam> adds,
            List<SapEditBomParam> editFails, List<SapEditBomRespParam> edits, List<String> errors,
            List<SysWerkLine> _werkLines) {

        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
        SysBomEndChangeUtil sysBomChangeUtil = new SysBomEndChangeUtil();
        List<TreeBom> treeBoms = JSONObject.parseArray(end.getBomData(), TreeBom.class);

        Map<String, TreeBom> mapTreeBoms = new HashMap<String, TreeBom>();
        List<TreeBom> newBoms = new ArrayList<>();

        if (3 != end.getBomRelateStatus()) {
            SysBomHistory lasHistory = bomHistoryService.getLastOne(end.getId());
            Map<String, TreeBom> flagTreeMap = new HashMap<>();
            List<TreeBom> oldTreeBoms = ObjectUtil.isNotEmpty(lasHistory)
                    ? JSONObject.parseArray(lasHistory.getBomData(), TreeBom.class)
                    : new ArrayList<>();
            List<TreeBom> treeBom = JSONObject.parseArray(end.getBomData(), TreeBom.class);
            mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
            oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(end, treeBom, mapTreeBoms, newBoms, flagTreeMap);
            oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(end, newBoms, mapTreeBoms));
            oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).collect(Collectors.toList());
            // oaAddOrEditParams.forEach(a -> a.setBomNo(sysBom.getBomNo()));
            /* oaAddOrEditParams = sysBomChangeUtil.addEndBoms(end, treeBoms); */
        }

        if ((null != addFails && !addFails.isEmpty()) || (editFails != null && !editFails.isEmpty())) {
            end.setAddFails(JSONObject.toJSONString(addFails));
            end.setAdds(JSONObject.toJSONString(adds));
            end.setEditFails(JSONObject.toJSONString(editFails));
            end.setEdits(JSONObject.toJSONString(edits));
            end.setErros(StringUtil.join(errors.toArray(), ";"));
            end.setBomChange(JSONObject.toJSONString(oaAddOrEditParams));
            end.setBomRelateStatus(3);
            this.updateById(end);
            throw new ServiceException(505, "sap推送失败");
        }

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();
        Map<String, String> versionMap = new HashMap<>();
        if (1 == end.getBomIfAdd()) {
            versionMap = sysBomServiceUtil.getVersionMap(treeBoms);
        }

        Map<String, String> mapPosnr = sysBomServiceUtil.getSapPosnr(adds, versionMap);
        if (edits != null && !edits.isEmpty()) {
            sysBomServiceUtil.getEditSapPosnr(edits, mapPosnr);
        }
        sysBomServiceUtil.loopSavePosnr(treeBoms, mapPosnr, versionMap);
        Integer isAddWerk = 0;
        if (1 == end.getBomIfAdd()) {
            isAddWerk = end.getBomIfAdd();
            end.setBomIfAdd(0);
            end.setBomAddWerks("");
        }

        end.setAddFails("[]");
        end.setEditFails("[]");
        end.setEdits("[]");
        end.setAdds("[]");
        end.setBomSapVersion(treeBoms.get(0).getVersion());
        end.setBomData(JSONObject.toJSONString(treeBoms));
        end.setBomRelateStatus(2);
        this.updateById(end);

        addHistory(end, oaAddOrEditParams, _werkLines, isAddWerk);
    }

    // 添加历史记录
    @Override
    public void addHistory(SysBomEnd end, List<OaAddOrEditParam> oaAddOrEditParams, List<SysWerkLine> _werkLines,
            Integer addwerk) {

        SysBom bom = sysBomService.get(end.getBomId());

        SysBomHistoryParam bomHistoryParam = new SysBomHistoryParam();
        bomHistoryParam.setBomData(end.getBomData());
        bomHistoryParam.setBomId(end.getId());
        bomHistoryParam.setProductState(bom.getProductState());
        bomHistoryParam.setCreateTime(new Date());
        if (null != oaAddOrEditParams && !oaAddOrEditParams.isEmpty()) {
            BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams).werkLines(_werkLines)
                    .build();
            bomHistoryParam.setBomChange(JSONObject.toJSONString(bomChangeParam));
        } else {
            bomHistoryParam.setBomChange(end.getBomChange());
        }
        /*
         * bomHistoryParam.setBomNo(sysBom.getBomNo());
         * bomHistoryParam.setBomVersion(sysBom.getBomVersion());
         * bomHistoryParam.setBomName(sysBom.getBomName());
         * bomHistoryParam.setType(ifAdd ? 3 : (list.size() != 0 ? 0 : 1));
         * bomHistoryParam.setSummitor(sysBom.getSummitor());
         */
        // 电芯bom变更
        SysBomHistory lastCellBomHistory = bomHistoryService.getLastOne(end.getBomId());

        // 包装bom变更
        SysBomHistory lastPackBomHistory = bomHistoryService.getLastOne(end.getBomPackId());

        String packBomVersion = Arrays.stream(lastPackBomHistory.getBomNo().split("-"))
                .reduce((first, second) -> second)
                .orElse("");

        bomHistoryParam.setBomNo(lastCellBomHistory.getBomNo() + "-" + packBomVersion);
        bomHistoryParam.setBomVersion(bom.getBomVersion());

        bomHistoryParam.setIsAddWerk(addwerk);
        bomHistoryParam.setIsEnd(1);
        bomHistoryParam.setBomLines(end.getBomLines());
        bomHistoryParam.setCellBomId(end.getBomId());
        bomHistoryParam.setPackBomId(end.getBomPackId());
        bomHistoryService.add(bomHistoryParam);
    }

    @Override
    public void sapImport(SysBomEnd param) throws ServiceException {

        SysBomEnd end = this.getById(param.getId());
        if (StrUtil.isEmpty(end.getBomCode())) {
            throw new ServiceException(500, "请设置成品代号");
        }

        if (end.getBomLines().equals("[]")) {
            throw new ServiceException(500, "请设置工厂");
        }

        SysBomHistory bomHistory = bomHistoryService.getLastOne(end.getBomId());
        if (null == bomHistory) {
            throw new ServiceException(500, "电芯BOM未推送到SAP,提交失败");
        }

        SysBomHistory packBomHistory = bomHistoryService.getLastOne(end.getBomPackId());
        if (null == packBomHistory) {
            throw new ServiceException(500, "包装BOM未推送到SAP,提交失败");
        }

        SysBomHistory endBomHistory = bomHistoryService.getLastOne(end.getId());
        if (null != endBomHistory) {
            throw new ServiceException(500, "成品BOM已经走sap变更，提交失败");
        }

        List<TreeBom> treeBoms = JSONObject.parseArray(end.getBomData(), TreeBom.class);
        List<SysWerkLine> werkLines = werkLineService.getWerks(JSONObject.parseArray(end.getBomLines(), Long.class));
        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct().collect(Collectors.toList());

        SysBomEndSapServiceUtil sysBomServiceUtil = new SysBomEndSapServiceUtil();

        JSONObject obj = new JSONObject();

        for (String _item : werkNos) {
            obj.put(_item, param.getSapVersion());
            sysBomServiceUtil.sapProof(treeBoms, _item, param.getSapVersion());
        }

        treeBoms.get(0).setVersion(obj.toJSONString());

        end.setBomData(JSONObject.toJSONString(treeBoms));

        endBom2Save(end, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(),
                werkLines);

    }

}
