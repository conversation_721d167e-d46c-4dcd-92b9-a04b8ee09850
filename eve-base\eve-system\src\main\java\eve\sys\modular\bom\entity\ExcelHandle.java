package eve.sys.modular.bom.entity;

import com.aspose.cells.License;
import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import eve.core.exception.ServiceException;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;
import org.apache.poi.xssf.usermodel.*;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
@Builder
public class ExcelHandle {



    private static String getCellContent(XSSFCell cell) {
        if (null == cell) {
            return "";
        }
        String result = cell.getStringCellValue();
        return result;
    }

    public static int setHeight(XSSFRow row, int cellIndex) {
        XSSFCell cell = row.getCell(cellIndex);
        // 1.先取出内容 计算长度 （这个方法在后面...）
        String content = getCellContent(cell);
        //log.info("内容" + content);
        // 计算字体的高度
        XSSFCellStyle cellStyle = cell.getCellStyle();
        XSSFFont font = cellStyle.getFont();
        // 字体的高度
        short fontHeight = font.getFontHeight();
        //log.info("字体的高度" + fontHeight);// 11号字体的高度 这里不重要（本方法也用不着） 这里设置每一行都是18磅
        // 计算字符的宽度
        // 代入默认字符宽度8：//字符像素宽度 = 字体宽度 * 字符个数 + 边距
        // 像素 = 5 + (字符个数 * 7)
        int i = 5 + (content.toCharArray().length * 7);
        // POI中的字符宽度算法是：double 宽度 = (字符个数 * (字符宽度 - 1) + 5) / (字符宽度 - 1) * 256;
        double poiWidth = content.length() * 12 / 7 * 256;
        // 然后再四舍五入成整数。
        int width = (int) Math.ceil(poiWidth);
        //log.info("字符串长度的宽度" + width);
        // 2.除以宽度 计算行数乘以行高 (256*80) 是我设置的我的单元格列的宽度
        double rowNum = (double) width / (256 * 18);
        //log.info("计算出来的行数" + rowNum);
        int rowNums = (int) Math.ceil(rowNum) + (content.split("\\n", -1).length -1);
        //log.info("行数" + rowNums);
        Integer height = rowNums * (fontHeight + 5) ;// 这个420

        height += (25 + 40 * (rowNums - 1));

        if (height < 320) {
            height = 350;
        }

        if (rowNum == 0) {
            return 0;
        }

        return height;// 直接设置行高为这个值就OK了
    }

    public static void main(String[] args) throws Exception {
        Context context = new Context();
        Map<Object,Object> map = new HashMap<>();
        map.put("code","123");
        context.putVar("name", "实验名称");
        context.putVar("other", "■ 是 □ 否 ");
        context.putVar("map", map);
        List<Map> objects = new ArrayList<>();
        //方案
        Map<Object,Object> map1 = new HashMap<>();
        map1.put("name","实验组1");
        map1.put("value","方案1");
        objects.add(map1);
        //不足6个补齐
        if(objects.size() < 6){
            int size = objects.size();
            for (int i = 0; i < 6 - size; i++) {
                objects.add(new HashMap());
            }
        }
        context.putVar("list", objects);


        //物料
        List<Map> objects2 = new ArrayList<>();
        Map<Object,Object> map2 = new HashMap<>();
        map2.put("name","超长物料名称123143254354353454334324234325354超长物料名称123143254354353454334324234325354");
        map2.put("code","编号1");
        Map<Object,Object> map3 = new HashMap<>();
        map3.put("name","物料名称");
        map3.put("code","编号1");
        objects2.add(map2);
        objects2.add(map3);
        if(objects2.size() < 32){
            int size = objects2.size();
            for (int i = 0; i < 32 - size; i++) {
                objects2.add(new HashMap());
            }
        }

        context.putVar("partList", objects2);
        InputStream inStream =  new FileInputStream("C:\\Users\\<USER>\\Desktop\\实验通知单模板.xlsx");
//        String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
        File outFile = new File("C:\\Users\\<USER>\\Desktop\\实验通知单.xlsx");
        OutputStream os = new FileOutputStream(outFile);
        //填充文字
        JxlsHelper.getInstance().processTemplate(inStream, os, context);

        // 表格高度处理
        File file = new File("C:\\Users\\<USER>\\Desktop\\实验通知单.xlsx");
        FileOutputStream fos = null;
        try (InputStream fis = new FileInputStream(file);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            // 不关后面写不进去
            fis.close();

            try {
                XSSFSheet sheet = workbook.getSheet("实验通知单");

                for (int i = 25; i < 41; i++) {
                    XSSFRow row1 = sheet.getRow(i);
                    short height = row1.getHeight();

                    Integer i1 = setHeight(row1, 2) ;
                    if(height < i1){
                        row1.setHeight(Short.valueOf(i1.toString()));
                    }


                    i++;
                }
            } catch (Exception e) {

            }

            fos = new FileOutputStream(file);
            // 重新写回该文件
            workbook.write(fos);
            fos.flush();
        } catch (Exception e) {
            throw new ServiceException(500, e.getMessage());
        } finally {
            fos.close();
        }

        //转pdf
        InputStream is = null;
        // 验证license
//        is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
        is = new FileInputStream("D:\\tmp\\model\\license.xml");
        License aposeLic = new License();
        aposeLic.setLicense(is);

        Workbook wb = new Workbook("C:\\Users\\<USER>\\Desktop\\实验通知单.xlsx");// 原始excel路径
        File pdf = new File("C:\\Users\\<USER>\\Desktop\\实验通知单.pdf");
        FileOutputStream fileOutputStream = new FileOutputStream(pdf);
        PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
        pdfSaveOptions.setOnePagePerSheet(true);
//        String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");

        wb.save( fileOutputStream, pdfSaveOptions);

    }
}
