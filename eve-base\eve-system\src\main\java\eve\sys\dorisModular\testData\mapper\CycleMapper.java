package eve.sys.dorisModular.testData.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import eve.sys.dorisModular.testData.entity.Cycle;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@DS("b4")
public interface CycleMapper extends BaseMapper<Cycle> {
    void batchInsertCycles(List<Cycle> list);
}
