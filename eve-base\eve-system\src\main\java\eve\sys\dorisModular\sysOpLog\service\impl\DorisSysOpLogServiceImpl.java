package eve.sys.dorisModular.sysOpLog.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.factory.PageFactory;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.sysOpLog.entity.DorisSysOpLog;
import eve.sys.dorisModular.sysOpLog.mapper.DorisSysOpLogMapper;
import eve.sys.dorisModular.sysOpLog.service.DorisISysOpLogService;
import eve.sys.modular.log.param.SysOpLogParam;
import eve.sys.util.NclobCompressUtil;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class DorisSysOpLogServiceImpl extends ServiceImpl<DorisSysOpLogMapper, DorisSysOpLog> implements DorisISysOpLogService {

    @Override
    public PageResult<DorisSysOpLog> page(SysOpLogParam sysOpLogParam) throws UnsupportedEncodingException {
        LambdaQueryWrapper<DorisSysOpLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DorisSysOpLog::getId, DorisSysOpLog::getAccount, DorisSysOpLog::getCreateName, DorisSysOpLog::getName,
                DorisSysOpLog::getOpType,DorisSysOpLog::getSuccess,DorisSysOpLog::getUrl,DorisSysOpLog::getIp,DorisSysOpLog::getOpTime);
        if (ObjectUtil.isNotNull(sysOpLogParam)) {
            //根据名称模糊查询
            if (ObjectUtil.isNotEmpty(sysOpLogParam.getName())) {
                queryWrapper.like(DorisSysOpLog::getName, sysOpLogParam.getName());
            }
            if (ObjectUtil.isNotEmpty(sysOpLogParam.getCreateName())) {
                queryWrapper.and(wrapper ->{
                    wrapper.like(DorisSysOpLog::getCreateName, sysOpLogParam.getCreateName()).or()
                            .like(DorisSysOpLog::getAccount, sysOpLogParam.getCreateName()).or()
                            .like(DorisSysOpLog::getCreateAccount, sysOpLogParam.getCreateName());
                });

            }
            //根据操作类型查询
            if (ObjectUtil.isNotEmpty(sysOpLogParam.getOpType())) {
                queryWrapper.eq(DorisSysOpLog::getOpType, sysOpLogParam.getOpType());
            }
            //根据是否成功查询
            if (ObjectUtil.isNotEmpty(sysOpLogParam.getSuccess())) {
                queryWrapper.eq(DorisSysOpLog::getSuccess, sysOpLogParam.getSuccess());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(sysOpLogParam.getSearchBeginTime(), sysOpLogParam.getSearchEndTime())) {
                queryWrapper.ge(DorisSysOpLog::getOpTime, DateUtil.parse(sysOpLogParam.getSearchBeginTime(),"yyyy-MM-dd HH:mm:ss"));
                queryWrapper.le(DorisSysOpLog::getOpTime,DateUtil.parse(sysOpLogParam.getSearchEndTime(),"yyyy-MM-dd HH:mm:ss"));
            }
        }
        //根据操作时间倒序排列
        queryWrapper.orderByDesc(DorisSysOpLog::getOpTime);
        Page<DorisSysOpLog> page = this.page(PageFactory.defaultPage(), queryWrapper);

        return new PageResult<>(page);
    }

    @Override
    public DorisSysOpLog get(SysOpLogParam sysOpLogParam) throws UnsupportedEncodingException {
        DorisSysOpLog sysOpLog = this.getById(sysOpLogParam.getId());
        if(StrUtil.isNotBlank(sysOpLog.getParamCompress())){
            sysOpLog.setParam(NclobCompressUtil.decodeAndDecompress(sysOpLog.getParamCompress()));
        }
        if(StrUtil.isNotBlank(sysOpLog.getResultCompress())){
            sysOpLog.setResult(NclobCompressUtil.decodeAndDecompress(sysOpLog.getResultCompress()));
        }
        return sysOpLog;
    }
}
