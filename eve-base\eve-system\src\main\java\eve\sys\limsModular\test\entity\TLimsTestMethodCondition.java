package eve.sys.limsModular.test.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 测试项目试验条件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@TableName("T_LIMS_TEST_METHOD_CONDITION")
public class TLimsTestMethodCondition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 制单人名称
     */
    private String createdbyname;

    /**
     * 制单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;

    /**
     * 试验项目方法
     */
    private Long testmethodid;

    /**
     * 条件名称
     */
    private String conditionname;

    /**
     * 序号
     */
    private int sorter;

    /**
     * 备注
     */
    private String remark;

    /**
     * 条件值
     */
    private String conditionvalue;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数据类型
     */
    private String datatype;

    /**
     * 字段标记
     */
    private String fieldsign;

    /**
     * 填写值
     */
    private String fillvalue;

    /**
     * 条件值样例
     */
    private String conditionvaluedemo;


}
