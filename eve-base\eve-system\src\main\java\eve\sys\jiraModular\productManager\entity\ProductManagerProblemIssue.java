package eve.sys.jiraModular.productManager.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@Getter
@Setter
//@TableName("product_manager_problem_issue")
@TableName("product_manager_problem_issue_test")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductManagerProblemIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    private String issueKey;

    private Long parentId;

    private String productName;

    private String projectName;

    private String projectStage;

    private String projectStageName;

    private String productState;

    private String productStateName;

    private String problemDimension;

    private String problemDimensionName;

    private String problemStatus;

    private String problemStatusName;

    private String problemStatus1;

    private String problemStatusName1;

    private String problemDescription;

    private String causeAnalysis;

    private String problemSolving;

    private String productProcess;

    private String responsiblePerson;

    private String responsiblePersonName;

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date findDate;

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date plannedCompletionDate;

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualCompletionDate;

    private Long stage;

//    private Date planReviewDate;
//
//    private Date planReviewDateChangeHistory;
//
//    private Date actualReviewDate;
//
//    private Date actualCompletionDate;
//
//    private Date reviewDate;
//
//    private Long overDays;

    private Long id;

    private String pkey;

    private Long issuenum;

    private Long project;

    private String reporter;

    private String assignee;

    private String creator;

    private String issuetype;

    private String summary;

    private String description;

    private String environment;

    private String priority;

    private String resolution;

    private String statusId;

    private String issueStatusName;

    private Date created;

    private Date updated;

    private Date duedate;

    private Date resolutiondate;

//    private Long votes;
//
//    private Long watches;
//
//    private Long timeoriginalestimate;
//
//    private Long timeestimate;
//
//    private Long timespent;

    private Long workflowId;

    private Long security;

//    private Long fixfor;

//    private Long component;

//    private String archived;

//    private String archivedby;

//    private Date archiveddate;

    @TableField(exist = false)
    private boolean disabled = false;

    @TableField(exist = false)
    private boolean editable = false;

    @TableField(exist = false)
    private Long reviewResult = 0L;

    @TableField(exist = false)
    private String reviewOpinion = "";

    @TableField(exist = false)
    private String listType = "";

    @TableField(exist = false)
    private String productStates;

    @TableField(exist = false)
    private Integer isCurWeek;

    @TableField(exist = false)
    private String productCateMultiName;

    @TableField(exist = false)
    private String productSplitName;

    @TableField(exist = false)
    private Long parentDept; //-- 父级部门ID

    @TableField(exist = false)
    private String productParentCate; //-- 父级产品类别ID

    @TableField(exist = false)
    private String productChildCate; //-- 子级产品类别ID
}
