package eve.sys.limsModular.temGradient.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 温度梯度
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@TableName("T_LIMS_TEM_GRADIENT")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TLimsTemGradient implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 检测项目ID
     */
    private Long ordtaskid;

    /**
     * 条件ID
     */
    private Long ordtaskconditionid;

    /**
     * 类型
     */
    private String datatype;

    /**
     * 排序
     */
    private Long orderno;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 倍率
     */
    private String magnification;

    /**
     * 起始SOC
     */
    private String startsoc;

    /**
     * 结束SOC
     */
    private String endsoc;

    /**
     * SOC取点间隔
     */
    private String intervalsoc;

    /**
     * SOC点
     */
    private String spotsoc;

    /**
     * 点位数
     */
    private String pointnumber;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 制单人名称
     */
    private String createdbyname;

    /**
     * 制单时间
     */
    private Date createdtime;

    /**
     * 制单人单位编码
     */
    private String createdbyorgid;

    /**
     * 制单人单位名称
     */
    private String createdbyorgname;

    /**
     * SOC
     */
    private String soc;

    /**
     * 功率(W)
     */
    private String power;

    /**
     * 功率截止条件
     */
    private String powercondition;

    /**
     * 功率条件值
     */
    private String powerconditionvalue;

    /**
     * 测试项目
     */
    private String testitems;

    /**
     * 检出限
     */
    private String detectionlimit;

    /**
     * 客户标准/限值要求
     */
    private String requirement;

    /**
     * RoHS限值
     */
    private String rohsvalue;

    /**
     * 单位
     */
    private String unit;

    private Long requirementid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 放电倍率
     */
    private String dischargerate;

    /**
     * 放电时间
     */
    private String dischargetime;

    /**
     * 测试位置
     */
    private String testposition;

    /**
     * 测试点数量
     */
    private String testpoints;

    /**
     * 试验阶段
     */
    private String teststage;

    /**
     * 参数项
     */
    private String testparam;

    /**
     * 电流
     */
    private String electriccurrent;

    /**
     * 方向
     */
    private String direction;

    /**
     * 时间
     */
    private String time;

    /**
     * 存储天数
     */
    private Long storagedays;

    /**
     * 总存储天数
     */
    private Long totalstoragedays;

    /**
     * 大中检
     */
    private String largeinspection;

    /**
     * 中检信息是否已同步 1：已同步
     */
    private String issync;


    /**
     * 内阻
     */
    private String innerres;

    /**
     * 电压
     */
    private String voltage;

    /**
     * 高度
     */
    private String height;

    /**
     * 体积
     */
    private String volume;

    /**
     * 电芯厚度
     */
    private String cellheight;

    /**
     * 重量
     */
    private String weight;

    /**
     * 绝缘阻值
     */
    private String isolateres;

    /**
     * 小中检
     */
    private String smallinspection;

    /**
     * 补电中检
     */
    private String recharge;

    /**
     * 尺寸信息类型:尺寸类型
     */
    private String sizetype;

    /**
     * 尺寸信息类型:测量次数
     */
    private Integer measuretime;

    /**
     * 尺寸信息类型:计算规则
     */
    private String calrule;

    /**
     * 照片
     */
    private String picture;

    /**
     * 过程视频
     */
    private String video;

    /**
     * 前后/循环数据记录值，过程电压
     */
    private String processvoltage;

    /**
     * 前后/循环数据记录值，过程电流
     */
    private String processcurrent;

    /**
     * 前后/循环数据记录值，过程温度
     */
    private String processtemp;
}
