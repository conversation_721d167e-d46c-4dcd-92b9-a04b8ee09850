package eve.sys.modular.bom.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aspose.cells.*;
import com.aspose.words.net.System.Data.DataException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.context.login.LoginContextHolder;
import eve.core.exception.ServiceException;
import eve.core.file.FileOperator;
import eve.core.pojo.page.PageResult;
import eve.core.pojo.response.ErrorResponseData;
import eve.sys.config.FileConfig;
import eve.sys.jiraModular.jiraUserSignature.entity.Ao8de701UserSignature;
import eve.sys.jiraModular.jiraUserSignature.service.IAo8de701UserSignatureService;
import eve.sys.modular.auth.service.AuthService;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.mapper.SysBomMapper;
import eve.sys.modular.bom.params.*;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bom.utils.BomUtils;
import eve.sys.modular.bomPush.entity.SysBomPush;
import eve.sys.modular.bomPush.service.ISysBomPushService;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomend.service.ISysBomEndService;
import eve.sys.modular.bomerror.entity.SysBomError;
import eve.sys.modular.bomerror.service.ISysBomErrorService;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.param.SysBomHistoryParam;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;
import eve.sys.modular.consts.entity.SysConfig;
import eve.sys.modular.consts.param.SysConfigParam;
import eve.sys.modular.consts.service.SysConfigService;
import eve.sys.modular.dict.entity.SysDictData;
import eve.sys.modular.dict.service.SysDictDataService;
import eve.sys.modular.file.service.SysFileInfoService;
import eve.sys.modular.node.service.ISysNodeService;
import eve.sys.modular.part.entity.SysPart;
import eve.sys.modular.part.service.ISysPartService;
import eve.sys.modular.part.utils.GenerateUniqueIdUtil;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.param.ProjectDetail;
import eve.sys.modular.product.param.request.DocParams;
import eve.sys.modular.product.service.IProductManagerService;
import eve.sys.modular.product.service.ProductJiraService;
import eve.sys.modular.product.service.ProductsService;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.techdoc.entity.TechDoc;
import eve.sys.modular.werkline.entity.SysWerkLine;
import eve.sys.modular.werkline.service.ISysWerkLineService;
import eve.sys.util.QRCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.util.StringUtil;
import org.apache.poi.xssf.usermodel.*;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.context.annotation.Lazy;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
@Service("ISysBomService")
@Slf4j
public class SysBomServiceImpl extends ServiceImpl<SysBomMapper, SysBom> implements ISysBomService {

    @Resource
    private ISysWerkLineService werkLineService;

    @Resource
    private ISysBomLineService bomLineService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private FileOperator fileOperator;

    @Resource
    private ISysBomErrorService bomErrorService;

    @Resource
    @Lazy
    private ISysBomPushService bomPushService;

    @Resource
    private ProductJiraService productJiraService;

    @Resource
    private ProductsService productsService;

    @Resource
    private IProductManagerService productManagerService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private ISysPartService partService;

    @Resource
    private ISysBomEndService sysBomEndService;

    @Resource
    private SysDictDataService dictDataService;

    @Resource
    private IAo8de701UserSignatureService signatureService;

    /*@Resource
    private IProductManagerService productManagerService;*/

    @Resource
    private AuthService authService;

    @Resource
    private ISysNodeService nodeService;

    @Override
    public List<TreeBom> initBaseBom(SysBomParam param){

        String id1 = GenerateUniqueIdUtil.getGuid();
        TreeBom bom1 = TreeBom.builder()
                    .id(id1)
                    .parent_id(null)
                    .partClass(param.getBomType() == 0 ? "电芯半成品" : "包装半成品")
                    .partName("")
                    .partDescription("")
                    .partGroup("")
                    .partLoss(0)
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber("")
                    .posnr(null)
                    .version("")
                    .partUse(1000)
                    .sapPartUse(1000)
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(true)
                    .order(0)
                    .base(1)
                    .build();

        if (param.getBomType() == 1) {
            List<TreeBom> initBoms1 = new ArrayList<>();
            initBoms1.add(bom1);
            return initBoms1;
        }
        String id2 = GenerateUniqueIdUtil.getGuid();
        String id3 = GenerateUniqueIdUtil.getGuid();
        String id4 = GenerateUniqueIdUtil.getGuid();
        String id5 = GenerateUniqueIdUtil.getGuid();

        TreeBom bom2 = TreeBom.builder()
                    .id(id2)
                    .parent_id(id1)
                    .partClass("组装半成品")
                    .partName("")
                    .partDescription("")
                    .partGroup("")
                    .partLoss(0)
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber("")
                    .posnr(null)
                    .version("")
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(false)
                    .order(0)
                    .base(1)
                    .build();

        bom1.getLists().add(bom2);

        TreeBom bom3 = TreeBom.builder()
                    .id(id3)
                    .parent_id(id2)
                    .partClass("芯包半成品")
                    .partName("")
                    .partDescription("")
                    .partGroup("")
                    .partLoss(0)
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber("")
                    .posnr(null)
                    .version("")
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(false)
                    .order(0)
                    .base(1)
                    .build();

        bom2.getLists().add(bom3);

        TreeBom bom4 = TreeBom.builder()
                    .id(id4)
                    .parent_id(id3)
                    .partClass("正极片")
                    .partName("")
                    .partDescription("")
                    .partGroup("")
                    .partLoss(0)
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber("")
                    .posnr(null)
                    .version("")
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(false)
                    .order(0)
                    .base(1)
                    .build();

        TreeBom bom5 = TreeBom.builder()
                    .id(id5)
                    .parent_id(id3)
                    .partClass("负极片")
                    .partName("")
                    .partDescription("")
                    .partGroup("")
                    .partLoss(0)
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber("")
                    .posnr(null)
                    .version("")
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(false)
                    .order(0)
                    .base(1)
                    .build();

        bom3.getLists().add(bom4);
        bom3.getLists().add(bom5);

        List<TreeBom> initBoms = new ArrayList<>();
        initBoms.add(bom1);
        return initBoms;
    }

    @Override
    public List<BomSapProof> sapVerify(SysBomParam param) throws ParseException {
        SysBom bom = this.get(param.getId());
        SysBomHistory lasHistory = bomHistoryService.getLastOne(param.getId());
        if (bom.getProductState() < 4 || lasHistory == null) {
            throw new ServiceException(500, "bom不可校验");
        }
        SysBomSapServiceUtil sapServiceUtil = new SysBomSapServiceUtil();
        List<TreeBom> oldTreeBoms = JSONObject.parseArray(lasHistory.getBomData(), TreeBom.class);
        return sapServiceUtil.getSapProof(bom, oldTreeBoms);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withDraw(SysBomParam param){
        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getId, param.getId());
        SysBom bom = this.getOne(queryWrapper);
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bom.getId());
        if (null == sysBomHistory) {
            throw new ServiceException(500, "新搭建的BOM无需撤回");
        }
        bomLineService.remove(
            Wrappers.<SysBomLine>lambdaQuery()
            .eq(SysBomLine::getBomId, bom.getId())
        );

        List<SysBomLine> sysBomLines = JSON.parseArray(sysBomHistory.getBomLines(), SysBomLine.class);
        bomLineService.insertBatch(sysBomLines);
        bom.setBomStatus(2L);
        bom.setProductState(sysBomHistory.getProductState().intValue());
        bom.setBomData(sysBomHistory.getBomData());
        bom.setBomNo(sysBomHistory.getBomNo());
        bom.setBomTransport(sysBomHistory.getBomTransport());
        bom.setBomName(sysBomHistory.getBomName());
        bom.setBomVersion(sysBomHistory.getBomVersion());
        bom.setFileId(sysBomHistory.getFileId());
        bom.setBomIfAdd(0);
        this.updateById(bom);
    }

    @Override
    public void sapImportVerify( SysBomParam param) {
        SysBom bom = this.get(param.getId());
        SysBomHistory history = bomHistoryService.getLastOne(bom.getId());
        if (bom.getProductState() < 4 || null != history) {
            throw new ServiceException(500, "bom状态不可导入sap");
        }
    }

    @Override
    public List<SapWerks> getWerksOptions() {
        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("WERK_CODE").build());
        List<SapWerks> werks = new ArrayList<>();
        if (null != sysconfig.getText()) {
            werks = JSONObject.parseArray(sysconfig.getText(), SapWerks.class);
        }
        return werks;
    }

    @Override
    public void sapImport(SysBomParam param) {
        SysBom bom = this.get(param.getId());

        List<SysBomLine> sysBomLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(bom.getId()).build());

        List<SysWerkLine> werkLines = werkLineService
                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

        if (werkLines.size() > 0 && !StrUtil.isEmpty(param.getWerk())) {
            Optional<SysWerkLine> optionalWerkLine = werkLines.stream()
                    .filter(e -> e.getWerkNo().equals(param.getWerk())).findFirst();
            if (!optionalWerkLine.isPresent()) {
                throw new ServiceException(500, "选择的工厂不匹配");
            }
        }

        List<TreeBom> treeBoms = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
        SysBomSapServiceUtil sapServiceUtil = new SysBomSapServiceUtil();
        importFromSap(treeBoms, param, sapServiceUtil);
        bom.setBomData(JSONObject.toJSONString(treeBoms));
        bom.setBomStatus(7L);
        bom.setNum(param.getNum());
        this.updateById(bom);
    }

    public void importFromSap(List<TreeBom> treeBoms, SysBomParam param, SysBomSapServiceUtil sapServiceUtil) {

        TreeBom temp = sapServiceUtil.getTreeBom(treeBoms, param.getTreeBomId());

        if (!StrUtil.isEmpty(temp.getParent_id()) && StrUtil.isEmpty(param.getWerk())) {
            TreeBom tempParentBom = sapServiceUtil.getTreeBom(treeBoms, temp.getParent_id());
            JSONObject werkObj = JSONObject.parseObject(tempParentBom.getVersion());
            Iterator<String> it = werkObj.keySet().iterator();
            while (it.hasNext()) {
                param.setWerk(it.next());
                break;
            }
        }

        SapBomScanRespParam bomScanRespParam = sapServiceUtil.callScanBom(temp.getSapNumber(), param.getWerk(),
                param.getVersion());
        if (null == bomScanRespParam.getIT_DATA() || bomScanRespParam.getIT_DATA().isEmpty()) {
            throw new ServiceException(500, "sap无记录");
        }

        temp.getLists().clear();

        JSONObject obj = new JSONObject();
        obj.put(param.getWerk(), bomScanRespParam.getIT_TITLE().get(0).getSTLAL());
        String version = obj.toJSONString();

        temp.setVersion(version);

        List<String> INDNRKS = bomScanRespParam.getIT_DATA().stream().map(SapBomScanParam::getIDNRK)
                .collect(Collectors.toList());

        List<SysPart> parts = partService.getPartListBySapNumbers(INDNRKS);

        for (SapBomScanParam item : bomScanRespParam.getIT_DATA()) {
            Optional<SysPart> optionalPart = parts.stream().filter(e -> e.getSapNumber().equals(item.getIDNRK()))
                    .findFirst();
            String cat = optionalPart.isPresent() ? optionalPart.get().getPartClass() : "";
            String partname = optionalPart.isPresent() ? optionalPart.get().getPartName() : "";
            TreeBom bom = TreeBom.builder()
                    .id(GenerateUniqueIdUtil.getGuid())
                    .parent_id(temp.getId())
                    .sapNumber(item.getIDNRK())
                    .partUnit(item.getMEINS())
                    .partUse(sapServiceUtil.Mutil(item.getMENGE(), temp.getPartUse()))
                    .partClass(cat)
                    .sapPartUse(item.getMENGE())
                    .partName(partname)
                    .partDescription(item.getIDNRK_TXT())
                    .partGroup("")
                    .partLoss(item.getAUSCH())
                    .partMaybe("")
                    .open(true)
                    .desc("")
                    .partNumber(item.getIDNRK())
                    .posnr(item.getPOSNR())
                    .version("")
                    .lists(new ArrayList<>())
                    .substitute(new ArrayList<>())
                    .validate(true)
                    .order(0)
                    .build();
            temp.getLists().add(bom);
        }
    }

    /**
     *
     * @param statusFlag     是否只查已审核
     * @param statusFlag2    若有审核中，是否查上一个
     * @return
     */
    @Override
    public List<SysBom> getBomFiles(Long issueId,boolean statusFlag,boolean statusFlag2) {
        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
            SysBom::getId,SysBom::getBomIssueId,SysBom::getBomName,SysBom::getBomVersion,SysBom::getUpdateTime,
            SysBom::getBomSourceId,SysBom::getSummitor,SysBom::getManager,SysBom::getBomNo,
            SysBom::getReviewer,SysBom::getApprover,SysBom::getFileId,SysBom::getBomStatus,SysBom::getProductState
            ,SysBom::getBomData
        );
        queryWrapper.eq(SysBom::getBomIssueId, issueId);
        queryWrapper.eq(SysBom::getBomType, 0);
        //只显示已审核
        if(statusFlag){
            queryWrapper.eq(SysBom::getBomStatus,2);
        }
        queryWrapper.orderByAsc(SysBom::getId);
        List<SysBom> sysBoms = this.list(queryWrapper);

        List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomIssueId(issueId).build());
        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        //工厂线别
        List<SysWerkLine> werkLines = getBomLines(issueId, sysBomLines);

        sysBoms.forEach(e -> {
            e.setLines(new ArrayList<>());
            e.setWerkLines(new ArrayList<>());
            if (lineMap.containsKey(e.getId())) {
                lineMap.get(e.getId()).forEach(_e -> {
                    werkLines.stream().filter($e -> $e.getId().equals(_e.getLineId())).findFirst().ifPresent(p -> {
                        e.getWerkLines().add(p);
                    });
                });
            }
        });

        //
        if(statusFlag2){
            List<SysBom> statusNot2 = sysBoms.stream().filter(e -> e.getBomStatus() != 2L).collect(Collectors.toList());
            if(!ObjectUtil.isEmpty(statusNot2)){
                sysBoms.stream().filter(e -> e.getBomStatus() != 2L).forEach(e -> {
                    LambdaQueryWrapper<SysBomHistory> queryHis =  new LambdaQueryWrapper<>();
                    queryHis.eq(SysBomHistory::getBomId,e.getId());
                    queryHis.orderByDesc(SysBomHistory::getUpdateTime);
                    Optional<SysBomHistory> first = bomHistoryService.list(queryHis).stream().findFirst();
                    if(first.isPresent()){
                        SysBomHistory his = first.get();
                        e.setBomName(his.getBomName());
                        e.setBomNo(his.getBomName());
                        e.setBomVersion(his.getBomVersion());
                        e.setBomRemark(his.getRemark());
                        e.setFileId(his.getFileId());
                        e.setSummitor(his.getSummitor());
                        e.setUpdateTime(his.getCreateTime());
                        e.setBomData(his.getBomData());
                    }
                });
            }
        }


        return sysBoms;
    }

    public List<SysWerkLine> getBomLines(Long issueId, List<SysBomLine> sysBomLines) {

        if (null == sysBomLines || sysBomLines.isEmpty()) {
            return new ArrayList<>();
        }

        List<SysWerkLine> werkLines = werkLineService
                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("WERK_CODE").build());

        Map<String, String> werkMap = new HashMap<>();

        if (null != sysconfig.getText()) {

            List<SapWerks> werks = JSONObject.parseArray(sysconfig.getText(), SapWerks.class);

            werks.forEach(e -> {
                werkMap.put(e.getWERKS(), e.getNAMECODE());
            });
        }

        werkLines.forEach(e -> {
            e.setNamecode(werkMap.containsKey(e.getWerkNo()) ? werkMap.get(e.getWerkNo()) : "");
        });

        return werkLines;
    }

    @Override
    public Map<String, List<SysWerkLine>> getWerkLines() {

        List<SapWerks> werks = getWerks();

        List<SysWerkLine> werkLines = werkLineService.getWerkLindList();

        if (werkLines.isEmpty()) {
            return null;
        }

        Map<String, String> sapMap = new HashMap<>(werks.size());

        for (SapWerks item : werks) {
            sapMap.put(item.getWERKS(), item.getNAMECODE() + ";" + item.getNAME1());
        }
        for (SysWerkLine item : werkLines) {
            if (!sapMap.containsKey(item.getWerkNo())) {
                item.setNamecode("");
                item.setWerks("");
                continue;
            }
            String[] ret = sapMap.get(item.getWerkNo()).split(";");
            item.setNamecode(ret[0]);
            item.setWerks(ret[1]);
        }

        Map<String, List<SysWerkLine>> mapWerkLines = werkLines.stream().collect(Collectors.groupingBy(e -> e.getWerkNo()));

        return mapWerkLines;
    }

    @Override
    public PageResult<SysBom> page(SysBomParam param) {
        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getBomStatus, 2);
        queryWrapper.eq(SysBom::getBomType, Optional.ofNullable(param).map(SysBomParam::getBomType).orElse(1));
        queryWrapper.eq(SysBom::getBomIssueId, param.getBomIssueId());

        if (ObjectUtil.isNotNull(param)) {

            if (ObjectUtil.isNotEmpty(param.getBomNo())) {
                queryWrapper.like(SysBom::getBomNo, param.getBomNo().trim());
            }
            if (ObjectUtil.isNotEmpty(param.getBomName())) {
                queryWrapper.like(SysBom::getBomName, param.getBomName().trim());
            }
            if (ObjectUtil.isNotEmpty(param.getBomlineIds())) {

                List<Long> bomids = bomLineService.list(Wrappers.<SysBomLine>lambdaQuery()
                    .eq(SysBomLine::getBomType, Optional.ofNullable(param).map(SysBomParam::getBomType).orElse(1))
                    .eq(SysBomLine::getBomIssueId, param.getBomIssueId())
                    .in(SysBomLine::getLineId, param.getBomlineIds())
                ).stream().map(SysBomLine::getBomId).collect(Collectors.toList());

                if (bomids.size() < 1) {
                    bomids.add(0L);
                }
                queryWrapper.in(SysBom::getId, bomids);
            }
        }

        PageResult<SysBom> rest = new PageResult<SysBom>(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));
        List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomIssueId(param.getBomIssueId()).build());
        Map<Long, List<Long>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(SysBomLine::getBomId,Collectors.mapping(SysBomLine::getLineId, Collectors.toList())));

        for (SysBom item : rest.getRows()) {
            item.setLines(new ArrayList<>());
            item.getLines().addAll(lineMap.getOrDefault(item.getId(), new ArrayList<>()));
        }
        return rest;
    }

    @Override
    public List<SysBom> getLists(BomParam param) {

        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysBom::getId,SysBom::getBomStatus,SysBom::getBomData,SysBom::getFileId,SysBom::getBomNo,SysBom::getBomVersion,SysBom::getBomIssueId,SysBom::getBomType,SysBom::getProductState);
        queryWrapper.eq(SysBom::getBomIssueId, param.getBomIssueId());
        queryWrapper.eq(SysBom::getBomType, param.getBomType());
        Optional.ofNullable(param).map(BomParam::getId).ifPresent(val->{
            queryWrapper.eq(SysBom::getId, val);
        });
        queryWrapper.orderByAsc(SysBom::getId);
        List<SysBom> sysBoms = this.list(queryWrapper);

        List<SysBomHistory> bomHistorys = new ArrayList<>();
        List<SysBomEnd> bomEnds = new ArrayList<>();

        List<Long> ids = sysBoms.stream().map(SysBom::getId).collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(sysBoms)) {

            bomHistorys = bomHistoryService.list(
                Wrappers.<SysBomHistory>lambdaQuery().in(
                    SysBomHistory::getBomId,ids
                )).stream().collect(Collectors.toList());

            bomEnds = sysBomEndService.list(
                Wrappers.<SysBomEnd>lambdaQuery().in(
                    param.getBomType() == 0 ? SysBomEnd::getBomId : SysBomEnd::getBomPackId ,ids
                )
            ).stream().collect(Collectors.toList());

        }

        List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomIssueId(param.getBomIssueId()).build());
        Map<Long, List<Long>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(SysBomLine::getBomId,Collectors.mapping(SysBomLine::getLineId, Collectors.toList())));

        for (SysBom item : sysBoms) {

            Optional<SysBomHistory> lastHistory = bomHistorys.stream().filter(e->e.getBomId().equals(item.getId())).sorted(Comparator.comparing(SysBomHistory::getCreateTime).reversed()).findFirst();

            item.setIsCheck(lastHistory.isPresent() ? true : false);
            item.setSapDate(lastHistory.isPresent() ? lastHistory.get().getCreateTime() : null);
            item.setLines(new ArrayList<>());
            item.getLines().addAll(lineMap.getOrDefault(item.getId(), new ArrayList<>()));
            item.setBomEndCount(
                bomEnds.stream().filter(e->{
                    if(param.getBomType() == 0){
                        return e.getBomId().equals(item.getId());
                    }else{
                        return e.getBomPackId().equals(item.getId());
                    }
                }).count()
            );

            if (param.getBomType() == 0) {
                item.setPackIds(
                    bomEnds.stream()
                    .filter(e->e.getBomId().equals(item.getId()))
                    .map(SysBomEnd::getBomPackId)
                    .collect(Collectors.toList())
                );
            }

        }

        return sysBoms;
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public SysBom addBom(SysBomParam sysBomParam) {

        if (StrUtil.isEmpty(sysBomParam.getBomPartName())) {
            throw new ServiceException(500, "请选择物料");
        }
        SysPart sysPart = partService.getBySapNumber(sysBomParam.getBomPartName());
        if (null == sysPart) {
            throw new ServiceException(500, "物料不存在");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date now = new Date();

        TreeBom _item = TreeBom.builder()
                .id(GenerateUniqueIdUtil.getGuid())
                .open(true)
                .partName(sysPart.getPartName())
                .partDescription(sysPart.getPartDescription())
                .partUnit(sysPart.getPartUnit())
                .partClass(sysPart.getPartClass())
                .sapNumber(sysPart.getSapNumber())
                .partUse(1000)
                .sapPartUse(1000)
                .partLoss(0)
                .partNumber(sysPart.getPartNumber())
                .partGroup("")
                .posnr("")
                .desc("")
                .version("")
                .substitute(new ArrayList<>())
                .validate(true)
                .lists(new ArrayList<>()).build();

        ProjectDetail detail = productJiraService.getProjectDetail(DocParams.builder().issueId(sysBomParam.getBomIssueId()).build());

        List<Integer> states =  Arrays.asList(1,7,8);


        if (states.indexOf(detail.getState()) > -1) {
            Map<Integer,String> tips = new HashMap<Integer,String>(3){{
                put(1,"立项评审阶段");
                put(7,"TBD阶段");
                put(8,"停产阶段");
            }};
            throw new ServiceException(500, tips.get(detail.getState())+"无法搭建BOM");
        }

        int mStatus = detail.getMStatus();
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, 1522118152715505666L);
        queryWrapper.eq(SysDictData::getCode, mStatus);
        String value = dictDataService.list(queryWrapper).get(0).getValue();

        SysBom sysBom = SysBom.builder()
            .id(IdUtil.getSnowflake(1, 1).nextId())
            .bomUse("1")
            .bomNo(null)
            .bomSourceId(0L)
            .bomIssueId(sysBomParam.getBomIssueId())
            .bomStartdate(sdf.format(now))
            .bomStatus(2L)
            .bomType(sysBomParam.getBomType())
            .bomData(JSONObject.toJSONString(Arrays.asList(_item)))
            .bomCtype(detail.getState() == 4 ? "1" : (detail.getState() == 5 ? "0" : "2"))
            .bomProductType(detail.getProductType())
            .productProjectName(detail.getProductProjectName())
            .manager(detail.getProductManager())
            .productState(detail.getState())
            .bomJiraNo(IdWorker.getIdStr())
            .productStage(value)
            .lines(new ArrayList<>()).build();

        sysBom.setUpdateTime(now);
        this.save(sysBom);
        return sysBom;
    }


    //@Transactional(rollbackFor = Exception.class)
    @Override
    public SysBom add(SysBomParam sysBomParam) {
        Date now = new Date();
        ProjectDetail detail = productJiraService.getProjectDetail(DocParams.builder().issueId(sysBomParam.getBomIssueId()).build());
        List<Integer> states = Arrays.asList(1,2,7,8);

        if (states.indexOf(detail.getState()) != -1) {
            throw new ServiceException(500, "B样前以及暂停状态禁止创建BOM");
        }

        int mStatus = detail.getMStatus();
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, 1522118152715505666L);
        queryWrapper.eq(SysDictData::getCode, mStatus);
        String value = dictDataService.list(queryWrapper).get(0).getValue();

        SysBom sysBom = SysBom.builder()
                .id(IdUtil.getSnowflake(1, 1).nextId())
                .bomUse("1")
                .bomSourceId(0L)
                .bomIssueId(sysBomParam.getBomIssueId())
                .bomStartdate(sysBomParam.getBomStartdate())
                .bomStatus(0L)
                .bomType(sysBomParam.getBomType())
                .bomTransport(sysBomParam.getBomTransport())
                .bomData(sysBomParam.getBomData())
                .bomCtype(sysBomParam.getProductState() == 4 ? "1" : (sysBomParam.getProductState() == 5 ? "0" : "2"))
                .bomProductType(detail.getProductType())
                .productProjectName(detail.getProductProjectName())
                .manager(detail.getProductManager())
                .productState(sysBomParam.getProductState())
                .bomJiraNo(IdWorker.getIdStr())
                .productStage(value)
                .lines(new ArrayList<>())
                .lines(new ArrayList<>()).build();

        sysBom.setUpdateTime(now);
        this.save(sysBom);
        sysBom.setCanImport( sysBom.getProductState() < 4 ? 0 : 1);
        sysBom.setLastProductState( detail.getState() == 3 ? 3 : 4);
        sysBom.setIsCheck(false);
        return sysBom;
    }

    @Override
    public void delBom(SysBomParam param) {
        SysBomHistory item =  bomHistoryService.getLastHistory(param.getId());
        if (null != item) {
            throw new ServiceException(500, "BOM有提交历史记录,不可删除");
        }
        this.baseMapper.deleteById(param.getId());
        bomLineService.deleteByBomId(SysBomLineParam.builder().bomId(param.getId()).build());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long edit(SysBomParam sysBomParam) {
        SysBomSapServiceUtil sapServiceUtil = new SysBomSapServiceUtil();
        List<TreeBom> treeBoms = JSON.parseArray(sysBomParam.getBomData(),TreeBom.class);
        sapServiceUtil.loopSaveSapPartUse(treeBoms);
        sysBomParam.setBomData(JSON.toJSONString(treeBoms));
        SysBom sysBom = new SysBom();
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBomParam.getId());

        if(null == sysBomHistory && null != sysBomParam.getBomName()){

            String[] spits = sysBomParam.getBomName().split("BOM-");
            sysBomParam.setBomName(spits[0]+"BOM-"+treeBoms.get(0).getSapNumber()+spits[1].substring(8));
            
        }
        BeanUtil.copyProperties(sysBomParam, sysBom);
        this.updateById(sysBom);
        return sysBomParam.getId();
    }

    @Override
    public SysBom get(Long id) {
        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getId, id);
        SysBom bom = this.getOne(queryWrapper);
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(bom.getId());
        bom.setCanImport(null != sysBomHistory || bom.getProductState() < 4 ? 0 : 1);
        bom.setLastProductState(null != sysBomHistory ? sysBomHistory.getProductState().intValue() : 0);
        bom.setIsCheck(null != sysBomHistory ? true : false);
        List<SysBomLine> sysBomLines = bomLineService.getList(SysBomLineParam.builder().bomIssueId(bom.getBomIssueId()).build());
        Map<Long, List<Long>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(SysBomLine::getBomId,Collectors.mapping(SysBomLine::getLineId, Collectors.toList())));
        bom.setLines(new ArrayList<>());
        bom.getLines().addAll( lineMap.getOrDefault(bom.getId(),new ArrayList<>()));
        return bom;
    }

    @Override
    public List<SapWerks> getWerks() {

        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("WERK_CODE").build());

        Map<String, String> werkMap = new HashMap<>();

        Map<String,String> buMap = new HashMap<>();

        List<SapWerks> werks = new ArrayList<>();
        if (null != sysconfig.getText()) {

            werks = JSONObject.parseArray(sysconfig.getText(), SapWerks.class);

            werks.forEach(e -> {
                werkMap.put(e.getWERKS(), e.getNAMECODE());
                buMap.put(e.getWERKS(),e.getBU());
            });
        }

        JSONObject sapResp = BomUtils.doGet(SapApiParam.BaseUrl + SapApiParam.FactoryApi, SapApiParam.Appkey, null);
        if (!sapResp.getBoolean("state")) {
            return werks;
        }

        List<SapWerks> sapWerks = JSONObject.parseArray(sapResp.getString("rows"), SapWerks.class);


        sapWerks.forEach(e -> {
            e.setNAMECODE(werkMap.containsKey(e.getWERKS()) ? werkMap.get(e.getWERKS()) : "");
            e.setBU(buMap.containsKey(e.getWERKS()) ? buMap.get(e.getWERKS()) : "");
        });

        sapWerks.add(SapWerks.builder().BUKRS("").WERKS("").NAMECODE("").NAME1("中试线").FRESFACTORY("1").build());

        Optional<SapWerks> one = werks.stream().filter(e->e.getNAME1().equals("中试线")).findFirst();

        if (one.isPresent()) {
            sapWerks.get(sapWerks.size()-1).setWERKS(one.get().getWERKS());
            sapWerks.get(sapWerks.size()-1).setNAMECODE(one.get().getNAMECODE());
        }

        sysconfig.setText(JSONObject.toJSONString(sapWerks));

        sysConfigService.saveOne(sysconfig);

        return sapWerks;
    }


    @Override
    public List<BuConfig> getBuConfig() {

        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("BU_CONFIG").build());

        List<BuConfig> buLists = new ArrayList<>();
        if (null != sysconfig.getText()) {

            buLists = JSONObject.parseArray(sysconfig.getText(), BuConfig.class);

        }

        return buLists;
    }

    @Override
    public List<OaAddOrEditParam> getLastHistory(Long bomId) {
        SysBom sysBom = this.get(bomId);

        List<TreeBom> newTreeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        List<TreeBom> oldTreeBoms = new ArrayList<>();

        SysBomHistory sysBomHistory = sysBom.getProductState() < 4 ? bomHistoryService.getCountByBomId(sysBom.getId())
                : bomHistoryService.getLastOne(sysBom.getId());

        if (null != sysBomHistory) {
            oldTreeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);

        }

        SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
        Map<String, TreeBom> flagTreeMap = new HashMap<>();
        Map<String, TreeBom> mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
        List<TreeBom> newBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, newTreeBoms, mapTreeBoms,
                newBoms, flagTreeMap);
        oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));

        Map<String,List<OaAddOrEditParam>> paramsMap = oaAddOrEditParams.stream()
        .filter(e -> null != e)
        .filter(e->e.getFlag().equals("新增") || e.getFlag().equals("删除"))
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(),
                Collectors.toList()
            )
        )
        .entrySet().stream()
        .filter(e->e.getValue().size() > 1)
        .map(val->val.getValue())
        .flatMap(val->val.stream())
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(),
                Collectors.mapping(item->OaAddOrEditParam.builder().id(item.getId()).flag(item.getFlag()).build(), Collectors.toList())
            )
        );

        List<String> ids = new ArrayList<>();

        for (Map.Entry<String,List<OaAddOrEditParam>> entry : paramsMap.entrySet()) {
            List<OaAddOrEditParam> delList = entry.getValue().stream().filter(e->e.getFlag().equals("删除")).collect(Collectors.toList());
            List<OaAddOrEditParam> addList = entry.getValue().stream().filter(e->e.getFlag().equals("新增")).collect(Collectors.toList());
            int minCount = delList.size() < addList.size() ? delList.size() : addList.size();
            for (int i = 0,j = minCount; i < j; i++) {
                ids.add(delList.get(i).getId());
                ids.add(addList.get(i).getId());
            }
        }
        return oaAddOrEditParams.stream().filter(e -> null != e).filter(e-> ids.indexOf(e.getId()) < 0).collect(Collectors.toList());
    }

    @Override
    public Long copy(BomCopyParam param) {

        Boolean targetIdExit = Optional.ofNullable(param).map(BomCopyParam::getTargetId).isPresent();

        if (targetIdExit) {
            SysBomHistory countByBomId = bomHistoryService.getCountByBomId(param.getTargetId());
            if (null != countByBomId) {
                throw new ServiceException(500, "提交过的版本不能复制");
            }
        }
        SysBom sourceBom = this.get(param.getSourceId());

        ProjectDetail detail = productJiraService.getProjectDetail(
                DocParams.builder().issueId(sourceBom.getBomIssueId()).build());

        SysBom tempBom = SysBom.builder()
        .id(IdUtil.getSnowflake(1, 1).nextId())
        .bomUse("1")
        .bomSourceId(0L)
        .bomIssueId(sourceBom.getBomIssueId())
        .bomStartdate(sourceBom.getBomStartdate())
        .bomStatus(0L)
        .bomType(sourceBom.getBomType())
        .bomTransport(sourceBom.getBomTransport())
        .bomCtype(sourceBom.getProductState() == 4 ? "1" : (sourceBom.getProductState() == 5 ? "0" : "2"))
        .bomProductType(detail.getProductType())
        .productProjectName(sourceBom.getProductProjectName())
        .manager(detail.getProductManager())
        .productState(sourceBom.getProductState())
        .bomJiraNo(IdWorker.getIdStr())
        .productStage(sourceBom.getProductStage())
        .lines(new ArrayList<>())
        .lines(new ArrayList<>()).build();

        SysBom targetBom = targetIdExit ? this.get(param.getTargetId()) : tempBom;

        List<TreeBom> treeBomList = JSONObject.parseArray(sourceBom.getBomData(), TreeBom.class);

        clearVersion(treeBomList);

        targetBom.setBomData(JSON.toJSONString(treeBomList));


        if (targetIdExit) {
            updateById(targetBom);
        }else{
            this.save(targetBom);
        }

        return targetBom.getId();
    }



    public void clearVersion(List<TreeBom> treeBomList) {
        for (int i = 0; i < treeBomList.size(); i++) {
            treeBomList.get(i).setPosnr("");
            treeBomList.get(i).setVersion("");
            List<TreeBom> lists = treeBomList.get(i).getLists();
            if (!ObjectUtil.isEmpty(lists)) {
                clearVersion(lists);
            }
        }
    }

    @Override
    //@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Boolean receiveFromJIRA(ReceiveJIRAParam param) throws IOException {
        SysBom sysBom = this.get(Long.parseLong(param.getBomId()));

        if (null != sysBom) {

            if ("approve".equals(param.getProcessStatus())) {
                try {
                    /*
                     * URL url = new URL(param.getFileLink());
                     *
                     * HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                     *
                     * conn.setRequestMethod("GET");
                     * conn.setRequestProperty("Authorization", "Basic aGt0eDpoa3R4MjAxNQ==");
                     *
                     * inputStream = conn.getInputStream();
                     * String name = "jira.pdf";
                     * MultipartFile multipartFile = new MockMultipartFile(name, name, null,
                     * inputStream);
                     * Long fileId = sysFileInfoService.uploadFile(multipartFile,
                     * FileConfig.BOM_JIRA);
                     * sysBom.setFileId(fileId);
                     */

                    ReceiveNameParam receiveNameParam = new ReceiveNameParam();
                    try {

                        List<ReceiveNameInParam> re = new ArrayList<>();

                        String[] split = param.getReviewer().split(",");
                        String[] split1 = param.getReviewerId().split(",");
                        if (null != param.getReviewerDate()) {
                            receiveNameParam.setReviewerDate(Arrays.asList(param.getReviewerDate().split(",")));
                        }
                        if (null != param.getControlledDate()) {
                            receiveNameParam.setApproverDate(
                                    DateUtil.format(new Date(Long.parseLong(param.getControlledDate())), "yyyy-MM-dd"));
                        }
                        for (int i = 0; i < split.length; i++) {
                            re.add(ReceiveNameInParam.builder().name(split[i]).id(split1[i]).build());
                        }
                        ReceiveNameInParam approve = ReceiveNameInParam.builder().name(param.getApprover())
                                .id(param.getApproverId()).build();

                        receiveNameParam.setApprover(approve);
                        receiveNameParam.setReviewer(re);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    sysBom = this.getById(sysBom.getId());
                    Long fileId = sysBom.getFileId();

                    List<TreeBom> newTreeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

                    List<TreeBom> oldTreeBoms = new ArrayList<>();

                    SysBomHistory sysBomHistory = bomHistoryService.getCountByBomId(sysBom.getId());

                    LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());

                    List<SysBomHistory> list = bomHistoryService.list(queryWrapper);

                    if (null != sysBomHistory) {
                        oldTreeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);

                    }

                    SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
                    Map<String, TreeBom> flagTreeMap = new HashMap<>();
                    Map<String, TreeBom> mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
                    List<TreeBom> newBoms = new ArrayList<>();
                    List<OaAddOrEditParam> oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, newTreeBoms,
                            mapTreeBoms, newBoms, flagTreeMap);
                    oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));
                    oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).collect(Collectors.toList());
                    SysBom finalSysBom = sysBom;
                    oaAddOrEditParams.forEach(a -> a.setBomNo(finalSysBom.getBomNo()));

                    List<SysBomLine> sysBomLines = bomLineService
                            .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

                    List<SysWerkLine> werkLines = ObjectUtil.isEmpty(sysBomLines) ? new ArrayList<>()
                            : werkLineService
                                    .getWerks(sysBomLines.stream().map(SysBomLine::getLineId)
                                            .collect(Collectors.toList()));

                    // SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();
                    // sysBomServiceUtil.loopSaveSapPartUse(newTreeBoms);
                    sysBom.setBomData(JSONObject.toJSONString(newTreeBoms));

                    SysBomHistoryParam bomHistoryParam = new SysBomHistoryParam();
                    bomHistoryParam.setBomData(sysBom.getBomData());
                    bomHistoryParam.setBomId(sysBom.getId());
                    bomHistoryParam.setProductState(sysBom.getProductState());
                    bomHistoryParam.setCreateTime(new Date());
                    bomHistoryParam.setBomNo(sysBom.getBomNo());
                    bomHistoryParam.setBomVersion(sysBom.getBomVersion());
                    bomHistoryParam.setBomName(sysBom.getBomName());
                    bomHistoryParam.setRemark(sysBom.getBomRemark());
                    BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams)
                            .werkLines(werkLines)
                            .build();
                    bomHistoryParam.setBomChange(JSONObject.toJSONString(bomChangeParam));
                    bomHistoryParam.setFileId(fileId);
                    bomHistoryParam.setType(list.size() != 0 ? 0 : 1);

                    bomHistoryParam.setSummitor(sysBom.getSummitor());

                    sysBom.setBomStatus(2L);

                    sysBom.setApprover(param.getApprover());
                    sysBom.setReviewer(param.getReviewer());

                    Long historyId = bomHistoryService.add(bomHistoryParam);

                    try {
                        Map map = this.pdfUpdateAndCode( sysBom.getId(), false, sysBom.getBomRemark(),null,
                                receiveNameParam);
                        System.err.println();
                        SysBom sysBom1 = this.get(sysBom.getId());

                        Map map1 = new HashMap();

                        map1.put("issueid", sysBom1.getChildIssueId());
                        map1.put("url",
                                sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build())
                                        .getValue() + sysBom1.getFileId());

                        JSONObject jsonObject = Utils.doPostAndToken(
                                "http://jira.evebattery.com/rest/jirareport/1.0/product/file/issue/add",
                                JiraApiParams.Token,
                                map1);

                        log.info("JIRA响应：" + JSON.toJSONString(jsonObject));

                        SysBomHistory sysBomHistory1 = new SysBomHistory();
                        sysBomHistory1.setId(historyId);
                        sysBomHistory1.setFileId(sysBom1.getFileId());

                        fileId = sysBom1.getFileId();

                        this.bomHistoryService.updateById(sysBomHistory1);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    bomPushService.update(sysBom, 4, fileId, null, sysBom.getBomNo());

                    bomPushService.add(sysBom, false, true, true, "审核成功", "JIRA",
                            sysBom.getBomNo(), null, fileId, null);
                    return this.updateById(sysBom);
                } catch (Exception e) {
                    log.error(e.getMessage());
                    return false;
                } finally {

                }
                // 驳回
            } else {
                sysBom.setBomStatus(4L);
                sysBom.setJiraIdStatus(0);
                bomPushService.update(sysBom, 1, null, null, sysBom.getBomNo());

                bomPushService.add(sysBom, false, true, true, "驳回", "JIRA",
                        "结束", null, null, null);
                return this.updateById(sysBom);

            }

        } else {

            if ("approve".equals(param.getProcessStatus())) {
                SysBomEnd bomEnd = sysBomEndService.getById(Long.parseLong(param.getBomId()));
                bomEnd.setBomRelateStatus(2);

                SysBom sysBom1 = this.get(bomEnd.getBomId());
                bomPushService.updateEnd(bomEnd, 4, null, null, "结束");

                bomPushService.addEnd(bomEnd, sysBom1, false, true, true, "审核成功", "JIRA",
                        "结束", null, null, null);

                return this.sysBomEndService.updateById(bomEnd);
            } else {
                SysBomEnd bomEnd = sysBomEndService.getById(Long.parseLong(param.getBomId()));
                bomEnd.setBomRelateStatus(7);

                // 变更记录
                List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
                SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
                List<TreeBom> treeBoms = JSONObject.parseArray(bomEnd.getBomData(), TreeBom.class);
                oaAddOrEditParams = sysBomChangeUtil.addEndBoms(bomEnd, treeBoms);
                List<SysWerkLine> werkLines = werkLineService
                        .getWerks(JSONObject.parseArray(bomEnd.getBomLines(), Long.class));
                sysBomEndService.addHistory(bomEnd, oaAddOrEditParams, werkLines, 0);

                SysBom sysBom1 = this.get(bomEnd.getId());
                bomPushService.updateEnd(bomEnd, 1, null, null, "");

                bomPushService.addEnd(bomEnd, sysBom1, false, true, true, "驳回", "JIRA",
                        "结束", null, null, null);

                return this.sysBomEndService.updateById(bomEnd);
            }

        }

    }

    @Override
    public ReceiveNameParam getNameFromJIRA(SysBom bom) {
        ReceiveNameParam receiveNameParam = new ReceiveNameParam();
        try {

            JSONObject jsonObject = Utils.doPostAndToken(
                    "http://jira.evebattery.com/rest/jirareport/1.0/product/file/get/approvalRole?issueId="
                            + bom.getBomIssueId() + "&fileType=" +
                            (bom.getBomType() == 0 ? "电芯BOM" : "包装BOM"),
                    JiraApiParams.Token,
                    new HashMap<>());

            log.info("jira获取审核人返回值" + JSON.toJSONString(jsonObject));

            if (jsonObject.getBoolean("result")) {
                String format = DateUtil.format(new Date(), "yyyy-MM-dd");
                JSONObject value = JSON.parseObject(jsonObject.getString("value"));
                String reviewerId = value.getString("reviewerId");
                String reviewer = value.getString("reviewer");
                String approverId = value.getString("approverId");
                String approver = value.getString("approver");
                List<ReceiveNameInParam> re = new ArrayList<>();
                List<String> dates = new ArrayList<>();

                String[] split = reviewer.split(",");
                String[] split1 = reviewerId.split(",");

                for (int i = 0; i < split.length; i++) {
                    re.add(ReceiveNameInParam.builder().name(split[i]).id(split1[i]).build());

                    dates.add(format);
                }

                ReceiveNameInParam approve = ReceiveNameInParam.builder().name(approver).id(approverId).build();
                receiveNameParam.setApprover(approve);
                receiveNameParam.setReviewer(re);
                receiveNameParam.setReviewerDate(dates);
                receiveNameParam.setApproverDate(format);

                bom.setApprover(approver);
                bom.setReviewer(reviewer);

                return receiveNameParam;
            } else {
                return null;
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private ReceiveNameParam getEndBomNameFromJIRA(SysBomEnd bom) {
        ReceiveNameParam receiveNameParam = new ReceiveNameParam();
        try {

            JSONObject jsonObject = Utils.doPostAndToken(
                    "http://jira.evebattery.com/rest/jirareport/1.0/product/file/get/approvalRole?issueId="
                            + bom.getBomIssueId() + "&fileType=成品BOM",
                    JiraApiParams.Token,
                    new HashMap<>());

            log.info("jira获取审核人返回值" + JSON.toJSONString(jsonObject));

            if (jsonObject.getBoolean("result")) {
                String format = DateUtil.format(new Date(), "yyyy-MM-dd");
                JSONObject value = JSON.parseObject(jsonObject.getString("value"));
                String reviewerId = value.getString("reviewerId");
                String reviewer = value.getString("reviewer");
                String approverId = value.getString("approverId");
                String approver = value.getString("approver");
                List<ReceiveNameInParam> re = new ArrayList<>();
                List<String> dates = new ArrayList<>();

                String[] split = reviewer.split(",");
                String[] split1 = reviewerId.split(",");

                for (int i = 0; i < split.length; i++) {
                    re.add(ReceiveNameInParam.builder().name(split[i]).id(split1[i]).build());

                    dates.add(format);
                }

                ReceiveNameInParam approve = ReceiveNameInParam.builder().name(approver).id(approverId).build();
                receiveNameParam.setApprover(approve);
                receiveNameParam.setReviewer(re);
                receiveNameParam.setReviewerDate(dates);
                receiveNameParam.setApproverDate(format);

                return receiveNameParam;
            } else {
                return null;
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Boolean ABPushToJIRA(Long id, String token) throws IOException {

        SysBom bom = get(id);

        Map map = this.jiraData(id, true);

        map.remove("fileId");
        map.put("id", bom.getJiraId());
        map.put("fileVersion", bom.getBomVersion());
        map.put("fileName", bom.getBomName());
        map.put("fileCode", bom.getBomNo());
        // String[] split = bomNo.split("-");
        // String num = split[split.length-1].substring(1,
        // split[split.length-1].length() - 1);
        String[] split = bom.getBomNo().split("-");
        map.put("serialNumber", split[split.length - 1].substring(1, split[split.length - 1].length() - 1));

        System.err.println("AB样推送参数：" + JSON.toJSONString(map));

        JSONObject jsonObject = Utils.doPostAndToken(
                "http://jira.evebattery.com/rest/jirareport/1.0/product/file/upload/bom", token,
                map);

        if (!jsonObject.getBoolean("result")) {
            log.error("推送jira失败,响应结果" + jsonObject);
            throw new ServiceException(500, "推送jira失败");
        }

        JSONObject value = JSONObject.parseObject(jsonObject.getString("value"));

        bom.setBomStatus(1L);
        bom.setChildIssueId(value.getLong("issueId"));
        bom.setJiraIdStatus(1);

        Long processId = IdUtil.getSnowflake(1, 8).nextId();

        bomPushService.add(bom, true, false, false, "提交审核", LoginContextHolder.me().getSysLoginUser().getName(),
                "JIRA", value.getString("issueKey"), null, processId);

        bomPushService.add(bom, value.getString("issueKey"), processId, null);

        return this.updateById(bom);
    }

    @Override
    public void ABEndPushToJIRA(Long id, String token) throws IOException {

        SysBomEnd bom = sysBomEndService.getById(id);

        Map map = this.endBomPdfUpdate(bom.getId());

        map.remove("fileId");

        System.err.println("AB样成品bom推送参数：" + JSON.toJSONString(map));

        JSONObject jsonObject = Utils.doPostAndToken(
                "http://jira.evebattery.com/rest/jirareport/1.0/product/file/upload/bom", token,
                map);

        if (!jsonObject.getBoolean("result")) {
            log.error("推送jira失败,响应结果" + jsonObject);
            throw new ServiceException(500, "推送jira失败");
        }

        JSONObject value = JSONObject.parseObject(jsonObject.getString("value"));

        Long processId = IdUtil.getSnowflake(1, 8).nextId();
        SysBom sysBom = this.get(bom.getBomId());

        bom.setBomRelateStatus(1);
        this.sysBomEndService.updateById(bom);

        bomPushService.addEnd(bom, sysBom, true, false, false, "提交审核",
                LoginContextHolder.me().getSysLoginUser().getName(),
                "JIRA", value.getString("issueKey"), null, processId);

        bomPushService.addEnd(bom, sysBom, value.getString("issueKey"), processId, null);

    }

    @Override
    public JSONObject CDPushToJIRA(Long id) throws ServiceException, IOException {

        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(ra)) {
            log.info("服务里RequestAttributes对象为空");
            throw new ServiceException(500, "jira登录异常");
        }
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        String token = request.getHeader("JiraAuth");

        SysBom sysBom = this.get(id);

        Map map = null;

        try {
            map = this.jiraData(id, true);
        } catch (Exception e) {
            throw new ServiceException(500, e.getMessage());
        }

        map.remove("fileId");

        map.put("id", sysBom.getJiraId());

        JSONObject jsonObject = Utils.doPostAndToken(
                "http://jira.evebattery.com/rest/jirareport/1.0/product/file/upload/bom", token,
                map);

        if (!jsonObject.getBoolean("result")) {
            log.error("推送jira失败,响应结果" + jsonObject);
            throw new ServiceException(500, JSONObject.toJSONString(jsonObject));
        }
        InputStream inputStream = null;
        JSONObject value = null;
        try {
            value = JSONObject.parseObject(jsonObject.getString("value"));

            URL url = new URL(value.getString("fileLink"));

            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("GET");
            conn.setRequestProperty("Authorization", "Basic aGt0eDpoa3R4MjAxNQ==");

            inputStream = conn.getInputStream();
            String name = "jira.pdf";
            MultipartFile multipartFile = new MockMultipartFile(name, name, null, inputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_JIRA);
            sysBom.setFileId(fileId);
            sysBom.setJiraIdStatus(1);
            this.updateById(sysBom);
        } catch (Exception e) {
            log.error("jira回写文件失败", e);
            throw new ServiceException(500, "jira回写文件失败");
        } finally {
            inputStream.close();
        }

        return value;

    }

    /*
     * cd样OA新增
     *
     * @param id
     *
     * @return
     */
    @Override
    public void OaAdd(SysBomParam param,Long id, List<Long> lineIds) {
        try {

            SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();

            SysBom sysBom = get(id);

            List<SysBomLine> sysBomLines = bomLineService
                    .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

            if (null == sysBomLines || sysBomLines.isEmpty()) {
                throw new ServiceException(500, "请选择产线");
            }

            List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
            /* if (sysBomServiceUtil.ifMengeEmpty(treeBoms)) {
                log.error("BOM使用量不为0");
                throw new ServiceException(500, "BOM使用量不为0");
            } */

            SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());

            List<TreeBom> oldTreeBoms = new ArrayList<>();

            /*List<SysWerkLine> werkLines = werkLineService
                    .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

            List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).collect(Collectors.toList());


             List<BomSapProof> proofs = sysBomServiceUtil.getSapExendProof(treeBoms,werkNos);

            if (proofs.size() > 0) {
                param.setProofs(proofs);
                return;
            } */
            // 判断是否c样新增到oa
            if (null != sysBomHistory) {

                oldTreeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);

                /*
                 * bom变更升版提示
                 */
                List<TreeBom> newBoms = new ArrayList<>();
                Map<String, TreeBom> flagTreeMap = new HashMap<>();
                Map<String, TreeBom> mapTreeBoms = sysBomServiceUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);

                sysBomServiceUtil.loopNewTreeBoms(sysBom, treeBoms, mapTreeBoms,newBoms, "", flagTreeMap);

                List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(sysBom, newBoms, "");

                if (!LoginContextHolder.me().isSuperAdmin() && sapAddBomParams.size() > 0) {
                    if (sysBom.getAdminComfirm().equals(0)) {
                        throw new ServiceException(500, "BOM变更有升版风险，请联系管理员进行确认");
                    }
                }
                /*
                 * bom变更升版提示 end
                 */

                sysBomServiceUtil.sapProof(sysBom, oldTreeBoms);
            }

            MultiValueMap<String, Object> stringObjectMultiValueMap = getMultiValueMap(sysBom, treeBoms, oldTreeBoms,
                    sysBomHistory, lineIds);

            System.err.println(JSON.toJSONString(stringObjectMultiValueMap));

            SysBomChangeUtil sysOaUtil = new SysBomChangeUtil();
            String s = sysOaUtil.pushToOA(stringObjectMultiValueMap);
            System.err.println(s);

            sysBom.setOaId(s);
            sysBom.setBomStatus(1L);
            this.updateById(sysBom);

            SysBom afterUpdate = this.get(sysBom.getId());

            Long processId = IdUtil.getSnowflake(1, 8).nextId();
            bomPushService.add(sysBom, s, processId, new Integer(1).equals(afterUpdate.getBomIfAdd()) ? "增加工厂"
                    : (new Integer(2).equals(afterUpdate.getBomIfAdd()) ? "减少工厂" : null));
            bomPushService.add(sysBom, true, false, false, "提交审核",
                    LoginContextHolder.me().getSysLoginUser().getName(), "OA", s, null, processId);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(403, e.getMessage());
        }

    }

    public MultiValueMap<String, Object> getMultiValueMap(SysBom sysBom, List<TreeBom> treeBoms,
            List<TreeBom> oldTreeBoms, SysBomHistory sysBomHistory, List<Long> lineIds) {
        SysBomOaServiceUtil sysBomOaServiceUtil = new SysBomOaServiceUtil();
        if (1 == sysBom.getBomIfAdd()) {
            return sysBomOaServiceUtil.loopTreeBom2(sysBom, oldTreeBoms);
        }
        if (2 == sysBom.getBomIfAdd()) {
            return sysBomOaServiceUtil.loopDelWerkTreeBoms(sysBom, oldTreeBoms, lineIds);
        }

        if (null == sysBomHistory) {
            return sysBomOaServiceUtil.loopTreeBom2(sysBom, treeBoms);
        }
        return sysBomOaServiceUtil.loopNewTreeBoms(sysBom, treeBoms, oldTreeBoms);
    }

    /**
     * cd样OA新增
     *
     * @param id
     * @return
     */
    @Override
    public Boolean endBomOaAdd(SysBomEnd param,Long id, List<SysWerkLine> sysWerkLines) {

        try {

            SysBomOaServiceUtil sysBomOaServiceUtil = new SysBomOaServiceUtil();

            SysBomEnd sysBom = sysBomEndService.getById(id);

            List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            // 电芯bom
            Long bomId = sysBom.getBomId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(SysBomHistory::getBomId, bomId);
            queryWrapper1.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> bomHistoryList = bomHistoryService.list(queryWrapper1);

            SysBom batteryBom = this.get(bomId);

            TreeBom bom = null;

            if (bomHistoryList.size() > 0) {
                bom = JSONObject.parseArray(bomHistoryList.get(bomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);
            } else {
                throw new ServiceException(500, "关联的电芯bom还未通过审核");
            }

            // 包装bom
            Long bomPackId = sysBom.getBomPackId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(SysBomHistory::getBomId, bomPackId);
            queryWrapper2.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> packBomHistoryList = bomHistoryService.list(queryWrapper2);

            TreeBom pack = null;

            if (packBomHistoryList.size() > 0) {
                pack = JSONObject
                        .parseArray(packBomHistoryList.get(packBomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);
            } else {
                throw new ServiceException(500, "关联的包装bom还未通过审核");
            }

            //List<TreeBom> inBom = new ArrayList<>();
            //inBom.add(bom);
            //inBom.add(pack);

            String code = "";
            String version = "";
            try {
                code += bomHistoryList.get(bomHistoryList.size() - 1).getBomNo() + "-";
                String[] split1 = packBomHistoryList.get(packBomHistoryList.size() - 1).getBomNo().split("-");
                String[] split = bomHistoryList.get(bomHistoryList.size() - 1).getBomNo().split("-");
                code += split1[split1.length - 1];

                version = split[split.length - 1] + "-" +
                        split1[split1.length - 1];

            } catch (Exception e) {
                throw new ServiceException(500, "关联的电芯bom或包装bom还未通过审核");
            }

            List<SysWerkLine> werkLines = sysBom.getBomIfAdd() > 1
                    ? JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class)
                    : werkLineService
                            .getWerks(JSONObject.parseArray(sysBom.getBomLines(), Long.class));

            List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                    .collect(Collectors.toList());
            
            SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();

            List<BomSapProof> proofs = sysBomServiceUtil.getSapExendProof(treeBoms,werkNos);

            if (proofs.size() > 0) {
                param.setProofs(proofs);
            }

            List<String> lineNos = werkLines.stream().map(SysWerkLine::getLineName).distinct()
                    .collect(Collectors.toList());

            MultiValueMap<String, Object> stringObjectMultiValueMap = null;

            SysBomHistory lastHistory = bomHistoryService.getLastHistory(sysBom.getId());

            if (2 == sysBom.getBomIfAdd()) {
                stringObjectMultiValueMap = sysBomOaServiceUtil.loopEndDelWerkTreeBoms(sysBom, batteryBom, code,
                        treeBoms, werkLines);
            }
            else if(1 == sysBom.getBomIfAdd()){
                stringObjectMultiValueMap = sysBomOaServiceUtil.loopEndTreeBom(sysBom,batteryBom, code, version, werkNos,
                        lineNos,
                        treeBoms);
            }else {
                if (null == lastHistory) {
                    stringObjectMultiValueMap = sysBomOaServiceUtil.loopEndTreeBom(sysBom,batteryBom, code, version, werkNos,
                        lineNos,
                        treeBoms);
                }else{
                    List<TreeBom> oldTreeBoms = JSONObject.parseArray(lastHistory.getBomData(), TreeBom.class);
                    stringObjectMultiValueMap = sysBomOaServiceUtil.loopNewEndTreeBoms(sysBom, treeBoms, oldTreeBoms);
                }

            }

            log.error("成品BOM的OA参数:" + JSON.toJSONString(stringObjectMultiValueMap));

            SysBomChangeUtil sysOaUtil = new SysBomChangeUtil();
            String s = sysOaUtil.pushToOA(stringObjectMultiValueMap);
            System.err.println(s);

            sysBom.setOaId(s);
            sysBom.setBomRelateStatus(1);
            this.sysBomEndService.updateById(sysBom);


            Long processId = IdUtil.getSnowflake(1, 8).nextId();
            bomPushService.addEnd(sysBom, batteryBom, s, processId,
                    1 == sysBom.getBomIfAdd() ? "增加工厂" : (2 == sysBom.getBomIfAdd() ? "减少工厂" : (null == lastHistory ? "新增" : "变更")));

            return bomPushService.addEnd(sysBom, batteryBom, true, false, false, "提交审核",
                    LoginContextHolder.me().getSysLoginUser().getName(), "OA", s, null, processId);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(403, e.getMessage());
        }

    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void upgrade( SysBomParam sysBomParam) {
        SysBom sysBom = get(sysBomParam.getId());
        ProjectDetail detail = productJiraService.getProjectDetail(
                DocParams.builder().issueId(sysBom.getBomIssueId()).build());


        if(null != sysBomParam.getJiraIdStatus()){
            sysBom.setJiraIdStatus(sysBomParam.getJiraIdStatus());
        }

        sysBom.setBomVersion(sysBomParam.getBomVersion());
        sysBom.setJiraIdStatus(1);
        sysBom.setFileId(sysBomParam.getFileId());
        Long bomStatus = sysBom.getBomStatus();
        sysBom.setBomStatus(bomStatus.equals(7L) ? 2L : 0L);

        if (bomStatus.equals(7L)) {

            List<SysBomLine> sysBomLines = bomLineService
                    .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

            if (null == sysBomLines || sysBomLines.isEmpty()) {
                throw new ServiceException(500, "产线未选");
            }

            updateImportCodeVersionFileName(sysBom);

            List<SysWerkLine> werkLines = werkLineService
                    .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

            SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
            Map<String, TreeBom> mapTreeBoms = new HashMap<String, TreeBom>();
            List<TreeBom> newBoms = new ArrayList<>();
            List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();
            SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
            List<TreeBom> oldTreeBoms = null != sysBomHistory
                    ? JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class)
                    : null;
            List<TreeBom> treeBom = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            Map<String, TreeBom> flagTreeMap = new HashMap<>();
            mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
            oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, treeBom, mapTreeBoms, newBoms, flagTreeMap);
            oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));
            oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).collect(Collectors.toList());
            oaAddOrEditParams.forEach(a -> a.setBomNo(sysBom.getBomNo()));

            SysBomHistoryParam bomHistoryParam = new SysBomHistoryParam();
            bomHistoryParam.setType(1);
            bomHistoryParam.setBomData(sysBom.getBomData());
            bomHistoryParam.setBomId(sysBom.getId());
            bomHistoryParam.setRemark(sysBom.getBomRemark());

            bomHistoryParam.setProductState(detail.getState());
            bomHistoryParam.setCreateTime(new Date());
            oaAddOrEditParams.forEach(a -> a.setBomNo(sysBom.getBomNo()));
            BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams)
                    .werkLines(werkLines)
                    .build();
            bomHistoryParam.setBomChange(JSONObject.toJSONString(bomChangeParam));
            bomHistoryParam.setBomNo(sysBom.getBomNo());
            bomHistoryParam.setBomVersion(sysBom.getBomVersion());
            bomHistoryParam.setBomName(sysBom.getBomName());
            bomHistoryParam.setIsAddWerk(0);
            bomHistoryParam.setSummitor(sysBom.getSummitor());
            bomHistoryParam.setFileId(sysBomParam.getFileId());
            bomHistoryParam.setBomTransport(sysBom.getBomTransport());
            bomHistoryParam.setBomLines(JSONObject.toJSONString(sysBomLines));
            bomHistoryService.add(bomHistoryParam);
        }
        //更新项目阶段
        int mStatus = detail.getMStatus();
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, 1522118152715505666L);
        queryWrapper.eq(SysDictData::getCode, mStatus);
        String value = dictDataService.list(queryWrapper).get(0).getValue();
        sysBom.setProductStage(value);

        this.updateById(sysBom);
    }

    @Override
    public void werkALter2Oa(SysBomParam param) {

        SysBom sysBom = get(param.getId());

        if (sysBom.getBomStatus().equals(1L)) {
            throw new ServiceException(500, "BOM处于审核中状态,提交失败");
        }

        List<Integer> states = new ArrayList<Integer>(3){{
            add(1);
            add(2);
            add(3);
            add(7);
            add(8);
        }};


        if (sysBom.getProductState() == 3) {
            throw new ServiceException(500, "BOM样品阶段为B样，无法提交，请联系项目管理部");
        }

        if (states.indexOf(sysBom.getProductState()) != -1) {
            throw new ServiceException(500, "BOM样品阶段有误，请联系项目管理部");
        }

        if (sysBom.getBomStatus() != 5 && sysBom.getBomStatus() != 6) {
            throw new ServiceException(500, "BOM不是工厂变更，无法提交");
        }

        if (sysBom.getBomType().equals(1) && (null == sysBom.getBomTransport() || sysBom.getBomTransport().equals("[]")) ) {
            throw new ServiceException(500, "请选择运输方式");
        }

        ProjectDetail detail = productJiraService.getProjectDetail(
                DocParams.builder().issueId(sysBom.getBomIssueId()).build());

        //更新项目阶段
        int mStatus = detail.getMStatus();
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, 1522118152715505666L);
        queryWrapper.eq(SysDictData::getCode, mStatus);
        String value = dictDataService.list(queryWrapper).get(0).getValue();

        SysBom sysBom1 = SysBom.builder().id(param.getId()).build();
        sysBom1.setProductStage(value);
        sysBom1.setSummitor(LoginContextHolder.me().getSysLoginUser().getName());
        this.updateById(sysBom1);

        if (sysBom.getBomStatus() == 6) {
            List<SysWerkLine> _lines =JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);
            this.OaAdd(param,param.getId(), _lines.stream().map(SysWerkLine::getId).collect(Collectors.toList()));
            return;
        }

        this.OaAdd(param,param.getId(), null);
    }

    @Override
    public void commit(SysBomParam param) throws IOException {
        SysBom sysBom = get(param.getId());

        if (sysBom.getBomStatus().equals(1L)) {
            throw new ServiceException(500, "BOM处于审核中状态,提交失败");
        }

        List<OaAddOrEditParam> lastHistorys = getLastHistory(param.getId());

        if (null == lastHistorys || lastHistorys.isEmpty()) {
            throw new ServiceException(500, "当前无变更,提交失败");
        }

        if (sysBom.getBomType().equals(1) && (sysBom.getBomTransport().equals("[]") || null == sysBom.getBomTransport())) {
            throw new ServiceException(500, "包装bom请选择运输方式");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        ProjectDetail detail = productJiraService.getProjectDetail(
        DocParams.builder().issueId(sysBom.getBomIssueId()).build());

        List<Integer> states = new ArrayList<Integer>(3){{
            add(1);
            add(2);
            add(3);
            add(7);
            add(8);
        }};

        if (states.indexOf(detail.getState()) != -1) {
            throw new ServiceException(500, "产品C样前以及暂停状态禁止提交BOM");
        }
        if (sysBom.getProductState() == 3) {
            throw new ServiceException(500, "BOM样品阶段为B样，无法提交，请联系项目管理部");
        }

        if (states.indexOf(sysBom.getProductState()) != -1) {
            throw new ServiceException(500, "BOM样品阶段有误，请联系项目管理部");
        }

        this.OaAdd(param,param.getId(), null);

        //更新项目阶段
        int mStatus = detail.getMStatus();
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, 1522118152715505666L);
        queryWrapper.eq(SysDictData::getCode, mStatus);
        String value = dictDataService.list(queryWrapper).get(0).getValue();

        SysBom sysBom1 = get(param.getId());
        sysBom1.setBomRemark(param.getRemark());
        sysBom1.setBomStartdate(sdf.format(new Date()));
        sysBom1.setAlterType(param.getAlterType());
        sysBom1.setProductStage(value);
        sysBom1.setSummitor(LoginContextHolder.me().getSysLoginUser().getName());
        this.updateById(sysBom1);
    }

    public Map<String, List<String>> getLineMap(List<SysWerkLine> werkLines) {

        SysConfig sysconfig = sysConfigService.getByCode(SysConfigParam.builder().code("WERK_CODE").build());

        Map<String, String> werkMap = new HashMap<>();

        if (null != sysconfig.getText()) {

            List<SapWerks> werks = JSONObject.parseArray(sysconfig.getText(), SapWerks.class);

            werks.forEach(e -> {
                werkMap.put(e.getWERKS(), e.getNAMECODE());
            });
        }

        werkLines.forEach(_e -> {
            _e.setNamecode(werkMap.containsKey(_e.getWerkNo()) ? werkMap.get(_e.getWerkNo()) : "");
        });

        return werkLines.stream().collect(Collectors.groupingBy(e -> e.getWerkNo(),
                Collectors.mapping(_e -> _e.getNamecode() + ";" + _e.getLineName(), Collectors.toList())));

    }

    @Override
    public void bom2Sap(SysBom sysBom, ReceiveNameParam nameFromJIRA) throws ServiceException {

        try {

            List<SysBomLine> sysBomLines = bomLineService
                    .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

            if (null == sysBomLines || sysBomLines.isEmpty()) {
                throw new ServiceException(500, "产线未选");
            }

            List<SysWerkLine> werkLines = werkLineService
                    .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

            if (1 == sysBom.getBomIfAdd()) {
                addWerk2BOM2Sap(sysBom, werkLines);
                addCheckPush(sysBom, nameFromJIRA);
                return;
            }

            if (2 == sysBom.getBomIfAdd()) {
                deleteWerk2BOM2Sap(sysBom, werkLines);
                addCheckPush(sysBom, nameFromJIRA);
                return;
            }

            List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                    .collect(Collectors.toList());

            SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
            // 判断是否c样新增到sap
            if (null == sysBomHistory) {
                addBom2Sap(sysBom, werkNos, werkLines);
                addCheckPush(sysBom, nameFromJIRA);
                return;
            }
            editBom2Sap(sysBom, sysBomHistory, werkNos, werkLines, nameFromJIRA);
            addCheckPush(sysBom, nameFromJIRA);

        } catch (Exception e) {
            String operator = "";
            try {
                operator = LoginContextHolder.me().getSysLoginUser().getName();
            } catch (Exception e1) {
                log.info("无登录用户");
            }

            bomPushService.update(sysBom, 6, null, null, "SAP");
            bomPushService.add(sysBom, false, false, false, "提交失败", operator,
                    "提交SAP", null, null, null);

            throw e;
        }

    }

    private void addCheckPush(SysBom sysBom, ReceiveNameParam nameFromJIRA) {
        try {
            this.pdfUpdateAndCode( sysBom.getId(), false, sysBom.getBomRemark(), null, nameFromJIRA);
            SysBom afterUpdateBom = this.getById(sysBom.getId());
            bomPushService.update(sysBom, 4, afterUpdateBom.getFileId(), null, sysBom.getBomNo());
            bomPushService.add(afterUpdateBom, false, false, true, "提交成功",
                    "SAP",
                    sysBom.getBomNo(), null, afterUpdateBom.getFileId(), null);

            SysBomHistory lastHistory = bomHistoryService.getLastHistory(sysBom.getId());
            lastHistory.setFileId(afterUpdateBom.getFileId());
            bomHistoryService.updateById(lastHistory);

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void deleteWerk2BOM2Sap(SysBom sysBom, List<SysWerkLine> werkLines) {

        if (sysBom.getBomStatus().equals(3L)) {
            reDelWerk2BOM2Sap(sysBom);
            return;
        }

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
        List<TreeBom> treeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.updateLoopTreeBom(treeBoms, "");

        List<SysWerkLine> _werkLines = JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);
        Map<String, List<String>> _delWerklinesMap = getLineMap(_werkLines);
        Map<String, List<String>> werklinesMap = getLineMap(werkLines);
        List<String> editWerkNos = _werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        Map<String, String> vMap = new HashMap<String, String>();
        String resp = null;

        for (String _item : editWerkNos) {
            String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomJiraNo());
            List<String> delLines = _delWerklinesMap.get(_item);
            List<String> lines = werklinesMap.get(_item);
            lines.removeAll(delLines);
            String linesStr;

            if (null == lines || lines.isEmpty()) {
                linesStr = "";
            } else {
                String nameCode = lines.get(0).split(";")[0];
                lines = lines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
                linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                        + lines.stream().map(String::valueOf).collect(Collectors.joining(","));
            }

            sapEditBomParams.forEach(e -> {
                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (editWerkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);
                e.setIV_STKTX(linesStr);
            });

            for (SapEditBomParam item : sapEditBomParams) {
                log.info("删除工厂变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("删除工厂变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }
        }

        delWerk2Bom2SapStore(sysBom, treeBoms, _werkLines, editFails, edits, errors, new SysBomError());
    }

    public void reDelWerk2BOM2Sap(SysBom sysBom) {

        SysBomError sysBomError = bomErrorService.getByBomId(sysBom.getId());
        List<SapEditBomParam> sapEditBomParams = JSONObject.parseArray(sysBomError.getEditFails(),
                SapEditBomParam.class);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = JSONObject.parseArray(sysBomError.getEdits(), SapEditBomRespParam.class);
        List<String> errors = new ArrayList<>();
        String resp = null;

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();
        String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomJiraNo());

        if (sapEditBomParams != null && !sapEditBomParams.isEmpty())
            for (SapEditBomParam item : sapEditBomParams) {
                item.setIV_AENNR(IV_AENNR);
                log.info("工厂删除重试:" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("工厂删除重试回复:" + resp);
                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
        List<TreeBom> treeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        delWerk2Bom2SapStore(sysBom, treeBoms, null, editFails, edits, errors, sysBomError);
    }

    public void delWerk2Bom2SapStore(SysBom sysBom, List<TreeBom> treeBoms, List<SysWerkLine> _werkLines,
            List<SapEditBomParam> editFails, List<SapEditBomRespParam> edits, List<String> errors,
            SysBomError sysBomError) {
        // 获取删除工厂变更记录
        SysBomChangeUtil bomChangeUtil = new SysBomChangeUtil();
        List<OaAddOrEditParam> oaAddOrEditParams = null;

        if (null == sysBomError.getId()) {
            oaAddOrEditParams = bomChangeUtil.deleteBoms(sysBom, treeBoms);
        }

        if (editFails != null && !editFails.isEmpty()) {
            String erros = StringUtil.join(errors.toArray(), ";");
            sysBomError.setBomId(sysBom.getId());
            sysBomError.setAddFails("[]");
            sysBomError.setEditFails(JSONObject.toJSONString(editFails));
            sysBomError.setAdds("[]");
            sysBomError.setEdits(JSONObject.toJSONString(edits));
            sysBomError.setErrorMsg(erros);
            if (null != oaAddOrEditParams && !oaAddOrEditParams.isEmpty()) {
                BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams)
                        .werkLines(_werkLines)
                        .build();
                sysBomError.setBomChange(JSONObject.toJSONString(bomChangeParam));
            }
            bomErrorService.saveSysBomErrorParam(sysBomError);
            sysBom.setBomStatus(3L);
            this.updateById(sysBom);

            throw new ServiceException(500, erros);
        }

        // BOM_DATA去除工厂版本
        bomChangeUtil.loopDeleteVersion(treeBoms,
                _werkLines.stream().filter(e -> 1 == e.getFlag()).map(SysWerkLine::getWerkNo).distinct()
                        .collect(Collectors.toList()));

        sysBom.setBomData(JSONObject.toJSONString(treeBoms));
        sysBom.setBomIfAdd(0);
        sysBom.setBomAddWerks("");
        sysBom.setBomStatus(2L);
        this.updateById(sysBom);

        // 设置删除工厂变更记录
        SysBomHistoryParam bomHistoryParam = new SysBomHistoryParam();
        bomHistoryParam.setBomData(sysBom.getBomData());
        bomHistoryParam.setBomId(sysBom.getId());
        bomHistoryParam.setRemark(sysBom.getBomRemark() + "&&lineRemark&&" + sysBom.getLineRemark());
        bomHistoryParam.setProductState(sysBom.getProductState());
        bomHistoryParam.setCreateTime(new Date());
        if (null != oaAddOrEditParams && !oaAddOrEditParams.isEmpty()) {
            BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams).werkLines(_werkLines)
                    .build();
            bomHistoryParam.setBomChange(JSONObject.toJSONString(bomChangeParam));
        } else {
            bomHistoryParam.setBomChange(sysBomError.getBomChange());
        }
        bomHistoryParam.setBomNo(sysBom.getBomNo());
        bomHistoryParam.setBomVersion(sysBom.getBomVersion());
        bomHistoryParam.setBomName(sysBom.getBomName());
        bomHistoryParam.setType(6);
        bomHistoryParam.setSummitor(sysBom.getSummitor());
        bomHistoryParam.setIsAddWerk(2);
        bomHistoryParam.setFileId(sysBom.getFileId());
        bomHistoryParam.setBomTransport(sysBom.getBomTransport());
        List<SysBomLine> sysBomLines = bomLineService
                    .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());
        bomHistoryParam.setBomLines(JSONObject.toJSONString(sysBomLines));
        bomHistoryService.add(bomHistoryParam);

        // 删除工厂
        bomLineService.deleteByLineIds(SysBomLineParam.builder().bomId(sysBom.getId()).build(),
                _werkLines.stream().map(SysWerkLine::getId).collect(Collectors.toList()));
    }

    private List<SapAddBomParam> getVersionIfSapError(SysBom sysBom){

        List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        List<SysBom> boms = this.list(
            Wrappers.lambdaQuery(SysBom.class)
            .eq(SysBom::getBomIssueId, sysBom.getBomIssueId())
            .eq(SysBom::getBomType, sysBom.getBomType())
            .ne(SysBom::getId, sysBom.getId())
            .like(SysBom::getBomData, treeBoms.get(0).getSapNumber())
        );

        List<Long> bomIds = boms.stream().map(SysBom::getId).collect(Collectors.toList());

        if (bomIds.size() < 1) {
            return new ArrayList<>();
        }

        SysBomError bomError = bomErrorService.getOne(
            Wrappers.lambdaQuery(SysBomError.class)
            .in(SysBomError::getBomId, bomIds)
            .orderByDesc(SysBomError::getCreateTime)
        );
        
        String addFails = Optional.ofNullable(bomError).map(SysBomError::getAddFails).orElse("[]");
        List<SapAddBomParam> sapAddBomParams = addFails.equals("[]") ? new ArrayList<>() : JSON.parseArray(addFails, SapAddBomParam.class);

        return sapAddBomParams;
        
    }

    public void addWerk2BOM2Sap(SysBom sysBom, List<SysWerkLine> werkLines) {

        if (sysBom.getBomStatus().equals(3L)) {
            reBom2Sap(sysBom);
            return;
        }

        String resp = null;

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();
        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
        List<TreeBom> treeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.updateLoopTreeBom(treeBoms, "");
        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopAddWerksTreeBom(sysBom, treeBoms, "");

        Map<String, List<String>> werklinesMap = getLineMap(werkLines);
        List<SysWerkLine> _werkLines = JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);

        List<String> errors = new ArrayList<>();

        // 变更请求
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        Map<String, String> vMap = new HashMap<String, String>();
        List<String> editWerkNos = _werkLines.stream().filter(e -> 0 == e.getFlag()).map(SysWerkLine::getWerkNo)
                .distinct()
                .collect(Collectors.toList());

        for (String _item : editWerkNos) {

            String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomJiraNo());

            List<String> strLines = werklinesMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapEditBomParams.forEach(e -> {
                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (editWerkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);
                e.setIV_STKTX(linesStr);
            });

            for (SapEditBomParam item : sapEditBomParams) {
                log.info("新增工厂变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增工厂变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }
        }

        // 新增请求
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });
        List<String> addWerkNos = _werkLines.stream().filter(e -> 1 == e.getFlag()).map(SysWerkLine::getWerkNo)
                .distinct()
                .collect(Collectors.toList());
    
        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(sysBom);

        for (String _item : addWerkNos) {

            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);

            List<String> strLines = werklinesMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String linesStr = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(linesStr);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }
            for (SapAddBomParam item : sapAddBomParams) {
                log.info("新增工厂到BOM请求：" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增工厂到BOM回复：" + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);
                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());

                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }

                    continue;
                }
                adds.add(param);
            }
        }

        bom2SapStore(sysBom, treeBoms, new ArrayList<>(), sysBomServiceUtil, addFails, adds, editFails,
                edits, errors,
                new SysBomError(), werkLines, null);
    }

    public void addBom2Sap(SysBom sysBom, List<String> werkNos, List<SysWerkLine> werkLines) throws ServiceException {

        if (sysBom.getBomStatus().equals(3L)) {
            reBom2Sap(sysBom);
            return;
        }

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();

        List<TreeBom> treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        if (sysBomServiceUtil.ifMengeEmpty(treeBoms)) {
            throw new ServiceException(500, "BOM使用量不为0");
        }

        String resp = null;
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(sysBom, treeBoms, "");

        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });

        Map<String, List<String>> werklinesMap = getLineMap(werkLines);

        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(sysBom);

        for (String _item : werkNos) {

            List<String> strLines = werklinesMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String lines = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);


            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(lines);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }
            for (SapAddBomParam item : sapAddBomParams) {
                log.info("新增BOM请求：" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增BOM回复：" + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);
                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());

                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }

                    continue;
                }
                adds.add(param);
            }
        }

        bom2SapStore(sysBom, treeBoms, new ArrayList<>(), sysBomServiceUtil, addFails, adds, new ArrayList<>(),
                new ArrayList<>(), errors,
                new SysBomError(), werkLines, null);

    }

    public void editBom2Sap(SysBom sysBom, SysBomHistory sysBomHistory, List<String> werkNos,
            List<SysWerkLine> werkLines, ReceiveNameParam nameFromJIRA)
            throws ServiceException {

        if (sysBom.getBomStatus().equals(3L)) {
            reBom2Sap(sysBom);
            return;
        }

        List<TreeBom> newTreeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();

        if (sysBomServiceUtil.ifMengeEmpty(newTreeBoms)) {
            throw new ServiceException(500, "BOM使用量不为0");
        }

        Map<String, TreeBom> flagTreeMap = new HashMap<>();
        List<TreeBom> oldTreeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        Map<String, TreeBom> mapTreeBoms = sysBomServiceUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = new ArrayList<>();
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        String resp = null;

        List<TreeBom> newBoms = new ArrayList<>();

        List<SapEditBomParam> sapEditBomParams = sysBomServiceUtil.loopNewTreeBoms(sysBom, newTreeBoms, mapTreeBoms,
                newBoms, "", flagTreeMap);

        List<SapAddBomParam> sapAddBomParams = sysBomServiceUtil.loopTreeBom(sysBom, newBoms, "");

        Map<String, String> vMap = new HashMap<String, String>();

        List<String> matnrs = new ArrayList<>();
        sapAddBomParams.forEach(e -> {
            matnrs.addAll(e.getIT_DATA().stream().map(SapAddBomItOrEtParam::getMATNR).distinct()
                    .collect(Collectors.toList()));
        });

        Map<String, List<String>> werklinesMap = getLineMap(werkLines);


        List<SapAddBomParam> errSapAddBomParams = getVersionIfSapError(sysBom);

        for (String _item : werkNos) {

            String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomJiraNo());

            sapEditBomParams.forEach(e -> {

                if (null != e.getIV_STLAL() && !e.getIV_STLAL().equals("")) {
                    if (werkNos.indexOf(_item) == 0) {
                        vMap.put(e.getIV_MATNR(), e.getIV_STLAL());
                    }
                    JSONObject obj = JSONObject.parseObject(vMap.get(e.getIV_MATNR()));
                    e.setIV_STLAL(obj == null ? "" : obj.get(_item) + "");
                }

                e.setIV_WERKS(_item);
                e.setIV_AENNR(IV_AENNR);

            });

            log.info("变更请求数据：" + JSONObject.toJSONString(sapEditBomParams));

            /* if (sapEditBomParams == null || sapEditBomParams.isEmpty()) {
                continue;
            } */
            for (SapEditBomParam item : sapEditBomParams) {
                log.info("变更申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("变更申请回复：" + resp);

                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

            /* if (newBoms == null || newBoms.isEmpty()) {
                continue;
            } */

            log.info("变更新增请求数据: " + JSONObject.toJSONString(sapAddBomParams));

            String IV_STLAL = errSapAddBomParams.stream().filter(e->e.getIV_WERKS().equals(_item)).findFirst().map(SapAddBomParam::getIV_STLAL).orElse("0");
            
            long STLAL = Long.valueOf(IV_STLAL) + 1L;

            IV_STLAL = STLAL != 1L ? (STLAL >= 10 ? STLAL + "" : "0" + STLAL) : "";

            String version = !IV_STLAL.equals("") ? IV_STLAL : sysBomServiceUtil
                    .callGetBomVersion(matnrs, _item);


            List<String> strLines = werklinesMap.get(_item);
            String nameCode = strLines.get(0).split(";")[0];
            strLines = strLines.stream().map(e -> e.split(";")[1]).collect(Collectors.toList());
            String lines = "适用" + (StrUtil.isEmpty(nameCode) ? "" : nameCode + "-")
                    + strLines.stream().map(String::valueOf).collect(Collectors.joining(","));

            sapAddBomParams.forEach(e -> {
                e.setIV_WERKS(_item);
                e.setIV_STLAL(version);
                e.setIV_STKTX(lines);
                e.getIT_DATA().forEach(_e -> {
                    _e.setWERKS(_item);
                });
            });

            if (sapAddBomParams == null || sapAddBomParams.isEmpty()) {
                continue;
            }

            for (SapAddBomParam item : sapAddBomParams) {
                log.info("变更新增申请: " + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("变更新增申请回复: " + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item.newClone());
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }

        }

        bom2SapStore(sysBom, newTreeBoms, oldTreeBoms, sysBomServiceUtil, addFails, adds, editFails, edits, errors,
                new SysBomError(), werkLines, nameFromJIRA);

    }

    public void reBom2Sap(SysBom sysBom) throws ServiceException {

        SysBomSapServiceUtil sysBomServiceUtil = new SysBomSapServiceUtil();

        SysBomHistory sysBomHistory = null;

        if (1 == sysBom.getBomIfAdd()) {
            sysBomHistory = bomHistoryService.getLastOne(sysBom.getId());
        }
        List<TreeBom> treeBom = 1 == sysBom.getBomIfAdd()
                ? JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class)
                : JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

        if (sysBomServiceUtil.ifMengeEmpty(treeBom)) {
            log.error("BOM使用量不为0");
            throw new ServiceException(500, "BOM使用量不为0");
        }

        SysBomError sysBomError = bomErrorService.getByBomId(sysBom.getId());
        List<SapEditBomParam> sapEditBomParams = JSONObject.parseArray(sysBomError.getEditFails(),
                SapEditBomParam.class);
        List<SapEditBomParam> editFails = new ArrayList<>();
        List<SapEditBomRespParam> edits = JSONObject.parseArray(sysBomError.getEdits(), SapEditBomRespParam.class);
        List<String> errors = new ArrayList<>();

        String resp = null;

        String IV_AENNR = sysBomServiceUtil.callDcnAennr(sysBom.getBomJiraNo());

        if (sapEditBomParams != null && !sapEditBomParams.isEmpty())
            for (SapEditBomParam item : sapEditBomParams) {
                item.setIV_AENNR(IV_AENNR);
                log.info("修改重试:" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomEditApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("修改重试回复:" + resp);
                SapEditBomRespParam param = JSONObject.parseObject(resp, SapEditBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    editFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                edits.add(param);
            }

        List<SapAddBomParam> sapAddBomParams = JSONObject.parseArray(sysBomError.getAddFails(), SapAddBomParam.class);
        List<SapAddBomParam> addFails = new ArrayList<>();
        List<SapAddBomRespParam> adds = JSONObject.parseArray(sysBomError.getAdds(), SapAddBomRespParam.class);

        if (sapAddBomParams != null && !sapAddBomParams.isEmpty())
            for (SapAddBomParam item : sapAddBomParams) {
                log.info("新增重试:" + JSONObject.toJSONString(item));
                resp = BomUtils.doPost(SapApiParam.BaseUrl +
                        SapApiParam.BomAddApi,
                        SapApiParam.Appkey, JSONObject.toJSONString(item));
                log.info("新增重试回复:" + resp);
                SapAddBomRespParam param = JSONObject.parseObject(resp, SapAddBomRespParam.class);

                if (!"S".equals(param.getEV_FLAG())) {
                    addFails.add(item);
                    if (param.getEV_TEXT() != null) {
                        errors.add(param.getEV_TEXT());
                    }
                    continue;
                }
                adds.add(param);
            }

        bom2SapStore(sysBom, treeBom, new ArrayList<>(), sysBomServiceUtil, addFails, adds, editFails, edits, errors,
                sysBomError, null, null);

    }

    public void bom2SapStore(SysBom sysBom, List<TreeBom> treeBom, List<TreeBom> oldTreeBoms,
            SysBomSapServiceUtil sysBomServiceUtil,
            List<SapAddBomParam> addFails,
            List<SapAddBomRespParam> adds,
            List<SapEditBomParam> editFails, List<SapEditBomRespParam> edits, List<String> errors,
            SysBomError sysBomError,
            List<SysWerkLine> werkLines, ReceiveNameParam nameFromJIRA) throws ServiceException {

        SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
        Map<String, TreeBom> mapTreeBoms = new HashMap<String, TreeBom>();
        List<TreeBom> newBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = new ArrayList<>();

        if (null == sysBomError.getId()) {
            Map<String, TreeBom> flagTreeMap = new HashMap<>();
            mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms, flagTreeMap);
            oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, treeBom, mapTreeBoms, newBoms, flagTreeMap);
            oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));
            oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).collect(Collectors.toList());
            oaAddOrEditParams.forEach(a -> a.setBomNo(sysBom.getBomNo()));
        }

        if ((addFails != null && !addFails.isEmpty()) || (editFails != null && !editFails.isEmpty())) {

            String erros = StringUtil.join(errors.toArray(), ";");
            sysBomError.setBomId(sysBom.getId());
            sysBomError.setAddFails(JSONObject.toJSONString(addFails));
            sysBomError.setEditFails(JSONObject.toJSONString(editFails));
            sysBomError.setAdds(JSONObject.toJSONString(adds));
            sysBomError.setEdits(JSONObject.toJSONString(edits));
            sysBomError.setErrorMsg(erros);
            if (null != oaAddOrEditParams && !oaAddOrEditParams.isEmpty()) {
                BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams).werkLines(werkLines)
                        .build();
                sysBomError.setBomChange(JSONObject.toJSONString(bomChangeParam));
            }
            bomErrorService.saveSysBomErrorParam(sysBomError);

            sysBom.setBomStatus(3L);
            this.updateById(sysBom);

            throw new ServiceException(500, erros);
        }

        Map<String, String> versionMap = new HashMap<>();
        if (1 == sysBom.getBomIfAdd()) {
            versionMap = sysBomServiceUtil.getVersionMap(treeBom);
        }
        Map<String, String> mapPosnr = sysBomServiceUtil.getSapPosnr(adds, versionMap);
        if (edits != null && !edits.isEmpty()) {
            sysBomServiceUtil.getEditSapPosnr(edits, mapPosnr);
        }
        sysBomServiceUtil.loopSavePosnr(treeBom, mapPosnr, versionMap);
        sysBom.setBomData(JSONObject.toJSONString(treeBom));
        sysBom.setJiraIdStatus(1);
        sysBom.setBomStatus(2L);
        Boolean ifAdd = (1 == sysBom.getBomIfAdd() || 2 == sysBom.getBomIfAdd());
        SysBomHistoryParam bomHistoryParam = new SysBomHistoryParam();

        LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
        List<SysBomHistory> list = bomHistoryService.list(queryWrapper);

        bomHistoryParam.setType(ifAdd ? (1 == sysBom.getBomIfAdd() ? 3 : 6) : (list.size() != 0 ? 0 : 1));
        Integer isAddWerk = sysBom.getBomIfAdd();
        if (1 == sysBom.getBomIfAdd()) {
            sysBom.setBomIfAdd(0);
            sysBom.setBomAddWerks("");
        }
        this.updateById(sysBom);

        SysBom sysBom1 = this.get(sysBom.getId());

        bomHistoryParam.setBomData(sysBom.getBomData());
        bomHistoryParam.setBomId(sysBom.getId());
        bomHistoryParam.setRemark(sysBom.getBomRemark());

        if (StrUtil.isNotBlank(sysBom.getLineRemark())) {
            bomHistoryParam.setRemark(sysBom.getBomRemark() + "&&lineRemark&&" + sysBom.getLineRemark());
        }

        bomHistoryParam.setAlterType(sysBom.getAlterType());
        bomHistoryParam.setProductState(sysBom.getProductState());
        bomHistoryParam.setCreateTime(new Date());
        if (null != oaAddOrEditParams && !oaAddOrEditParams.isEmpty()) {
            oaAddOrEditParams.forEach(a -> a.setBomNo(sysBom.getBomNo()));
            BomChangeParam bomChangeParam = BomChangeParam.builder().changes(oaAddOrEditParams).werkLines(werkLines)
                    .build();
            bomHistoryParam.setBomChange(JSONObject.toJSONString(bomChangeParam));
        } else {
            bomHistoryParam.setBomChange(sysBomError.getBomChange());
        }
        bomHistoryParam.setBomNo(sysBom1.getBomNo());
        bomHistoryParam.setBomVersion(sysBom1.getBomVersion());
        bomHistoryParam.setBomName(sysBom1.getBomName());
        bomHistoryParam.setIsAddWerk(isAddWerk);
        bomHistoryParam.setSummitor(sysBom.getSummitor());
        bomHistoryParam.setFileId(sysBom1.getFileId());
        bomHistoryParam.setBomTransport(sysBom.getBomTransport());

        List<SysBomLine> sysBomLines = bomLineService
                    .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());
        bomHistoryParam.setBomLines(JSONObject.toJSONString(sysBomLines));
        bomHistoryService.add(bomHistoryParam);
        bomErrorService.delete(sysBom.getId());
    }

    private String getAlterDetails(String change){

        List<OaAddOrEditParam> oaAddOrEditParams = JSONObject.parseArray(change, OaAddOrEditParam.class);

        Map<String,List<OaAddOrEditParam>> paramsMap = oaAddOrEditParams.stream()
        .filter(e -> null != e)
        .filter(e->e.getFlag().equals("新增") || e.getFlag().equals("删除"))
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(),
                Collectors.toList()
            )
        )
        .entrySet().stream()
        .filter(e->e.getValue().size() > 1)
        .map(val->val.getValue())
        .flatMap(val->val.stream())
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(),
                Collectors.mapping(item->OaAddOrEditParam.builder().id(item.getId()).flag(item.getFlag()).build(), Collectors.toList())
            )
        );

        List<String> ids = new ArrayList<>();

        for (Map.Entry<String,List<OaAddOrEditParam>> entry : paramsMap.entrySet()) {
            List<OaAddOrEditParam> delList = entry.getValue().stream().filter(e->e.getFlag().equals("删除")).collect(Collectors.toList());
            List<OaAddOrEditParam> addList = entry.getValue().stream().filter(e->e.getFlag().equals("新增")).collect(Collectors.toList());
            int minCount = delList.size() < addList.size() ? delList.size() : addList.size();
            for (int i = 0,j = minCount; i < j; i++) {
                ids.add(delList.get(i).getId());
                ids.add(addList.get(i).getId());
            }
        }
        oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).filter(e-> ids.indexOf(e.getId()) < 0).collect(Collectors.toList());

        Map<String,List<OaAddOrEditParam>> mapAddOrEdits = oaAddOrEditParams.stream().collect(Collectors.groupingBy(OaAddOrEditParam::getFlag,Collectors.toList()));
        List<String> alters = new ArrayList<>();

        int i = 1;

        for (Map.Entry<String,List<OaAddOrEditParam>> item : mapAddOrEdits.entrySet()) {

            if (item.getKey().equals("修改")) {

                List<String> details = new ArrayList<>();

                for (OaAddOrEditParam _item : item.getValue()) {
                    details.add(new StringBuilder()
                    .append(_item.getPartName())
                    .append(_item.getSapNumber())
                    .append("用量由")
                    .append(_item.getPrePartUse())
                    .append(_item.getPrePartUnit())
                    .append("更改为")
                    .append(_item.getPartUse())
                    .append(_item.getPartUnit())
                    .toString());
                }
                alters.add(new StringBuilder().append(i+".物料用量变更:").append(String.join(";", details)).toString());
            }

            if (item.getKey().equals("删除")){
                List<String> details = new ArrayList<>();
                for (OaAddOrEditParam _item : item.getValue()) {
                    details.add(new StringBuilder()
                    .append("删除")
                    .append(_item.getPartName())
                    .append(_item.getSapNumber())
                    .toString());
                }
                alters.add(new StringBuilder().append(i+".删除物料:").append(String.join(";", details)).toString());
            }

            if (item.getKey().equals("新增")){
                List<String> details = new ArrayList<>();
                for (OaAddOrEditParam _item : item.getValue()) {
                    details.add(new StringBuilder()
                    .append("增加")
                    .append(_item.getPartName())
                    .append(_item.getSapNumber())
                    .append(",用量")
                    .append(_item.getPartUse())
                    .append(_item.getPartUnit())
                    .toString());
                }
                alters.add(new StringBuilder().append(i+".新增物料:").append(String.join(";", details)).toString());
            }
            i++;
        }
        return String.join("\n", alters);
    }

    @Override
    public Map pdfUpdate(Long id, Boolean update) throws IOException {

        SysBom sysBom = get(id);

        if (null != sysBom.getBomNo()) {
            String[] split = sysBom.getBomNo().split("-");
            String oldCode = split[split.length - 2];
            if (!oldCode.equals(getProductState3(sysBom.getProductState()))) {
                split[split.length - 2] = getProductState3(sysBom.getProductState());
            }
            sysBom.setBomNo(Stream.of(split).collect(Collectors.joining("-")));
            this.updateById(sysBom);
        }

        String factVersion = "";

        if (!new Long(0).equals(sysBom.getBomStatus()) && !new Long(7).equals(sysBom.getBomStatus()) && !new Long(4).equals(sysBom.getBomStatus())
                && null != sysBom.getFileId()) {
            Map<String, Object> data = new HashMap<>();
            data.put("fileId", sysBom.getFileId());
            return data;
        }

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }
            }
        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        /* BufferedInputStream in = null; */
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {
            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "BOM模板.xlsx"));

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            /*
             * in = new BufferedInputStream(
             * new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
             * "logo.png")));
             */
            out = new ByteArrayOutputStream(1024);
            /*
             * byte[] temp = new byte[1024];
             * int size = 0;
             * while ((size = in.read(temp)) != -1) {
             * out.write(temp, 0, size);
             * }
             * byte[] content = out.toByteArray();
             */

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : treeBomListMap.keySet()) {
                parent = key;
                break;
            }

            Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();
            map.put("name", parent.getPartDescription());
            map.put("code", parent.getSapNumber());
            map.put("fileCode", sysBom.getBomNo());
            map.put("version", sysBom.getBomVersion());
            map.put("fileName", sysBom.getBomName());
            map.put("partUnit", parent.getPartUnit());
            map.put("state", getProductState(sysBom.getProductState()));
            map.put("transport", getTransport(sysBom.getBomTransport()));
            map.put("partUse", parent.getPartUse());
            // map.put("img", content);
            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();
                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += " " + key1 + "-" + value;
                        }

                    }

                    if (!"".equals(version)) {
                        factVersion = version;
                    }
                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("name", parent.getPartDescription());
            newMap.put("code", parent.getSapNumber());
            newMap.put("fileCode", sysBom.getBomNo());
            newMap.put("version", sysBom.getBomVersion());
            newMap.put("fileName", sysBom.getBomName());
            newMap.put("partUnit", parent.getPartUnit());
            newMap.put("state", getProductState(sysBom.getProductState()));
            newMap.put("transport", getTransport(sysBom.getBomTransport()));
            newMap.put("partUse", parent.getPartUse());
            // newMap.put("img", content);
            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }
            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
            queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                String bomChange = list.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray.parseArray(change, JSONObject.class);
                if (null != jsonObjects1) {
                    // 新增
                    if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", list.get(i).getRemark());
                        object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                        jsonObjects.add(object);
                    } else if (new Integer(3).equals(list.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂:");
                        object.put("reason", list.get(i).getRemark());
                        if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "新增工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }

                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(list.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂:");
                        object.put("reason", list.get(i).getRemark());
                        if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "删除工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }

                        object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {

                        JSONObject object = new JSONObject();



                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }
                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }
                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */

                        object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                        object.put("detail", getAlterDetails(change));
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", list.get(i).getRemark());
                        jsonObjects.add(object);
                    }
                }

            }

            // SysBomHistory countByBomId =
            // bomHistoryService.getCountByBomId(sysBom.getId());
            List<OaAddOrEditParam> lastHistory = this.getLastHistory(sysBom.getId());
            // 新增
            if (list.size() == 0) {
                JSONObject object = new JSONObject();
                object.put("detail", "初版提交");
                object.put("bomStartdate", null == lastHistory ? DateUtil.format(new Date(), "yyyy-MM-dd")
                        : lastHistory.get(0).getBomStartdate());
                object.put("version", null == lastHistory ? sysBom.getBomVersion() : lastHistory.get(0).getVersion());
                object.put("productState", getProductState2(sysBom.getProductState()));
                jsonObjects.add(object);
            } else {

                if (lastHistory.size() != 0) {
                    JSONObject object = new JSONObject();
                    /* String detail = "";
                    for (int j = 0; j < lastHistory.size(); j++) {
                        if (!"".equals(detail)) {
                            detail += "\n";
                        }
                        String serNum = "";
                        if (lastHistory.size() > 1) {
                            serNum = (j + 1) + "、";
                        }
                        if ("新增".equals(lastHistory.get(j).getFlag()) || "删除".equals(lastHistory.get(j).getFlag())) {
                            detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber() + lastHistory.get(j).getFlag()
                                    + "子物料"
                                    + lastHistory.get(j).getSapNumber() + "-"
                                    + lastHistory.get(j).getPartDescription()

                                    + "使用量:"
                                    + ("删除".equals(lastHistory.get(j).getFlag()) ? lastHistory.get(j).getPrePartUse()
                                            : lastHistory.get(j).getPartUse());

                        }
                        if ("修改".equals(lastHistory.get(j).getFlag())) {
                            detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber() + lastHistory.get(j).getFlag()
                                    + "子物料"
                                    + lastHistory.get(j).getSapNumber() + "-"
                                    + lastHistory.get(j).getPartDescription()
                                    + "使用量:" + lastHistory.get(j).getPartUse() + "-变更前使用量:"
                                    + lastHistory.get(j).getPrePartUse();
                        }
                    } */
                    object.put("productState", getProductState2(sysBom.getProductState()));
                    object.put("detail", getAlterDetails(JSONObject.toJSONString(lastHistory)));
                    object.put("bomStartdate", DateUtil.format(new Date(), "yyyy-MM-dd"));
                    object.put("version", lastHistory.get(0).getVersion());
                    jsonObjects.add(object);
                }

            }
            Integer historySize = jsonObjects.size();
            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            context.putVar("bomName", sysBom.getBomType() == 0 ? "电芯" : "包装");

            context.putVar("historyList", jsonObjects);

            context.putVar("bomList", result);
            context.putVar("factVersion", factVersion);

            // context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
            // "https://oa.evebattery.com", 150, 150));
            context.putVar("sheetName", sheetNames);
            context.putVar("materialsName", parent.getPartDescription());
            context.putVar("materialsCode", parent.getSapNumber());
            context.putVar("partUnit", parent.getPartUnit());
            context.putVar("bomState", getProductState(sysBom.getProductState()));
            context.putVar("transport", getTransport(sysBom.getBomTransport()));
            context.putVar("month", getChineseDate(new Date()));
            context.putVar("productType", sysBom.getBomProductType());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(sysBom.getBomIssueId());
            context.putVar("productStage", sysBom.getProductStage());
            context.putVar("productProjectName", sysBom.getProductProjectName());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("status", list.size() == 0 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废");
            setProductState(sysBom.getProductState(), context);

            // 验证license
            is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
            License aposeLic = new License();
            aposeLic.setLicense(is);

            // 生成excel
            JxlsHelper.getInstance().processTemplate(inStream, os, context);

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");
                    for (int i = 0; i < historySize * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 1) / 2;
                        Integer i2 = setHeight(row1, 2) / 2;
                        row1.setHeight(i1 < i2 ?Short.valueOf(i2.toString()):Short.valueOf(i1.toString()));
                        row2.setHeight(i1 < i2 ?Short.valueOf(i2.toString()):Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }

                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 14), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 14, 15), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 15, 16), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(34, 35, 15, 16), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 13, 16), sheet);

                }

                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();

                /*
                 * Workbook workbook1 = new Workbook(path);
                 * WorksheetCollection worksheets = workbook1.getWorksheets();
                 * for (int i = 0; i < 3; i++) {
                 * Worksheet worksheet = worksheets.get(i);
                 *
                 *
                 * worksheet.getPageSetup().setHeaderPicture(2,QRCodeUtil.createImageByte(
                 * "utf-8",
                 * "https://oa.evebattery.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                 * + sysBom.getOaId(),
                 * 100, 100));
                 *
                 * worksheet.getPageSetup().setHeaderMarginInch(0.08d);
                 *
                 *
                 * worksheet.getPageSetup().setHeader(2,"&G");//添加图片
                 * //worksheet.getPageSetup().setHeaderMarginInch(50d);
                 *
                 * }
                 * workbook1.save(path);
                 */

            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                fos.close();
            }

            // excel导出pdf
            Workbook wb = new Workbook(path);// 原始excel路径
            String pdfName = UUID.randomUUID().toString();
            String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOS = new FileOutputStream(new File(pdfPath));
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);

            File pdf = new File(pdfPath);
            pdfInputStream = new FileInputStream(pdf);
            MultipartFile multipartFile = new MockMultipartFile(pdf.getName(), pdf.getName(), null, pdfInputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_BUCKET_PDF);

            /*
             * sysBom.setFileId(fileId);
             * this.updateById(sysBom);
             */

            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");

            // SysBom finished = this.getOne(sysBom.getBomSourceId(), 2);

            Map<String, Object> data = new HashMap<>();
            data.put("parentIssueId", sysBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");
            data.put("revisionContent", "");
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());
            data.put("fileLink",
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + fileId);
            data.put("fileId", fileId);
            // data.put("finishedMaterialCode", finished.getBomCode());
            // data.put("finishedBomId", finished.getId());
            data.put("compilerId", LoginContextHolder.me().getSysLoginUser().getAccount());

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            /*
             * if (null != in) {
             * in.close();
             * }
             */
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

        return null;
    }

    @Override
    public Map pdfUpdateLine(Long id) throws IOException {

        SysBom sysBom = get(id);

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {

            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "BOM模板1.xlsx"));

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
            // }

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : treeBomListMap.keySet()) {
                parent = key;
                break;
            }

            Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();
            map.put("name", parent.getPartDescription());
            map.put("code", parent.getSapNumber());
            map.put("fileCode", sysBom.getBomNo());
            map.put("version", sysBom.getBomVersion());
            map.put("fileName", sysBom.getBomName());
            map.put("partUnit", parent.getPartUnit());
            map.put("state", getProductState(sysBom.getProductState()));
            map.put("transport", getTransport(sysBom.getBomTransport()));
            map.put("partUse", parent.getPartUse());
            map.put("img", content);
            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());

                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();

                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += "\n" + key1 + "-" + value;

                        }

                    }

                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("name", parent.getPartDescription());
            newMap.put("code", parent.getSapNumber());
            newMap.put("fileCode", sysBom.getBomNo());
            newMap.put("version", sysBom.getBomVersion());
            newMap.put("fileName", sysBom.getBomName());
            newMap.put("partUnit", parent.getPartUnit());
            newMap.put("state", getProductState(sysBom.getProductState()));
            newMap.put("transport", getTransport(sysBom.getBomTransport()));
            newMap.put("partUse", parent.getPartUse());
            newMap.put("img", content);
            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;

                    // 第二十条数据
                    if (num <= 25 && j == inBoms.size() - 1) {
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", outBoms.get(i).get("length"));
                        objectObjectHashMap.put("version", outBoms.get(i).get("version"));
                        objectObjectHashMap.put("inBom", inBoms);
                        bomList.add(objectObjectHashMap);

                        if (num == 25) {
                            newMap.put("data", bomList);
                            result.add(newMap);
                            newMap = new HashMap<>();
                            newMap.put("name", parent.getPartDescription());
                            newMap.put("code", parent.getSapNumber());
                            newMap.put("fileCode", sysBom.getBomNo());
                            newMap.put("version", sysBom.getBomVersion());
                            newMap.put("fileName", sysBom.getBomName());
                            newMap.put("partUnit", parent.getPartUnit());
                            newMap.put("state", getProductState(sysBom.getProductState()));
                            newMap.put("transport", getTransport(sysBom.getBomTransport()));
                            newMap.put("partUse", parent.getPartUse());
                            newMap.put("img", content);
                            bomList = new ArrayList<>();
                            objectObjectHashMap = new HashMap<>();
                            sheetNames.add("表格" + Math.random());
                            num = 0;
                        }

                        if (i == outBoms.size() - 1) {

                            for (int k = num; k < 25; k++) {
                                Map<String, Object> fullMap = new HashMap<>();
                                fullMap.put("length", 1);
                                List<TreeBom> fullBom = new ArrayList<>();
                                fullBom.add(null);
                                fullMap.put("inBom", fullBom);
                                bomList.add(fullMap);
                            }
                            newMap.put("data", bomList);
                            result.add(newMap);
                            sheetNames.add("表格" + Math.random());

                        }

                    } else if (num == 25 && j < inBoms.size() - 1) {

                        int limit = j + 1;
                        List<TreeBom> one = new ArrayList<>();
                        for (int k = 0; k < limit; k++) {
                            one.add(inBoms.get(k));
                        }
                        Map<String, Object> oneMap = new HashMap<>();
                        oneMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        oneMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        oneMap.put("version", outBoms.get(i).get("version"));
                        oneMap.put("length", limit);
                        oneMap.put("inBom", one);

                        bomList.add(oneMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        newMap = new HashMap<>();
                        newMap.put("name", parent.getPartDescription());
                        newMap.put("code", parent.getSapNumber());
                        newMap.put("fileCode", sysBom.getBomNo());
                        newMap.put("version", sysBom.getBomVersion());
                        newMap.put("fileName", sysBom.getBomName());
                        newMap.put("partUnit", parent.getPartUnit());
                        newMap.put("state", getProductState(sysBom.getProductState()));
                        newMap.put("transport", getTransport(sysBom.getBomTransport()));
                        newMap.put("partUse", parent.getPartUse());
                        newMap.put("img", content);
                        bomList = new ArrayList<>();

                        List<TreeBom> other = new ArrayList<>();
                        for (int l = limit; l < inBoms.size(); l++) {
                            other.add(inBoms.get(l));
                        }

                        Map<String, Object> otherMap = new HashMap<>();
                        otherMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        otherMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        oneMap.put("version", outBoms.get(i).get("version"));
                        otherMap.put("length", inBoms.size() - limit);
                        otherMap.put("inBom", other);
                        num = inBoms.size() - limit;
                        bomList.add(otherMap);
                        break;

                    } else if (i == outBoms.size() - 1 && j == inBoms.size() - 1) {
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("version", outBoms.get(i).get("version"));
                        objectObjectHashMap.put("length", outBoms.get(i).get("length"));
                        objectObjectHashMap.put("inBom", inBoms);
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                    }

                }
            }
            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
            queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                String bomChange = list.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray
                        .parseArray(change, JSONObject.class);
                if (null != jsonObjects1) {
                    // 新增
                    if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", list.get(i).getRemark());
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);

                    } else if (new Integer(3).equals(list.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂: " + list.get(i).getRemark());
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", "增加工厂");
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(list.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂: " + list.get(i).getRemark());
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", "减少工厂");
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }
                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }
                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */

                        object.put("detail", getAlterDetails(change));
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", jsonObjects1.get(0).getString("version"));
                        object.put("reason", list.get(i).getRemark());
                        object.put("summitor", list.get(i).getSummitor());
                        jsonObjects.add(object);
                    }
                }

            }

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            context.putVar("bomName", sysBom.getBomType() == 0 ? "电芯" : "包装");

            context.putVar("historyList", jsonObjects);
            context.putVar("checkMan", Base64.getDecoder().decode(
                    "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"
                            .trim()));
            context.putVar("version", sysBom.getBomVersion());
            context.putVar("name", sysBom.getBomName() + "-" + parent.getSapNumber());
            context.putVar("code", sysBom.getBomNo());
            context.putVar("bomList", result);
            context.putVar("sheetName", sheetNames);
            context.putVar("month", getChineseDate(new Date()));
            context.putVar("productType", sysBom.getBomProductType());
            context.putVar("summitor", sysBom.getSummitor());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(sysBom.getBomIssueId());
            context.putVar("productStage", sysBom.getProductStage());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));

            context.putVar("factory", factory);
            context.putVar("status", list.size() == 0 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废");
            setProductState(sysBom.getProductState(), context);

            // 生成excel
            JxlsHelper.getInstance().processTemplate(inStream, os, context);

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");
                    for (int i = 0; i < jsonObjects.size() * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 1) / 2;
                        Integer i2 = setHeight(row1, 2) / 2;
                        row1.setHeight(i1 < i2 ?Short.valueOf(i2.toString()):Short.valueOf(i1.toString()));
                        row2.setHeight(i1 < i2 ?Short.valueOf(i2.toString()):Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 35, 13, 14), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 35, 14, 15), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 35, 15, 16), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(35, 36, 15, 16), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 13, 16), sheet);

                }
                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                fos.close();
            }

            // 验证license
            is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
            License aposeLic = new License();
            aposeLic.setLicense(is);

            // excel导出pdf
            Workbook wb = new Workbook(path);// 原始excel路径
            String pdfName = UUID.randomUUID().toString();
            String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOS = new FileOutputStream(new File(pdfPath));
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);

            File pdf = new File(pdfPath);
            pdfInputStream = new FileInputStream(pdf);
            MultipartFile multipartFile = new MockMultipartFile(pdf.getName(), pdf.getName(), null, pdfInputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_BUCKET_PDF);

            sysBom.setFileId(fileId);
            this.updateById(sysBom);
            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            Map<String, Object> data = new HashMap<>();
            data.put("fileId", fileId);
            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

        return null;
    }


    public Map endBomPdfUpdate1(Long id) throws IOException {

        SysBomEnd sysBom = sysBomEndService.getById(id);

        // SysBomLineParam sysBomLineParam = new SysBomLineParam();
        // sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        // List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        // Map<Long, List<SysBomLine>> lineMap =
        // sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        List<Long> lines = JSONObject.parseArray(sysBom.getBomLines(), Long.class);

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {
            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "成品BOM模板.xlsx"));

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            Map<TreeBom, List<TreeBom>> map1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : map1.keySet()) {
                parent = key;
                break;
            }

            // 电芯bom
            Long bomId = sysBom.getBomId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(SysBomHistory::getBomId, bomId);
            queryWrapper1.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> bomHistoryList = bomHistoryService.list(queryWrapper1);

            TreeBom bom = null;
            String batFactVersion = "";
            if (bomHistoryList.size() > 0) {
                bom = JSONObject.parseArray(bomHistoryList.get(bomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);

                Map<TreeBom, List<TreeBom>> batBomData = sysBomServiceUtil.loopTreeBom1(
                        JSONObject.parseArray(bomHistoryList.get(bomHistoryList.size() - 1).getBomData(),
                                TreeBom.class));

                for (TreeBom key : batBomData.keySet()) {
                    if (!StrUtil.isBlank(key.getVersion())) {
                        String version = "";
                        JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                            String key1 = entry.getKey();
                            Object value = entry.getValue();
                            if ("".equals(version)) {
                                version += key1 + "-" + value;
                            } else {
                                version += " " + key1 + "-" + value;
                            }
                        }
                        batFactVersion = version;
                        break;
                    }

                }

            } else {
                throw new ServiceException(500, "关联的电芯bom还未通过审核");
            }

            // 包装bom
            Long bomPackId = sysBom.getBomPackId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(SysBomHistory::getBomId, bomPackId);
            queryWrapper2.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> packBomHistoryList = bomHistoryService.list(queryWrapper2);

            TreeBom pack = null;

            String packFactVersion = "";

            if (packBomHistoryList.size() > 0) {
                pack = JSONObject
                        .parseArray(packBomHistoryList.get(packBomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);

                Map<TreeBom, List<TreeBom>> packBomData = sysBomServiceUtil.loopTreeBom1(JSONObject
                        .parseArray(packBomHistoryList.get(packBomHistoryList.size() - 1).getBomData(), TreeBom.class));

                for (TreeBom key : packBomData.keySet()) {
                    if (!StrUtil.isBlank(key.getVersion())) {
                        String version = "";
                        JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                            String key1 = entry.getKey();
                            Object value = entry.getValue();
                            if ("".equals(version)) {
                                version += key1 + "-" + value;
                            } else {
                                version += " " + key1 + "-" + value;
                            }
                        }
                        packFactVersion = version;
                        break;
                    }

                }

            } else {
                throw new ServiceException(500, "关联的包装bom还未通过审核");
            }

            List<TreeBom> inBom = new ArrayList<>();
            inBom.add(bom);
            inBom.add(pack);

            treeBoms.get(0).setLists(inBom);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);
            map1.clear();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();

            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();
                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += "\n" + key1 + "-" + value;
                        }

                    }

                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();

            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }

                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }
            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            String updateContent = "";

            for (int i = 0; i < bomHistoryList.size(); i++) {
                String bomChange = bomHistoryList.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray.parseArray(change, JSONObject.class);

                if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                    // 新增
                    if (new Integer(1).equals(bomHistoryList.get(i).getType())
                            || "A".equals(bomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        jsonObjects.add(object);
                    } else if (new Integer(3).equals(bomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂:");
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        if (null != bomHistoryList.get(i).getRemark()
                                && bomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = bomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "新增工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }

                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(bomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂:");
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        if (null != bomHistoryList.get(i).getRemark()
                                && bomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = bomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "删除工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }

                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }
                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */
                        String details = getAlterDetails(change);
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("detail", details);
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));

                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);

                        if (!"".equals(updateContent)) {
                            updateContent += "\n";
                        }

                        updateContent += details;

                    }
                }

            }

            for (int i = 0; i < packBomHistoryList.size(); i++) {
                String bomChange = packBomHistoryList.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray
                        .parseArray(change, JSONObject.class);
                if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                    // 新增
                    if (new Integer(1).equals(packBomHistoryList.get(i).getType())
                            || "A".equals(packBomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("type", "包装");
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(3).equals(packBomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂:");
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        if (null != bomHistoryList.get(i).getRemark()
                                && bomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = bomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "新增工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(packBomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂:");

                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        if (null != packBomHistoryList.get(i).getRemark()
                                && packBomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = packBomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "删除工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }

                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }

                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */
                        String details = getAlterDetails(change);
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("detail", details);
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);

                        if (!"".equals(updateContent)) {
                            updateContent += "\n";
                        }

                        updateContent += details;
                    }
                }

            }

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            SysBom batteryBom = this.get(bomId);
            SysBom packBom = this.get(bomPackId);

            String code = "";
            String name = "";
            String version = "";
            try {
                code += bomHistoryList.get(bomHistoryList.size() - 1).getBomNo() + "-";
                String[] split1 = packBomHistoryList.get(packBomHistoryList.size() - 1).getBomNo().split("-");
                String[] split = bomHistoryList.get(bomHistoryList.size() - 1).getBomNo().split("-");
                code += split1[split1.length - 1];

                version = split[split.length - 1] + "-" +
                        split1[split1.length - 1];

                name = code + "-成品BOM-" + parent.getSapNumber()
                        + (StrUtil.isBlank(factory) ? "" : ("(适用于" + factory + ")"));
            } catch (Exception e) {
                throw new ServiceException(500, "关联的电芯bom或包装bom还未通过审核");
            }

            context.putVar("historyList", jsonObjects);
            context.putVar("bomList", result);
            context.putVar("sheetName", sheetNames);
            context.putVar("month", getChineseDate(sysBom.getCreateTime()));
            context.putVar("productType", batteryBom.getBomProductType());
            context.putVar("productProjectName", batteryBom.getProductProjectName());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("bomName", parent.getPartDescription());
            context.putVar("bomCode", parent.getSapNumber());
            context.putVar("bomPartUse", parent.getPartUse());
            context.putVar("bomPartUnit", parent.getPartUnit());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(batteryBom.getBomIssueId());
            context.putVar("productStage", batteryBom.getProductStage());
            context.putVar("transport", getTransport(packBom.getBomTransport()));
            context.putVar("code", code);
            context.putVar("version", version);
            context.putVar("batFactVersion", batFactVersion);
            context.putVar("packFactVersion", packFactVersion);
            context.putVar("name", name);
            context.putVar("img", content);
            context.putVar("bomState", getProductState(batteryBom.getProductState()));

            context.putVar("status",
                    bomHistoryList.size() <= 1 && packBomHistoryList.size() <= 1 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废");
            setProductState(batteryBom.getProductState(), context);

            // 生成excel
            JxlsHelper.getInstance().processTemplate(inStream, os, context);

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");

                    for (int i = 0; i < (bomHistoryList.size() + packBomHistoryList.size()) * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 2) / 2;
                        row1.setHeight(Short.valueOf(i1.toString()));
                        row2.setHeight(Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 11, 11), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 12, 12), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 13), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 10, 13), sheet);

                }
                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new ServiceException(500, e.getMessage());
            } finally {
                fos.close();
            }

            // 验证license
            is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
            License aposeLic = new License();
            aposeLic.setLicense(is);

            // excel导出pdf
            Workbook wb = new Workbook(path);// 原始excel路径
            String pdfName = UUID.randomUUID().toString();
            String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOS = new FileOutputStream(new File(pdfPath));
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);

            File pdf = new File(pdfPath);
            pdfInputStream = new FileInputStream(pdf);
            MultipartFile multipartFile = new MockMultipartFile(pdf.getName(), pdf.getName(), null, pdfInputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_BUCKET_PDF);

            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");

            Map<String, Object> data = new HashMap<>();
            data.put("fileLink",
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + fileId);
            data.put("fileId", fileId);
            data.put("parentIssueId", batteryBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", "成品BOM");
            data.put("revisionContent", updateContent);
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());
            data.put("fileVersion", version);
            data.put("fileName", name);
            data.put("fileCode", code);

            /*
             * data.put("finishedMaterialCode", "998");
             * data.put("finishedBomId", 998);
             */
            data.put("compilerId", "071716");

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, e.getMessage());
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

    }

    @Override
    public Map endBomPdfUpdate(Long id) throws IOException {

        SysBomEnd sysBom = sysBomEndService.getById(id);

        // SysBomLineParam sysBomLineParam = new SysBomLineParam();
        // sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        // List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        // Map<Long, List<SysBomLine>> lineMap =
        // sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        List<Long> lines = JSONObject.parseArray(sysBom.getBomLines(), Long.class);

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {
            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, sysBom.getBomRelateStatus() == 2?"成品BOM模板.xlsx":"成品BOM模板-未审核.xlsx"));

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            Map<TreeBom, List<TreeBom>> map1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : map1.keySet()) {
                parent = key;
                break;
            }

            // 电芯bom
            Long bomId = sysBom.getBomId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(SysBomHistory::getBomId, bomId);
            queryWrapper1.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> bomHistoryList = bomHistoryService.list(queryWrapper1);
            bomHistoryList = bomHistoryList.stream().filter(e->null != e.getBomChange()).collect(Collectors.toList());

            TreeBom bom = null;
            String batFactVersion = "";
            if (bomHistoryList.size() > 0) {
                TreeBom treeBom = JSONObject.parseArray(bomHistoryList.get(bomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);
                treeBom.setLists(new ArrayList<>());
                bom = treeBom;

                Map<TreeBom, List<TreeBom>> batBomData = sysBomServiceUtil.loopTreeBom1(
                        JSONObject.parseArray(bomHistoryList.get(bomHistoryList.size() - 1).getBomData(),
                                TreeBom.class));

                for (TreeBom key : batBomData.keySet()) {
                    if (!StrUtil.isBlank(key.getVersion())) {
                        String version = "";
                        JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                            String key1 = entry.getKey();
                            Object value = entry.getValue();
                            if ("".equals(version)) {
                                version += key1 + "-" + value;
                            } else {
                                version += " " + key1 + "-" + value;
                            }
                        }
                        batFactVersion = version;
                        break;
                    }

                }

            } else {
                throw new ServiceException(500, "关联的电芯bom还未通过审核");
            }

            // 包装bom
            Long bomPackId = sysBom.getBomPackId();
            LambdaQueryWrapper<SysBomHistory> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(SysBomHistory::getBomId, bomPackId);
            queryWrapper2.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> packBomHistoryList = bomHistoryService.list(queryWrapper2);

            TreeBom pack = null;

            String packFactVersion = "";

            if (packBomHistoryList.size() > 0) {
                TreeBom treeBom = JSONObject
                        .parseArray(packBomHistoryList.get(packBomHistoryList.size() - 1).getBomData(), TreeBom.class)
                        .get(0);
                treeBom.setLists(new ArrayList<>());
                pack = treeBom;

                Map<TreeBom, List<TreeBom>> packBomData = sysBomServiceUtil.loopTreeBom1(JSONObject
                        .parseArray(packBomHistoryList.get(packBomHistoryList.size() - 1).getBomData(), TreeBom.class));

                for (TreeBom key : packBomData.keySet()) {
                    if (!StrUtil.isBlank(key.getVersion())) {
                        String version = "";
                        JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                            String key1 = entry.getKey();
                            Object value = entry.getValue();
                            if ("".equals(version)) {
                                version += key1 + "-" + value;
                            } else {
                                version += " " + key1 + "-" + value;
                            }
                        }
                        packFactVersion = version;
                        break;
                    }

                }

            } else {
                throw new ServiceException(500, "关联的包装bom还未通过审核");
            }

            List<TreeBom> inBom = new ArrayList<>();
            inBom.add(bom);
            inBom.add(pack);

            treeBoms.get(0).setLists(inBom);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);
            map1.clear();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();

            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();
                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += "\n" + key1 + "-" + value;
                        }

                    }

                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();

            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }

                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }
            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            String updateContent = "";

            for (int i = 0; i < bomHistoryList.size(); i++) {
                String bomChange = bomHistoryList.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray
                        .parseArray(change, JSONObject.class);

                if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                    // 新增
                    if (new Integer(1).equals(bomHistoryList.get(i).getType())
                            || "A".equals(bomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        jsonObjects.add(object);
                    } else if (new Integer(3).equals(bomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂:");
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        if (null != bomHistoryList.get(i).getRemark()
                                && bomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = bomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "新增工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }

                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(bomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂:");
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        if (null != bomHistoryList.get(i).getRemark()
                                && bomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = bomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "删除工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }

                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }
                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */
                        String details = getAlterDetails(change);
                        object.put("productState",
                                getProductState2(bomHistoryList.get(i).getProductState().intValue()));
                        object.put("detail", details);
                        object.put("type", "电芯");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));

                        object.put("version", bomHistoryList.get(i).getBomNo()
                                .split("-")[bomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", bomHistoryList.get(i).getRemark());
                        object.put("summitor", bomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);

                        if (!"".equals(updateContent)) {
                            updateContent += "\n";
                        }

                        updateContent += details;

                    }
                }

            }

            for (int i = 0; i < packBomHistoryList.size(); i++) {
                String bomChange = packBomHistoryList.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                List<JSONObject> jsonObjects1 = JSONArray
                        .parseArray(change, JSONObject.class);
                if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                    // 新增
                    if (new Integer(1).equals(packBomHistoryList.get(i).getType())
                            || "A".equals(packBomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "初版提交");
                        object.put("type", "包装");
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(3).equals(packBomHistoryList.get(i).getType())) {
                        JSONObject object = new JSONObject();
                        object.put("detail", "新增工厂:");
                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        if (null != packBomHistoryList.get(i).getRemark()
                                && packBomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = packBomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "新增工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else if (new Integer(6).equals(packBomHistoryList.get(i).getType())) {

                        JSONObject object = new JSONObject();
                        object.put("detail", "删除工厂:");

                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        if (null != packBomHistoryList.get(i).getRemark()
                                && packBomHistoryList.get(i).getRemark().contains("&&lineRemark&&")) {
                            String[] split = packBomHistoryList.get(i).getRemark().split("&&lineRemark&&");
                            object.put("detail", "删除工厂:" + split[0]);
                            object.put("reason", split[1]);

                        }
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);
                    } else {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < jsonObjects1.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }

                            String serNum = "";
                            if (jsonObjects1.size() > 1) {
                                serNum = (j + 1) + "、";
                            }

                            if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                    || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + " 使用量:"
                                        + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                ? jsonObjects1.get(j).get("prePartUse")
                                                : jsonObjects1.get(j).get("partUse"));

                            }
                            if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                        + jsonObjects1.get(j).get("flag") + "子物料"
                                        + jsonObjects1.get(j).get("sapNumber") + "-"
                                        + jsonObjects1.get(j).get("partDescription")
                                        + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                        + jsonObjects1.get(j).get("prePartUse");
                            }
                        } */
                        String details = getAlterDetails(change);
                        object.put("productState",
                                getProductState2(packBomHistoryList.get(i).getProductState().intValue()));
                        object.put("detail", details);
                        object.put("type", "包装");
                        object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                        object.put("version", packBomHistoryList.get(i).getBomNo()
                                .split("-")[packBomHistoryList.get(i).getBomNo().split("-").length - 1]);
                        object.put("reason", packBomHistoryList.get(i).getRemark());
                        object.put("summitor", packBomHistoryList.get(i).getSummitor());
                        jsonObjects.add(object);

                        if (!"".equals(updateContent)) {
                            updateContent += "\n";
                        }

                        updateContent += details;
                    }
                }

            }

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            SysBom batteryBom = this.get(bomId);
            SysBom packBom = this.get(bomPackId);

            String code = "";
            String name = "";
            String version = "";
            try {
                code += bomHistoryList.get(bomHistoryList.size() - 1).getBomNo() + "-";
                String[] split1 = packBomHistoryList.get(packBomHistoryList.size() - 1).getBomNo().split("-");
                String[] split = bomHistoryList.get(bomHistoryList.size() - 1).getBomNo().split("-");
                code += split1[split1.length - 1];

                version = split[split.length - 1] + "-" +
                        split1[split1.length - 1];

                name = code + "-成品BOM-" + parent.getSapNumber()
                        + (StrUtil.isBlank(factory) ? "" : ("(适用于" + factory + ")"));
            } catch (Exception e) {
                throw new ServiceException(500, "关联的电芯bom或包装bom还未通过审核");
            }


            ReceiveNameParam endBomNameFromJIRA = getEndBomNameFromJIRA(sysBom);

            putPdfChcekName(context,endBomNameFromJIRA);

            context.putVar("summitor", getSummitor(sysBom.getId()));
            context.putVar("historyList", jsonObjects);
            context.putVar("bomList", result);
            context.putVar("sheetName", sheetNames);
            context.putVar("month", getChineseDate(sysBom.getCreateTime()));
            context.putVar("productType", batteryBom.getBomProductType());
            context.putVar("productProjectName", batteryBom.getProductProjectName());
            context.putVar("date", DateUtil.format(getEndBomDate(sysBom.getId()), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("bomName", parent.getPartDescription());
            context.putVar("bomCode", parent.getSapNumber());
            context.putVar("bomPartUse", parent.getPartUse());
            context.putVar("bomPartUnit", parent.getPartUnit());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(batteryBom.getBomIssueId());
            context.putVar("productStage", batteryBom.getProductStage());
            context.putVar("transport", getTransport(packBom.getBomTransport()));
            context.putVar("code", code);
            context.putVar("version", version);
            context.putVar("batFactVersion", batFactVersion);
            context.putVar("packFactVersion", packFactVersion);
            context.putVar("name", name);
            context.putVar("img", content);
            context.putVar("bomState", getProductState(batteryBom.getProductState()));

            context.putVar("status",
                    bomHistoryList.size() <= 1 && packBomHistoryList.size() <= 1 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废");
            setProductState(batteryBom.getProductState(), context);

            // 生成excel
            JxlsHelper.getInstance().processTemplate(inStream, os, context);

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");

                    for (int i = 0; i < (bomHistoryList.size() + packBomHistoryList.size()) * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 2) / 2;
                        Integer i2 = setHeight(row1, 3) / 2;
                        i1 = i1>i2?i1:i2;
                        row1.setHeight(Short.valueOf(i1.toString()));
                        row2.setHeight(Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

               /* while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 11, 11), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 12, 12), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 13), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 10, 13), sheet);

                }*/
                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new ServiceException(500, e.getMessage());
            } finally {
                fos.close();
            }

            // 验证license
            is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
            License aposeLic = new License();
            aposeLic.setLicense(is);


            if (sysBom.getOaId() != null) {
                byte[] imageByte = null;


                    imageByte = QRCodeUtil.createImageByte("utf-8",
                            ConstantContextHolder.getSysConfigWithDefault("oa_eve_url", String.class, "")
                                    + "km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                                    + sysBom.getOaId(),
                            100, 100);



                Workbook workbook1 = new Workbook(path);
                WorksheetCollection worksheets = workbook1.getWorksheets();
                for (int i = 0; i < 3; i++) {
                    Worksheet worksheet = worksheets.get(i);
                    worksheet.getPageSetup().setHeaderPicture(2, imageByte);
                    worksheet.getPageSetup().setHeaderMarginInch(0.08d);
                    worksheet.getPageSetup().setHeader(2, "&G");// 添加图片

                }
                workbook1.save(path);

            }





            // excel导出pdf
            Workbook wb = new Workbook(path);// 原始excel路径
            String pdfName = UUID.randomUUID().toString();
            String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOS = new FileOutputStream(new File(pdfPath));
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);

            File pdf = new File(pdfPath);
            pdfInputStream = new FileInputStream(pdf);
            MultipartFile multipartFile = new MockMultipartFile(pdf.getName(), pdf.getName(), null, pdfInputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_BUCKET_PDF);

            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");

            Map<String, Object> data = new HashMap<>();
            data.put("fileLink",
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + fileId);
            data.put("fileId", fileId);
            data.put("parentIssueId", batteryBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", "成品BOM");
            data.put("revisionContent", updateContent);
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());
            data.put("fileVersion", version);
            data.put("fileName", name);
            data.put("fileCode", code);

            /*
             * data.put("finishedMaterialCode", "998");
             * data.put("finishedBomId", 998);
             */
            //data.put("compilerId", "071716");

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, e.getMessage());
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

    }

    @Override
    public Map pdfUpdateAndCode( Long id, Boolean update, String remark, String alterType, ReceiveNameParam receiveNameParam)
            throws IOException {

        SysBom sysBom = get(id);

        if (null != sysBom.getBomNo()) {
            String[] split = sysBom.getBomNo().split("-");
            String oldCode = split[split.length - 2];
            if (!oldCode.equals(getProductState3(sysBom.getProductState()))) {
                split[split.length - 2] = getProductState3(sysBom.getProductState());
            }
            sysBom.setBomNo(Stream.of(split).collect(Collectors.joining("-")));
            this.updateById(sysBom);
        }

        String factVersion = "";

        if ("[]".equals(sysBom.getBomData()) || StrUtil.isBlank(sysBom.getBomData())) {
            throw new ServiceException(500, "请先搭建bom");
        }

        if (sysBom.getBomType().equals(1) && (sysBom.getBomTransport().equals("[]") || null == sysBom.getBomTransport())) {
            throw new ServiceException(500, "包装bom请选择运输方式");
        }

        SysBomSapServiceUtil sysBomSapServiceUtil = new SysBomSapServiceUtil();

        List<SysBomLine> sysBomLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

        List<TreeBom> treeBomsOwn = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
        /* if (sysBomSapServiceUtil.ifMengeEmpty(treeBomsOwn)) {
            log.error("BOM使用量不为0");
            throw new ServiceException(500, "BOM使用量不为0");
        } */

        List<SysWerkLine> _werkLines = werkLineService
                    .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

            List<String> werkNos = _werkLines.stream().map(SysWerkLine::getWerkNo).collect(Collectors.toList());
            
        List<BomSapProof> proofs = sysBomSapServiceUtil.getSapExendProof(treeBomsOwn,werkNos);

        if (proofs.size() > 0) {

            Map<String,Object> data = new HashMap<>();
            data.put("isExtend",false);
            data.put("proofs", proofs);
            return data;
        }

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : treeBomListMap.keySet()) {
                parent = key;
                break;
            }
            Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();
            map.put("name", parent.getPartDescription());
            map.put("code", parent.getSapNumber());
            map.put("fileCode", sysBom.getBomNo());
            map.put("version", sysBom.getBomVersion());
            map.put("fileName", sysBom.getBomName());
            map.put("partUnit", parent.getPartUnit());
            map.put("state", getProductState(sysBom.getProductState()));
            map.put("transport", getTransport(sysBom.getBomTransport()));
            map.put("partUse", parent.getPartUse());
            map.put("img", content);
            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();

                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += " " + key1 + "-" + value;

                        }

                    }
                    if (!"".equals(version)) {
                        factVersion = version;
                    }
                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("name", parent.getPartDescription());
            newMap.put("code", parent.getSapNumber());
            newMap.put("fileCode", sysBom.getBomNo());
            newMap.put("version", sysBom.getBomVersion());
            newMap.put("fileName", sysBom.getBomName());
            newMap.put("partUnit", parent.getPartUnit());
            newMap.put("state", getProductState(sysBom.getProductState()));
            newMap.put("transport", getTransport(sysBom.getBomTransport()));
            newMap.put("partUse", parent.getPartUse());
            newMap.put("img", content);
            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }

                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }

            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
                    update ? "BOM模板.xlsx" : "BOM模板" + sheetNames.size() + ".xlsx"));

            Map<String, Object> data = new HashMap<>();
            data.put("parentIssueId", sysBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");
            data.put("revisionContent", "");
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());

            // data.put("compilerId",
            // LoginContextHolder.me().getSysLoginUser().getAccount());

            SysBom bom = get(id);

            // 已使用
            if (!new Integer(0).equals(bom.getJiraIdStatus()) && update) {

                this.updateCodeVersionFileName(bom, null);

            }

            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
            queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                String bomChange = list.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                if (null != change) {
                    List<JSONObject> jsonObjects1 = JSONArray
                            .parseArray(change, JSONObject.class);
                    if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                        // 新增
                        if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                            JSONObject object = new JSONObject();
                            object.put("detail", "初版提交");
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(3).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "新增工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "新增工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(6).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "删除工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "删除工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));

                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        } else {
                            JSONObject object = new JSONObject();
                            /* String detail = "";
                            for (int j = 0; j < jsonObjects1.size(); j++) {
                                if (!"".equals(detail)) {
                                    detail += "\n";
                                }
                                String serNum = "";
                                if (jsonObjects1.size() > 1) {
                                    serNum = (j + 1) + "、";
                                }
                                if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                        || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + " 使用量:"
                                            + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                    ? jsonObjects1.get(j).get("prePartUse")
                                                    : jsonObjects1.get(j).get("partUse"));

                                }
                                if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                            + jsonObjects1.get(j).get("prePartUse");
                                }
                            } */

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("detail", getAlterDetails(change));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        }
                    }
                }

            }

            // SysBomHistory countByBomId =
            // bomHistoryService.getCountByBomId(sysBom.getId());

            if (update) {
                List<OaAddOrEditParam> lastHistory = this.getLastHistory(sysBom.getId());
                // 新增
                if (list.size() == 0) {
                    JSONObject object = new JSONObject();
                    object.put("detail", "初版提交");
                    object.put("bomStartdate", null == lastHistory ? DateUtil.format(new Date(), "yyyy-MM-dd")
                            : lastHistory.get(0).getBomStartdate());
                    object.put("version",
                            null == lastHistory ? sysBom.getBomVersion() : lastHistory.get(0).getVersion());
                    object.put("reason", remark);
                    object.put("summitor", LoginContextHolder.me().getSysLoginUser().getName());
                    object.put("productState", getProductState2(sysBom.getProductState()));
                    jsonObjects.add(object);
                } else {

                    if (lastHistory.size() != 0) {
                        JSONObject object = new JSONObject();
                        /* String detail = "";
                        for (int j = 0; j < lastHistory.size(); j++) {
                            if (!"".equals(detail)) {
                                detail += "\n";
                            }
                            String serNum = "";
                            if (lastHistory.size() > 1) {
                                serNum = (j + 1) + "、";
                            }
                            if ("新增".equals(lastHistory.get(j).getFlag())
                                    || "删除".equals(lastHistory.get(j).getFlag())) {
                                detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber()
                                        + lastHistory.get(j).getFlag()
                                        + "子物料"
                                        + lastHistory.get(j).getSapNumber() + "-"
                                        + lastHistory.get(j).getPartDescription()

                                        + "使用量:"
                                        + ("删除".equals(lastHistory.get(j).getFlag())
                                                ? lastHistory.get(j).getPrePartUse()
                                                : lastHistory.get(j).getPartUse());

                            }
                            if ("修改".equals(lastHistory.get(j).getFlag())) {
                                detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber()
                                        + lastHistory.get(j).getFlag()
                                        + "子物料"
                                        + lastHistory.get(j).getSapNumber() + "-"
                                        + lastHistory.get(j).getPartDescription()
                                        + "使用量:" + lastHistory.get(j).getPartUse() + "-变更前使用量:"
                                        + lastHistory.get(j).getPrePartUse();
                            }
                        } */
                        object.put("productState", getProductState2(sysBom.getProductState()));
                        object.put("detail", getAlterDetails(JSONObject.toJSONString(lastHistory)));
                        object.put("bomStartdate", DateUtil.format(new Date(), "yyyy-MM-dd"));
                        object.put("version", bom.getBomVersion());
                        object.put("reason", remark);
                        object.put("summitor", bom.getSummitor());
                        jsonObjects.add(object);
                    }

                }
            }

            int size1 = jsonObjects.size();

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            SysBom afterUpdateBom = this.getById(bom.getId());

            context.putVar("bomName", afterUpdateBom.getBomType() == 0 ? "电芯" : "包装");
            context.putVar("historyList", jsonObjects);
            context.putVar("version", afterUpdateBom.getBomVersion());
            context.putVar("name",
                    null != afterUpdateBom.getBomName() && afterUpdateBom.getBomName().contains("(适用于")
                            ? bom.getBomName().substring(0, afterUpdateBom.getBomName().indexOf("(适用于"))
                            : afterUpdateBom.getBomName());
            context.putVar("name1", afterUpdateBom.getBomName());
            context.putVar("code", afterUpdateBom.getBomNo());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(afterUpdateBom.getBomIssueId());
            context.putVar("productStage", afterUpdateBom.getProductStage());
            context.putVar("bomList", result);

            if (!update) {

                if (sysBom.getProductState() < 4) {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            "http://jira.evebattery.com/browse/"
                                    + bom.getOaId(),
                            150, 150));
                } else {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            ConstantContextHolder.getSysConfigWithDefault("oa_eve_url", String.class, "")
                                    + "km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                                    + bom.getOaId(),
                            150, 150));
                }

                putPdfChcekName(context, receiveNameParam);

            }

            context.putVar("sheetName", sheetNames);
            context.putVar("materialsName", parent.getPartDescription());
            context.putVar("materialsCode", parent.getSapNumber());
            context.putVar("partUnit", parent.getPartUnit());
            context.putVar("bomState", getProductState(sysBom.getProductState()));
            context.putVar("transport", getTransport(sysBom.getBomTransport()));

            context.putVar("factVersion", factVersion);
            context.putVar("month", getChineseDate(new Date()));
            String name =  afterUpdateBom.getSummitor();
            try {
                context.putVar("summitor",update?LoginContextHolder.me().getSysLoginUser().getName():afterUpdateBom.getSummitor());
            }catch (Exception e){
                context.putVar("summitor",afterUpdateBom.getSummitor());
                log.error("BOM预览获取提交人出错",e);
            }
            context.putVar("productType", afterUpdateBom.getBomProductType());
            context.putVar("productProjectName", afterUpdateBom.getProductProjectName());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("status", update ? (list.size() == 0 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废")
                    : (list.size() == 1 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废"));
            String page = "";
            for (int i = 0; i < sheetNames.size(); i++) {
                if ("".equals(page)) {
                    page = page + (3 + i);
                } else {
                    page = page + "," + (3 + i);
                }

            }

            setProductState(afterUpdateBom.getProductState(), context);

            try {
                // 生成excel
                JxlsHelper.getInstance().processTemplate(inStream, os, context);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");
                    for (int i = 0; i < size1 * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 1) / 2;
                        Integer i2 = setHeight(row1, 2) / 2;
                        //取大
                        i1 = i2 > i1?i2:i1;
                        row1.setHeight(Short.valueOf(i1.toString()));
                        row2.setHeight(Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 14), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 14, 15), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 15, 16), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(34, 35, 15, 16), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 14, 16), sheet);

                }

                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();

                // 验证license
                is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
                License aposeLic = new License();
                aposeLic.setLicense(is);

                if (!update) {
                    byte[] imageByte = null;
                    if (sysBom.getProductState() < 4) {

                        imageByte = QRCodeUtil.createImageByte("utf-8", "http://jira.evebattery.com/browse/"
                                + bom.getOaId(),
                                100, 100);
                    } else {

                        imageByte = QRCodeUtil.createImageByte("utf-8",
                                ConstantContextHolder.getSysConfigWithDefault("oa_eve_url", String.class, "")
                                        + "km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                                        + bom.getOaId(),
                                100, 100);

                    }

                    Workbook workbook1 = new Workbook(path);
                    WorksheetCollection worksheets = workbook1.getWorksheets();
                    for (int i = 0; i < sheetNames.size() + 2; i++) {
                        Worksheet worksheet = worksheets.get(i);
                        worksheet.getPageSetup().setHeaderPicture(2, imageByte);
                        worksheet.getPageSetup().setHeaderMarginInch(0.08d);
                        worksheet.getPageSetup().setHeader(2, "&G");// 添加图片

                    }
                    workbook1.save(path);

                }

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                fos.close();
            }

            /*
             * // 验证license
             * is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
             * "license.xml"));
             * License aposeLic = new License();
             * aposeLic.setLicense(is);
             */

            // excel导出pdf
            Workbook wb = new Workbook(path);// 原始excel路径
            String pdfName = UUID.randomUUID().toString();
            String pdfPath = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            fileOS = new FileOutputStream(new File(pdfPath));
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);

            File pdf = new File(pdfPath);
            pdfInputStream = new FileInputStream(pdf);
            MultipartFile multipartFile = new MockMultipartFile(pdf.getName(), pdf.getName(), null, pdfInputStream);
            Long fileId = sysFileInfoService.uploadFile(multipartFile, FileConfig.BOM_BUCKET_PDF);

            bom.setFileId(fileId);
            bom.setBomRemark(remark);
            bom.setJiraIdStatus(update ? 0 : 1);
            if (null != alterType) {
                bom.setAlterType(Integer.valueOf(alterType));
            }

            this.updateById(bom);

            fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, pdfName + ".pdf");
            //fileOperator.deleteFile(FileConfig.BOM_TMP_BUCKET, xlsxName + ".xlsx");
            data.put("isExtend",true);
            data.put("fileLink",
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + fileId);
            System.err.println(
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + fileId);
            data.put("fileId", fileId);
            data.put("id", bom.getJiraId());
            data.put("page", page);
            data.put("fileName", bom.getBomName());
            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, e.getMessage());
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

    }


    @Override
    public Map excelExport(Long id, Boolean update)
            throws IOException {

        SysBom sysBom = get(id);

        String factVersion = "";

        if ("[]".equals(sysBom.getBomData()) || StrUtil.isBlank(sysBom.getBomData())) {
            throw new ServiceException(500, "请先搭建bom");
        }

        if (sysBom.getBomType().equals(1) && (sysBom.getBomTransport().equals("[]") || null == sysBom.getBomTransport())) {
            throw new ServiceException(500, "包装bom请选择运输方式");
        }

        SysBomSapServiceUtil sysBomSapServiceUtil = new SysBomSapServiceUtil();

        List<SysBomLine> sysBomLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());

        List<TreeBom> treeBomsOwn = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
        if (sysBomSapServiceUtil.ifMengeEmpty(treeBomsOwn)) {
            log.error("BOM使用量不为0");
            throw new ServiceException(500, "BOM使用量不为0");
        }

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, sysBom.getBomName() + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : treeBomListMap.keySet()) {
                parent = key;
                break;
            }
            Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();
            map.put("name", parent.getPartDescription());
            map.put("code", parent.getSapNumber());
            map.put("fileCode", sysBom.getBomNo());
            map.put("version", sysBom.getBomVersion());
            map.put("fileName", sysBom.getBomName());
            map.put("partUnit", parent.getPartUnit());
            map.put("state", getProductState(sysBom.getProductState()));
            map.put("transport", getTransport(sysBom.getBomTransport()));
            map.put("partUse", parent.getPartUse());
            map.put("img", content);
            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();

                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += " " + key1 + "-" + value;

                        }

                    }
                    if (!"".equals(version)) {
                        factVersion = version;
                    }
                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("name", parent.getPartDescription());
            newMap.put("code", parent.getSapNumber());
            newMap.put("fileCode", sysBom.getBomNo());
            newMap.put("version", sysBom.getBomVersion());
            newMap.put("fileName", sysBom.getBomName());
            newMap.put("partUnit", parent.getPartUnit());
            newMap.put("state", getProductState(sysBom.getProductState()));
            newMap.put("transport", getTransport(sysBom.getBomTransport()));
            newMap.put("partUse", parent.getPartUse());
            newMap.put("img", content);
            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }

                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }

            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
                     "BOM模板.xlsx" ));

            Map<String, Object> data = new HashMap<>();
            data.put("parentIssueId", sysBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");
            data.put("revisionContent", "");
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());

            // data.put("compilerId",
            // LoginContextHolder.me().getSysLoginUser().getAccount());

            SysBom bom = get(id);

            // 已使用
            if (!new Integer(0).equals(bom.getJiraIdStatus()) && update) {

                this.updateCodeVersionFileName(bom, null);

            }

            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
            queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                String bomChange = list.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                if (null != change) {
                    List<JSONObject> jsonObjects1 = JSONArray
                            .parseArray(change, JSONObject.class);
                    if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                        // 新增
                        if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                            JSONObject object = new JSONObject();
                            object.put("detail", "初版提交");
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(3).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "新增工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "新增工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(6).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "删除工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "删除工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));

                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        } else {
                            JSONObject object = new JSONObject();
                            /* String detail = "";
                            for (int j = 0; j < jsonObjects1.size(); j++) {
                                if (!"".equals(detail)) {
                                    detail += "\n";
                                }
                                String serNum = "";
                                if (jsonObjects1.size() > 1) {
                                    serNum = (j + 1) + "、";
                                }
                                if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                        || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + " 使用量:"
                                            + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                    ? jsonObjects1.get(j).get("prePartUse")
                                                    : jsonObjects1.get(j).get("partUse"));

                                }
                                if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                            + jsonObjects1.get(j).get("prePartUse");
                                }
                            } */

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("detail", getAlterDetails(change));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        }
                    }
                }

            }

            // SysBomHistory countByBomId =
            // bomHistoryService.getCountByBomId(sysBom.getId());



            int size1 = jsonObjects.size();

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            SysBom afterUpdateBom = this.getById(bom.getId());

            context.putVar("bomName", afterUpdateBom.getBomType() == 0 ? "电芯" : "包装");
            context.putVar("historyList", jsonObjects);
            context.putVar("version", afterUpdateBom.getBomVersion());
            context.putVar("name",
                    null != afterUpdateBom.getBomName() && afterUpdateBom.getBomName().contains("(适用于")
                            ? bom.getBomName().substring(0, afterUpdateBom.getBomName().indexOf("(适用于"))
                            : afterUpdateBom.getBomName());
            context.putVar("name1", afterUpdateBom.getBomName());
            context.putVar("code", afterUpdateBom.getBomNo());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(afterUpdateBom.getBomIssueId());
            context.putVar("productStage", afterUpdateBom.getProductStage());
            context.putVar("bomList", result);

            if (!update) {

                if (sysBom.getProductState() < 4) {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            "http://jira.evebattery.com/browse/"
                                    + bom.getOaId(),
                            150, 150));
                } else {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            ConstantContextHolder.getSysConfigWithDefault("oa_eve_url", String.class, "")
                                    + "km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                                    + bom.getOaId(),
                            150, 150));
                }


            }
            List<String> sheetNamesString = new ArrayList<>();
            for (int i = 0; i < sheetNames.size(); i++) {
                sheetNamesString.add("物料清单" + (i+1));
            }

            context.putVar("sheetName", sheetNamesString);
            context.putVar("materialsName", parent.getPartDescription());
            context.putVar("materialsCode", parent.getSapNumber());
            context.putVar("partUnit", parent.getPartUnit());
            context.putVar("bomState", getProductState(sysBom.getProductState()));
            context.putVar("transport", getTransport(sysBom.getBomTransport()));

            context.putVar("factVersion", factVersion);
            context.putVar("month", getChineseDate(new Date()));
            String name =  afterUpdateBom.getSummitor();
            try {
                context.putVar("summitor",update?LoginContextHolder.me().getSysLoginUser().getName():afterUpdateBom.getSummitor());
            }catch (Exception e){
                context.putVar("summitor",afterUpdateBom.getSummitor());
                log.error("BOM预览获取提交人出错",e);
            }
            context.putVar("productType", afterUpdateBom.getBomProductType());
            context.putVar("productProjectName", afterUpdateBom.getProductProjectName());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("status", update ? (list.size() == 0 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废")
                    : (list.size() == 1 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废"));
            String page = "";
            for (int i = 0; i < sheetNames.size(); i++) {
                if ("".equals(page)) {
                    page = page + (3 + i);
                } else {
                    page = page + "," + (3 + i);
                }

            }

            setProductState(afterUpdateBom.getProductState(), context);

            try {



                // 生成excel
                JxlsHelper.getInstance().processTemplate(inStream, os, context);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");
                    for (int i = 0; i < size1 * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 1) / 2;
                        Integer i2 = setHeight(row1, 2) / 2;
                        //取大
                        i1 = i2 > i1?i2:i1;
                        row1.setHeight(Short.valueOf(i1.toString()));
                        row2.setHeight(Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 14), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 14, 15), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 15, 16), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(34, 35, 15, 16), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 14, 16), sheet);

                }

                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();

                // 验证license
                is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
                License aposeLic = new License();
                aposeLic.setLicense(is);


            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                fos.close();
            }

            /*
             * // 验证license
             * is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
             * "license.xml"));
             * License aposeLic = new License();
             * aposeLic.setLicense(is);
             */

            // excel导出pdf

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, e.getMessage());
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

    }

    @Override
    public Map excelExportByHistory(Long id, Boolean update)
            throws IOException {

        SysBom sysBom = get(id);

        String factVersion = "";
        
        SysBomSapServiceUtil sysBomSapServiceUtil = new SysBomSapServiceUtil();

        List<SysBomLine> sysBomLines = bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());


        LambdaQueryWrapper<SysBomHistory> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(SysBomHistory::getBomId,id);
        queryWrapper1.orderByDesc(SysBomHistory::getCreateTime);
        List<SysBomHistory> histories = this.bomHistoryService.list(queryWrapper1);
        SysBomHistory sysBomHistory = histories.get(0);
        List<TreeBom> treeBomsOwn = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);
        if (sysBomSapServiceUtil.ifMengeEmpty(treeBomsOwn)) {
            log.error("BOM使用量不为0");
            throw new ServiceException(500, "BOM使用量不为0");
        }

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        InputStream inStream = null;
        OutputStream os = null;
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        InputStream is = null;
        FileOutputStream fileOS = null;
        FileInputStream pdfInputStream = null;

        try {

            // 生成的excel
            String xlsxName = UUID.randomUUID().toString();
            String path = fileOperator.newEmptyFile(FileConfig.BOM_TMP_BUCKET, sysBom.getBomName() + ".xlsx");
            os = new FileOutputStream(path);
            // 模板内亿纬动力图片
            in = new BufferedInputStream(
                    new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "logo.png")));
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();

            // 页名称
            List<Object> sheetNames = new ArrayList<>();
            List<Map> result = new ArrayList<>();

            List<TreeBom> treeBoms = null;

            treeBoms = JSONObject.parseArray(sysBomHistory.getBomData(), TreeBom.class);

            sysBomServiceUtil.putLevel(treeBoms);

            Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);

            TreeBom parent = null;

            for (TreeBom key : treeBomListMap.keySet()) {
                parent = key;
                break;
            }
            Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

            List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel))
                    .collect(Collectors.toList());

            Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

            for (int i = 0; i < collect.size(); i++) {
                map1.put(collect.get(i), treeBomListMap1.get(collect.get(i)));
            }

            // 初始化数据
            Map<String, Object> map = new HashMap<>();
            map.put("name", parent.getPartDescription());
            map.put("code", parent.getSapNumber());
            map.put("fileCode", sysBom.getBomNo());
            map.put("version", sysBom.getBomVersion());
            map.put("fileName", sysBom.getBomName());
            map.put("partUnit", parent.getPartUnit());
            map.put("state", getProductState(sysBom.getProductState()));
            map.put("transport", getTransport(sysBom.getBomTransport()));
            map.put("partUse", parent.getPartUse());
            map.put("img", content);
            List<Map> boms = new ArrayList<>();
            for (TreeBom key : map1.keySet()) {
                List<TreeBom> treeBoms1 = map1.get(key);
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("partDescription", key.getPartDescription());
                objectObjectHashMap.put("partNumber", key.getSapNumber());
                objectObjectHashMap.put("length", treeBoms1.size());
                if (!StrUtil.isBlank(key.getVersion())) {
                    String version = "";
                    JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key1 = entry.getKey();
                        Object value = entry.getValue();

                        if ("".equals(version)) {
                            version += key1 + "-" + value;
                        } else {
                            version += " " + key1 + "-" + value;

                        }

                    }
                    if (!"".equals(version)) {
                        factVersion = version;
                    }
                    objectObjectHashMap.put("version", version);
                }

                objectObjectHashMap.put("inBom", treeBoms1);

                boms.add(objectObjectHashMap);
            }
            map.put("data", boms);

            // 数据分页处理
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("name", parent.getPartDescription());
            newMap.put("code", parent.getSapNumber());
            newMap.put("fileCode", sysBom.getBomNo());
            newMap.put("version", sysBom.getBomVersion());
            newMap.put("fileName", sysBom.getBomName());
            newMap.put("partUnit", parent.getPartUnit());
            newMap.put("state", getProductState(sysBom.getProductState()));
            newMap.put("transport", getTransport(sysBom.getBomTransport()));
            newMap.put("partUse", parent.getPartUse());
            newMap.put("img", content);
            List<Map> bomList = new ArrayList<>();
            List<Map> outBoms = (List<Map>) map.get("data");
            int num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                for (int j = 0; j < inBoms.size(); j++) {
                    num++;
                    inBoms.get(j).setIndex(num);
                }
            }
            num = 0;
            for (int i = 0; i < outBoms.size(); i++) {
                List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                if (inBoms.size() <= 25 - num) {
                    objectObjectHashMap.put("inBom", inBoms);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", inBoms.size());
                    bomList.add(objectObjectHashMap);
                    objectObjectHashMap = new HashMap<>();
                    num += inBoms.size();

                    if (i == outBoms.size() - 1) {

                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }
                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }

                        newMap.put("data", bomList);
                        sheetNames.add("表格" + Math.random());
                        result.add(newMap);
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                } else if (inBoms.size() > 25 - num) {
                    int need = 25 - num;
                    List<TreeBom> full = new ArrayList<>();
                    for (int j = 0; j < 25 - num; j++) {
                        full.add(inBoms.get(j));
                    }
                    objectObjectHashMap.put("inBom", full);
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", full.size());
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    bomList = new ArrayList<>();
                    newMap = new HashMap<>();
                    objectObjectHashMap = new HashMap<>();
                    num = 0;

                    if (inBoms.size() - need > 25) {
                        List<TreeBom> full25 = new ArrayList<>();
                        for (int k = need; k < 25 + need; k++) {
                            full25.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", full25);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", full25.size());
                        bomList.add(objectObjectHashMap);
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());
                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                        objectObjectHashMap = new HashMap<>();
                        num = 0;

                        List<TreeBom> other = new ArrayList<>();
                        for (int k = need + 25; k < inBoms.size(); k++) {
                            other.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - (need + 25));

                    } else {
                        List<TreeBom> other1 = new ArrayList<>();
                        for (int k = need; k < inBoms.size(); k++) {
                            other1.add(inBoms.get(k));
                        }
                        objectObjectHashMap.put("inBom", other1);
                        objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                        objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                        objectObjectHashMap.put("length", other1.size());
                        bomList.add(objectObjectHashMap);
                        num += (inBoms.size() - need);
                    }

                    if (i == outBoms.size() - 1) {
                        Integer lastNum = 0;
                        for (int l = 0; l < bomList.size(); l++) {
                            Map map2 = bomList.get(l);
                            Integer length = (Integer) map2.get("length");
                            lastNum += length;
                        }

                        for (int n = lastNum; n < 25; n++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                        bomList = new ArrayList<>();
                        newMap = new HashMap<>();
                    }

                }

            }

            // 模板
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
                     "BOM模板.xlsx" ));

            Map<String, Object> data = new HashMap<>();
            data.put("parentIssueId", sysBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");
            data.put("revisionContent", "");
            data.put("revisionReason", "");
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());

            // data.put("compilerId",
            // LoginContextHolder.me().getSysLoginUser().getAccount());

            SysBom bom = get(id);

            // 已使用
            if (!new Integer(0).equals(bom.getJiraIdStatus()) && update) {

                this.updateCodeVersionFileName(bom, null);

            }

            Context context = new Context();
            List<JSONObject> jsonObjects = new LinkedList<>();

            LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
            queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
            List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                String bomChange = list.get(i).getBomChange();
                String change = JSONObject.parseObject(bomChange).getString("changes");
                if (null != change) {
                    List<JSONObject> jsonObjects1 = JSONArray
                            .parseArray(change, JSONObject.class);
                    if (null != jsonObjects1 && jsonObjects1.size() > 0) {
                        // 新增
                        if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                            JSONObject object = new JSONObject();
                            object.put("detail", "初版提交");
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(3).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "新增工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "新增工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("summitor", list.get(i).getSummitor());
                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            jsonObjects.add(object);
                        } else if (new Integer(6).equals(list.get(i).getType())) {

                            JSONObject object = new JSONObject();
                            object.put("detail", "删除工厂:");
                            object.put("reason", list.get(i).getRemark());
                            if (null != list.get(i).getRemark() && list.get(i).getRemark().contains("&&lineRemark&&")) {
                                String[] split = list.get(i).getRemark().split("&&lineRemark&&");
                                object.put("detail", "删除工厂:" + split[0]);
                                object.put("reason", split[1]);

                            }

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));

                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        } else {
                            JSONObject object = new JSONObject();
                            /* String detail = "";
                            for (int j = 0; j < jsonObjects1.size(); j++) {
                                if (!"".equals(detail)) {
                                    detail += "\n";
                                }
                                String serNum = "";
                                if (jsonObjects1.size() > 1) {
                                    serNum = (j + 1) + "、";
                                }
                                if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                        || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + " 使用量:"
                                            + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                                    ? jsonObjects1.get(j).get("prePartUse")
                                                    : jsonObjects1.get(j).get("partUse"));

                                }
                                if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                                    detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                            + jsonObjects1.get(j).get("flag") + "子物料"
                                            + jsonObjects1.get(j).get("sapNumber") + "-"
                                            + jsonObjects1.get(j).get("partDescription")
                                            + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                            + jsonObjects1.get(j).get("prePartUse");
                                }
                            } */

                            object.put("productState", getProductState2(list.get(i).getProductState().intValue()));
                            object.put("detail", getAlterDetails(change));
                            object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                            object.put("version", jsonObjects1.get(0).getString("version"));
                            object.put("reason", list.get(i).getRemark());
                            object.put("summitor", list.get(i).getSummitor());
                            jsonObjects.add(object);
                        }
                    }
                }

            }

            // SysBomHistory countByBomId =
            // bomHistoryService.getCountByBomId(sysBom.getId());



            int size1 = jsonObjects.size();

            if (jsonObjects.size() < 25) {
                int i1 = 25 - jsonObjects.size();

                for (int i = 0; i < i1; i++) {
                    jsonObjects.add(new JSONObject());
                }
            }

            SysBom afterUpdateBom = this.getById(bom.getId());

            context.putVar("bomName", afterUpdateBom.getBomType() == 0 ? "电芯" : "包装");
            context.putVar("historyList", jsonObjects);
            context.putVar("version", afterUpdateBom.getBomVersion());
            context.putVar("name",
                    null != afterUpdateBom.getBomName() && afterUpdateBom.getBomName().contains("(适用于")
                            ? bom.getBomName().substring(0, afterUpdateBom.getBomName().indexOf("(适用于"))
                            : afterUpdateBom.getBomName());
            context.putVar("name1", afterUpdateBom.getBomName());
            context.putVar("code", afterUpdateBom.getBomNo());
            //查询实时的项目阶段
//            ProductManager productManager = productManagerService.getById(afterUpdateBom.getBomIssueId());
            context.putVar("productStage", afterUpdateBom.getProductStage());
            context.putVar("bomList", result);

            if (!update) {

                if (sysBom.getProductState() < 4) {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            "http://jira.evebattery.com/browse/"
                                    + bom.getOaId(),
                            150, 150));
                } else {
                    context.putVar("QRcode", QRCodeUtil.createImageByte("utf-8",
                            ConstantContextHolder.getSysConfigWithDefault("oa_eve_url", String.class, "")
                                    + "km/review/km_review_main/kmReviewMain.do?method=view&fdId="
                                    + bom.getOaId(),
                            150, 150));
                }


            }
            List<String> sheetNamesString = new ArrayList<>();
            for (int i = 0; i < sheetNames.size(); i++) {
                sheetNamesString.add("物料清单" + (i+1));
            }

            context.putVar("sheetName", sheetNamesString);
            context.putVar("materialsName", parent.getPartDescription());
            context.putVar("materialsCode", parent.getSapNumber());
            context.putVar("partUnit", parent.getPartUnit());
            context.putVar("bomState", getProductState(sysBom.getProductState()));
            context.putVar("transport", getTransport(sysBom.getBomTransport()));

            context.putVar("factVersion", factVersion);
            context.putVar("month", getChineseDate(new Date()));
            String name =  afterUpdateBom.getSummitor();
            try {
                context.putVar("summitor",update?LoginContextHolder.me().getSysLoginUser().getName():afterUpdateBom.getSummitor());
            }catch (Exception e){
                context.putVar("summitor",afterUpdateBom.getSummitor());
                log.error("BOM预览获取提交人出错",e);
            }
            context.putVar("productType", afterUpdateBom.getBomProductType());
            context.putVar("productProjectName", afterUpdateBom.getProductProjectName());
            context.putVar("date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            context.putVar("factory", factory);
            context.putVar("status", update ? (list.size() == 0 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废")
                    : (list.size() == 1 ? "■ 制订 □ 修订 □ 作废" : "□ 制订 ■ 修订 □ 作废"));
            String page = "";
            for (int i = 0; i < sheetNames.size(); i++) {
                if ("".equals(page)) {
                    page = page + (3 + i);
                } else {
                    page = page + "," + (3 + i);
                }

            }

            setProductState(afterUpdateBom.getProductState(), context);

            try {



                // 生成excel
                JxlsHelper.getInstance().processTemplate(inStream, os, context);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 表格无边框处理
            File file = new File(path);
            FileOutputStream fos = null;
            try (InputStream fis = new FileInputStream(file);
                    XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
                // 不关后面写不进去
                fis.close();
                Iterator<Sheet> sheetIterator = workbook.iterator();

                try {
                    XSSFSheet sheet2 = workbook.getSheet("Sheet2");
                    for (int i = 0; i < size1 * 2; i++) {
                        XSSFRow row1 = sheet2.getRow(7 + i);
                        XSSFRow row2 = sheet2.getRow(8 + i);
                        Integer i1 = setHeight(row1, 1) / 2;
                        Integer i2 = setHeight(row1, 2) / 2;
                        //取大
                        i1 = i2 > i1?i2:i1;
                        row1.setHeight(Short.valueOf(i1.toString()));
                        row2.setHeight(Short.valueOf(i1.toString()));
                        i++;
                    }
                } catch (Exception e) {

                }

                while (sheetIterator.hasNext()) {
                    Sheet sheet = (Sheet) sheetIterator.next();
                    if (sheet.getSheetName().equals("Sheet1") || sheet.getSheetName().equals("Sheet2")) {
                        continue;
                    }
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 13, 14), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 14, 15), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(10, 34, 15, 16), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, new CellRangeAddress(34, 35, 15, 16), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, new CellRangeAddress(34, 34, 14, 16), sheet);

                }

                fos = new FileOutputStream(file);
                // 重新写回该文件
                workbook.write(fos);
                fos.flush();

                // 验证license
                is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL, "license.xml"));
                License aposeLic = new License();
                aposeLic.setLicense(is);


            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                fos.close();
            }

            /*
             * // 验证license
             * is = new FileInputStream(fileOperator.newEmptyFile(FileConfig.BOM_TMP_MODEL,
             * "license.xml"));
             * License aposeLic = new License();
             * aposeLic.setLicense(is);
             */

            // excel导出pdf

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, e.getMessage());
        } finally {
            if (null != inStream) {
                inStream.close();
            }
            if (null != os) {
                os.close();
            }
            if (null != in) {
                in.close();
            }
            if (null != out) {
                out.close();
            }
            if (null != is) {
                is.close();
            }
            if (null != fileOS) {
                fileOS.close();
            }
            if (null != pdfInputStream) {
                pdfInputStream.close();
            }
        }

    }

    private void putPdfChcekName(Context context, ReceiveNameParam receiveNameParam) {

        if (null == receiveNameParam) {
            return;
        }

        List<ReceiveNameInParam> reviewer = receiveNameParam.getReviewer();
        if (null != reviewer) {
            for (int i = 0; i < reviewer.size(); i++) {
                Ao8de701UserSignature ao8de701UserSignature = signatureService.querySignByNum(reviewer.get(i).getId());
                if (null != ao8de701UserSignature) {
                    context.putVar("reviewer" + i,
                            Base64.getDecoder().decode(ao8de701UserSignature.getSignaturePicture()
                                    .trim()));
                } else {
                    context.putVar("reviewerName" + i, reviewer.get(i).getName());
                }
            }
        }

        ReceiveNameInParam approver = receiveNameParam.getApprover();
        String approverDate = receiveNameParam.getApproverDate();
        List<String> reviewerDate = receiveNameParam.getReviewerDate();

        if (null != approver) {
            Ao8de701UserSignature ao8de701UserSignature = signatureService.querySignByNum(approver.getId());

            if (null != ao8de701UserSignature) {
                context.putVar("checkMan", Base64.getDecoder().decode(ao8de701UserSignature.getSignaturePicture()
                        .trim()));
            } else {
                context.putVar("checkManName", approver.getName());
            }

        }
        if (null != reviewerDate) {
            for (int i = 0; i < reviewerDate.size(); i++) {
                context.putVar("date" + i, reviewerDate.get(i));
            }
        }

        context.putVar("checkManDate", approverDate);

    }

    public Map jiraData(Long id, Boolean update) throws IOException {

        SysBom sysBom = get(id);

        List<JSONObject> jsonObjects = new LinkedList<>();

        LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBomHistory::getBomId, sysBom.getId());
        queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
        List<SysBomHistory> list = bomHistoryService.list(queryWrapper);
        for (int i = 0; i < list.size(); i++) {
            String bomChange = list.get(i).getBomChange();
            String change = JSONObject.parseObject(bomChange).getString("changes");
            List<JSONObject> jsonObjects1 = JSONArray.parseArray(change,
                    JSONObject.class);
            if (null != jsonObjects1) {
                // 新增
                if (new Integer(1).equals(list.get(i).getType()) || "A".equals(list.get(i).getType())) {
                    JSONObject object = new JSONObject();
                    object.put("detail", "初版提交");
                    object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                    object.put("version", jsonObjects1.get(0).getString("version"));
                    object.put("reason", list.get(i).getRemark());
                    jsonObjects.add(object);
                } else if (new Integer(3).equals(list.get(i).getType())) {
                    JSONObject object = new JSONObject();
                    object.put("detail", "新增工厂: " + list.get(i).getRemark());
                    object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                    object.put("version", jsonObjects1.get(0).getString("version"));
                    object.put("reason", "增加工厂");
                    object.put("summitor", list.get(i).getSummitor());
                    jsonObjects.add(object);
                } else if (new Integer(6).equals(list.get(i).getType())) {
                    JSONObject object = new JSONObject();
                    object.put("detail", "删除工厂: " + list.get(i).getRemark());
                    object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                    object.put("version", jsonObjects1.get(0).getString("version"));
                    object.put("reason", "减少工厂");
                    object.put("summitor", list.get(i).getSummitor());
                    jsonObjects.add(object);
                } else {
                    JSONObject object = new JSONObject();
                    /* String detail = "";
                    for (int j = 0; j < jsonObjects1.size(); j++) {
                        if (!"".equals(detail)) {
                            detail += "\n";
                        }
                        String serNum = "";
                        if (jsonObjects1.size() > 1) {
                            serNum = (j + 1) + "、";
                        }
                        if ("新增".equals(jsonObjects1.get(j).get("flag"))
                                || "删除".equals(jsonObjects1.get(j).get("flag"))) {
                            detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                    + jsonObjects1.get(j).get("flag") + "子物料"
                                    + jsonObjects1.get(j).get("sapNumber") + "-"
                                    + jsonObjects1.get(j).get("partDescription")
                                    + " 使用量:"
                                    + ("删除".equals(jsonObjects1.get(j).get("flag"))
                                            ? jsonObjects1.get(j).get("prePartUse")
                                            : jsonObjects1.get(j).get("partUse"));

                        }
                        if ("修改".equals(jsonObjects1.get(j).get("flag"))) {
                            detail += serNum + "主物料" + jsonObjects1.get(j).get("mSapNumber")
                                    + jsonObjects1.get(j).get("flag") + "子物料"
                                    + jsonObjects1.get(j).get("sapNumber") + "-"
                                    + jsonObjects1.get(j).get("partDescription")
                                    + "使用量:" + jsonObjects1.get(j).get("partUse") + "-变更前使用量:"
                                    + jsonObjects1.get(j).get("prePartUse");
                        }
                    } */

                    object.put("detail", getAlterDetails(change));
                    object.put("bomStartdate", jsonObjects1.get(0).getString("bomStartdate"));
                    object.put("version", jsonObjects1.get(0).getString("version"));
                    object.put("reason", list.get(i).getRemark());
                    jsonObjects.add(object);
                }
            }

        }

        // SysBomHistory countByBomId =
        // bomHistoryService.getCountByBomId(sysBom.getId());
        List<OaAddOrEditParam> lastHistory = this.getLastHistory(sysBom.getId());
        // 新增
        if (list.size() == 0) {
            JSONObject object = new JSONObject();
            object.put("detail", "初版提交");
            object.put("bomStartdate", null == lastHistory ? DateUtil.format(new Date(), "yyyy-MM-dd")
                    : lastHistory.get(0).getBomStartdate());
            object.put("version", null == lastHistory ? sysBom.getBomVersion() : lastHistory.get(0).getVersion());

            jsonObjects.add(object);
        } else {

            if (lastHistory.size() != 0) {
                JSONObject object = new JSONObject();
                /* String detail = "";
                for (int j = 0; j < lastHistory.size(); j++) {
                    if (!"".equals(detail)) {
                        detail += "\n";
                    }
                    String serNum = "";
                    if (lastHistory.size() > 1) {
                        serNum = (j + 1) + "、";
                    }
                    if ("新增".equals(lastHistory.get(j).getFlag()) || "删除".equals(lastHistory.get(j).getFlag())) {
                        detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber() + lastHistory.get(j).getFlag()
                                + "子物料"
                                + lastHistory.get(j).getSapNumber() + "-"
                                + lastHistory.get(j).getPartDescription()

                                + "使用量:"
                                + ("删除".equals(lastHistory.get(j).getFlag()) ? lastHistory.get(j).getPrePartUse()
                                        : lastHistory.get(j).getPartUse());

                    }
                    if ("修改".equals(lastHistory.get(j).getFlag())) {
                        detail += serNum + "主物料" + lastHistory.get(j).getMSapNumber() + lastHistory.get(j).getFlag()
                                + "子物料"
                                + lastHistory.get(j).getSapNumber() + "-"
                                + lastHistory.get(j).getPartDescription()
                                + "使用量:" + lastHistory.get(j).getPartUse() + "-变更前使用量:"
                                + lastHistory.get(j).getPrePartUse();
                    }
                } */

                object.put("detail", getAlterDetails(JSONObject.toJSONString(lastHistory)));
                object.put("bomStartdate", DateUtil.format(new Date(), "yyyy-MM-dd"));
                object.put("version", lastHistory.get(0).getVersion());
                jsonObjects.add(object);
            }

        }

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(sysBom.getBomIssueId());
        List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        sysBom.setLines(new ArrayList<>());
        if (lineMap.containsKey(sysBom.getId())) {
            sysBom.getLines().addAll(
                    lineMap.get(sysBom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = sysBom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        // 页名称
        List<Object> sheetNames = new ArrayList<>();
        List<Map> result = new ArrayList<>();

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        List<TreeBom> treeBoms = null;
        // 取合并数据
        /*
         * if (update) {
         * treeBoms = endBom(sysBom.getId());
         * // 取当前bom数据
         * } else {
         */
        treeBoms = JSONObject.parseArray(sysBom.getBomData(), TreeBom.class);
        // }
        Map<TreeBom, List<TreeBom>> map1 = sysBomServiceUtil.loopTreeBom1(treeBoms);

        TreeBom parent = null;

        for (TreeBom key : map1.keySet()) {
            parent = key;
            break;
        }


        map1 = sysBomServiceUtil.loopTreeBom1(treeBoms);
        // 初始化数据
        Map<String, Object> map = new HashMap<>();
        map.put("name", parent.getPartDescription());
        map.put("code", parent.getSapNumber());
        map.put("fileCode", sysBom.getBomNo());
        map.put("version", sysBom.getBomVersion());
        map.put("fileName", sysBom.getBomName());
        map.put("partUnit", parent.getPartUnit());
        map.put("state", getProductState(sysBom.getProductState()));
        map.put("transport", getTransport(sysBom.getBomTransport()));
        map.put("partUse", parent.getPartUse());
        List<Map> boms = new ArrayList<>();
        for (TreeBom key : map1.keySet()) {
            List<TreeBom> treeBoms1 = map1.get(key);
            Map<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("partDescription", key.getPartDescription());
            objectObjectHashMap.put("partNumber", key.getSapNumber());
            objectObjectHashMap.put("length", treeBoms1.size());
            if (!StrUtil.isBlank(key.getVersion())) {
                String version = "";
                JSONObject jsonObject = JSONObject.parseObject(key.getVersion());
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    String key1 = entry.getKey();
                    Object value = entry.getValue();

                    if ("".equals(version)) {
                        version += key1 + "-" + value;
                    } else {
                        version += "\n" + key1 + "-" + value;

                    }

                }

                objectObjectHashMap.put("version", version);
            }

            objectObjectHashMap.put("inBom", treeBoms1);

            boms.add(objectObjectHashMap);
        }
        map.put("data", boms);

        // 数据分页处理
        Map<String, Object> newMap = new HashMap<>();
        newMap.put("name", parent.getPartDescription());
        newMap.put("code", parent.getSapNumber());
        newMap.put("fileCode", sysBom.getBomNo());
        newMap.put("version", sysBom.getBomVersion());
        newMap.put("fileName", sysBom.getBomName());
        newMap.put("partUnit", parent.getPartUnit());
        newMap.put("state", getProductState(sysBom.getProductState()));
        newMap.put("transport", getTransport(sysBom.getBomTransport()));
        newMap.put("partUse", parent.getPartUse());
        List<Map> bomList = new ArrayList<>();
        List<Map> outBoms = (List<Map>) map.get("data");
        int num = 0;
        for (int i = 0; i < outBoms.size(); i++) {
            List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
            for (int j = 0; j < inBoms.size(); j++) {
                num++;
                inBoms.get(j).setIndex(num);
            }
        }
        num = 0;
        for (int i = 0; i < outBoms.size(); i++) {
            List<TreeBom> inBoms = (List<TreeBom>) outBoms.get(i).get("inBom");
            Map<String, Object> objectObjectHashMap = new HashMap<>();
            for (int j = 0; j < inBoms.size(); j++) {
                num++;

                // 第二十条数据
                if (num <= 25 && j == inBoms.size() - 1) {
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("length", outBoms.get(i).get("length"));
                    objectObjectHashMap.put("version", outBoms.get(i).get("version"));
                    objectObjectHashMap.put("inBom", inBoms);
                    bomList.add(objectObjectHashMap);

                    if (num == 25) {
                        newMap.put("data", bomList);
                        result.add(newMap);
                        newMap = new HashMap<>();
                        newMap.put("name", parent.getPartDescription());
                        newMap.put("code", parent.getSapNumber());
                        newMap.put("fileCode", sysBom.getBomNo());
                        newMap.put("version", sysBom.getBomVersion());
                        newMap.put("fileName", sysBom.getBomName());
                        newMap.put("partUnit", parent.getPartUnit());
                        newMap.put("state", getProductState(sysBom.getProductState()));
                        newMap.put("transport", getTransport(sysBom.getBomTransport()));
                        newMap.put("partUse", parent.getPartUse());
                        bomList = new ArrayList<>();
                        objectObjectHashMap = new HashMap<>();
                        sheetNames.add("表格" + Math.random());
                        num = 0;
                    }

                    if (i == outBoms.size() - 1) {

                        for (int k = num; k < 25; k++) {
                            Map<String, Object> fullMap = new HashMap<>();
                            fullMap.put("length", 1);
                            List<TreeBom> fullBom = new ArrayList<>();
                            fullBom.add(null);
                            fullMap.put("inBom", fullBom);
                            bomList.add(fullMap);
                        }
                        newMap.put("data", bomList);
                        result.add(newMap);
                        sheetNames.add("表格" + Math.random());

                    }

                } else if (num == 25 && j < inBoms.size() - 1) {

                    int limit = j + 1;
                    List<TreeBom> one = new ArrayList<>();
                    for (int k = 0; k < limit; k++) {
                        one.add(inBoms.get(k));
                    }
                    Map<String, Object> oneMap = new HashMap<>();
                    oneMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    oneMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    oneMap.put("version", outBoms.get(i).get("version"));
                    oneMap.put("length", limit);
                    oneMap.put("inBom", one);

                    bomList.add(oneMap);
                    newMap.put("data", bomList);
                    result.add(newMap);
                    sheetNames.add("表格" + Math.random());
                    newMap = new HashMap<>();
                    newMap.put("name", parent.getPartDescription());
                    newMap.put("code", parent.getSapNumber());
                    newMap.put("fileCode", sysBom.getBomNo());
                    newMap.put("version", sysBom.getBomVersion());
                    newMap.put("fileName", sysBom.getBomName());
                    newMap.put("partUnit", parent.getPartUnit());
                    newMap.put("state", getProductState(sysBom.getProductState()));
                    newMap.put("transport", getTransport(sysBom.getBomTransport()));
                    newMap.put("partUse", parent.getPartUse());
                    bomList = new ArrayList<>();

                    List<TreeBom> other = new ArrayList<>();
                    for (int l = limit; l < inBoms.size(); l++) {
                        other.add(inBoms.get(l));
                    }

                    Map<String, Object> otherMap = new HashMap<>();
                    otherMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    otherMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    otherMap.put("version", outBoms.get(i).get("version"));
                    otherMap.put("length", inBoms.size() - limit);
                    otherMap.put("inBom", other);
                    num = inBoms.size() - limit;
                    bomList.add(otherMap);
                    break;

                } else if (i == outBoms.size() - 1 && j == inBoms.size() - 1) {
                    objectObjectHashMap.put("partDescription", outBoms.get(i).get("partDescription"));
                    objectObjectHashMap.put("partNumber", outBoms.get(i).get("partNumber"));
                    objectObjectHashMap.put("version", outBoms.get(i).get("version"));
                    objectObjectHashMap.put("length", outBoms.get(i).get("length"));
                    objectObjectHashMap.put("inBom", inBoms);
                    bomList.add(objectObjectHashMap);
                    newMap.put("data", bomList);
                    sheetNames.add("表格" + Math.random());
                    result.add(newMap);
                }

            }
        }

        String page = "";
        for (int i = 0; i < sheetNames.size(); i++) {
            if ("".equals(page)) {
                page = page + (3 + i);
            } else {
                page = page + "," + (3 + i);
            }

        }

        try {

            for (TreeBom key : map1.keySet()) {
                parent = key;
                break;
            }
            // SysBom finished = this.getOne(sysBom.getBomSourceId(), 2);

            Map<String, Object> data = new HashMap<>();
            data.put("parentIssueId", sysBom.getBomIssueId());
            data.put("factory", factory);
            data.put("fileType", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");
            data.put("revisionContent", jsonObjects.get(jsonObjects.size() - 1).getString("detail"));
            data.put("revisionReason", sysBom.getBomRemark());
            data.put("materialCode", parent.getSapNumber());
            data.put("bomId", sysBom.getId());
            data.put("fileLink",
                    sysConfigService.getByCode(SysConfigParam.builder().code("file_preview").build()).getValue()
                            + sysBom.getFileId());
            data.put("fileId", sysBom.getFileId());
            data.put("signPageNums", page);
            /*
             * data.put("finishedMaterialCode", "998");
             * data.put("finishedBomId", 998);
             */
            data.put("compilerId", LoginContextHolder.me().getSysLoginUserAccount());

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    //@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public ReturnOaResult receiveStatusFromOA(ReceiveOaParam param) throws IOException {
        List<String> RESULT_LIST = Arrays.asList("10", "20", "30", "40");

        log.info("BOM oa状态同步参数" + JSON.toJSONString(param));

        if (ObjectUtil.isEmpty(param)) {
            return new ReturnOaResult("传入参数不能为空", "E");
        }

        if (!RESULT_LIST.contains(param.getResult())) {
            String result = StringUtils.isEmpty(param.getResult()) ? "空" : param.getResult();
            return new ReturnOaResult("传入参数result值为:" + result + ",有误", "E");
        }

        if (StringUtils.isEmpty(param.getFdId())) {
            return new ReturnOaResult("传入参数fdId为空", "E");
        }

        // 10=驳回、撤回, 20=废弃, 30=正常审批通过, 40=OA系统重复调用
        if ("40".equals(param.getResult())) {
            return new ReturnOaResult();
        }

        LambdaQueryWrapper<SysBom> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(SysBom::getOaId, param.getFdId());

        List<SysBom> list = this.list(updateWrapper);

        List<SysBomEnd> endList = new ArrayList<>();
        // 成品bom
        if (list.size() == 0) {
            LambdaQueryWrapper<SysBomEnd> endWrapper = new LambdaQueryWrapper<>();
            endWrapper.eq(SysBomEnd::getOaId, param.getFdId());

            endList = this.sysBomEndService.list(endWrapper);
        }

        SysBom sysBom = list.size() > 0 ? list.get(0) : null;

        Boolean updateFile = list.size() > 0
                ? new Integer(1).equals(sysBom.getBomIfAdd()) || new Integer(2).equals(sysBom.getBomIfAdd())
                : false;

        if ("30".equals(param.getResult())) {

            try {
                if (list.size() > 0) {
                    bomPushService.update(sysBom, 2, null, null, "SAP");

                    bomPushService.add(sysBom, false, true, false, "审核成功", "OA",
                            "提交SAP", null, null, null);

                    ReceiveNameParam nameFromJIRA = getNameFromJIRA(sysBom);
                    //bom起始日期更新
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    sysBom.setJiraIdStatus(1);
                    sysBom.setBomStartdate(formatter.format(new Date()));
                    this.updateById(sysBom);
                    bom2Sap(sysBom, nameFromJIRA);

                } else {

                    bomPushService.updateEnd(endList.get(0), 2, null, null, "SAP");

                    bomPushService.addEnd(endList.get(0), this.get(endList.get(0).getBomId()), false, true, false,
                            "审核成功", "OA",
                            "提交SAP", null, null, null);

                    if (1 == endList.get(0).getBomIfAdd()) {
                        sysBomEndService.addWerk2Bom2Sap(endList.get(0));
                    } else if (2 == endList.get(0).getBomIfAdd()) {
                        sysBomEndService.deleteWerk2BOM2Sap(endList.get(0));
                    } else {
                        SysBomHistory sysBomHistory = bomHistoryService.getLastOne(endList.get(0).getId());
                        if (null == sysBomHistory) {
                            sysBomEndService.addBom2Sap(endList.get(0));
                        }else{
                            List<SysBomLine> sysBomLines = bomLineService.list(
                                Wrappers.<SysBomLine>lambdaQuery()
                                .in(
                                    SysBomLine::getLineId,
                                    JSONObject.parseArray(endList.get(0).getBomLines(), Long.class)
                                )
                            );

                            List<SysWerkLine> werkLines = werkLineService
                                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

                            sysBomEndService.editBom2Sap(endList.get(0),sysBomHistory,
                            werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                            .collect(Collectors.toList()),werkLines);

                        }
                    }

                    bomPushService.updateEnd(endList.get(0), 4, null, null, "完成");
                    bomPushService.addEnd(endList.get(0), this.get(endList.get(0).getBomId()), false, false, true,
                            "提交成功",
                            "SAP",
                            "完成", null, null, null);

                }

                return new ReturnOaResult();
            } catch (ServiceException e) {
                // 成品bom失败处理
                if (list.size() < 1) {
                    bomPushService.updateEnd(endList.get(0), 6, null, null, "SAP");

                    SysBom sysBom1 = this.get(endList.get(0).getBomId());

                    bomPushService.addEnd(endList.get(0), sysBom1, false, false, false, "提交失败", "OA",
                            "提交SAP", null, null, null);
                }

                log.error(e.getErrorMessage(), e);
                return ReturnOaResult.builder().code("F").msg(e.getErrorMessage()).build();
                // TODO: handle exception

            } finally {
                /*
                 * if (updateFile && list.size() > 0) {
                 * Map map = this.pdfUpdateLine(sysBom.getId());
                 * LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();
                 * queryWrapper.eq(SysBomPush::getBomId, sysBom.getId());
                 *//**
                    * 推送状态 0 提交审批 2 OA已审核 6:推送SAP失败 3 已提交SAP 4 JIRA已受控
                    *//*
                       * queryWrapper.eq(SysBomPush::getPushStatus, 4);
                       * queryWrapper.isNotNull(SysBomPush::getSubmitterId);
                       * queryWrapper.orderByDesc(SysBomPush::getCreateTime);
                       *
                       * List<SysBomPush> pushList = bomPushService.list(queryWrapper);
                       *
                       * if (ObjectUtil.isEmpty(pushList)) {
                       * log.error(sysBom.getId() + "查找不到审核状态");
                       * } else {
                       * SysBomPush sysBomPush = pushList.get(0);
                       * sysBomPush.setFileId((Long) map.get("fileId"));
                       * bomPushService.updateById(sysBomPush);
                       * }
                       *
                       * }
                       */

            }

        }

        // 废弃
        if ("20".equals(param.getResult())) {
            if (list.size() > 0) {
                bomPushService.update(sysBom, 1, null, null, sysBom.getBomNo());

                bomPushService.add(sysBom, false, true, true, "驳回", "OA",
                        sysBom.getBomNo(), null, null, null);

                sysBom.setBomStatus(4L);
                sysBom.setJiraIdStatus(0);
                if (1 == sysBom.getBomIfAdd()) {

                    List<SysWerkLine> addWerkLines = JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class);
                    List<Long> addLineIds = addWerkLines.stream().map(SysWerkLine::getId).collect(Collectors.toList());
                    bomLineService.deleteByLineIds(SysBomLineParam.builder().bomId(sysBom.getId()).build(), addLineIds);
                    sysBom.setBomIfAdd(0);
                    sysBom.setBomAddWerks("");
                }

                this.updateById(sysBom);
            } else {
                SysBomEnd bomEnd = endList.get(0);
                bomEnd.setBomRelateStatus(7);

                SysBom sysBom1 = this.get(bomEnd.getBomId());
                bomPushService.updateEnd(bomEnd, 1, null, null, "");

                bomPushService.addEnd(bomEnd, sysBom1, false, true, true, "驳回", "OA",
                        "结束", null, null, null);

                if (1 == bomEnd.getBomIfAdd()) {
                    // sysBom.setBomIfAdd(0);
                    List<SysWerkLine> addWerkLines = JSONObject.parseArray(bomEnd.getBomAddWerks(), SysWerkLine.class);
                    List<Long> addLineIds = addWerkLines.stream().map(SysWerkLine::getId).collect(Collectors.toList());
                    List<Long> originLineIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);
                    originLineIds.removeAll(addLineIds);
                    bomEnd.setBomIfAdd(0);
                    bomEnd.setBomAddWerks("");
                    bomEnd.setBomLines(JSONObject.toJSONString(originLineIds));
                }

                if (2 == bomEnd.getBomIfAdd()) {
                    List<SysWerkLine> delWerkLines = JSONObject.parseArray(bomEnd.getBomAddWerks(), SysWerkLine.class);
                    List<Long> delLineIds = delWerkLines.stream().map(SysWerkLine::getId).collect(Collectors.toList());
                    List<Long> originLineIds = JSONObject.parseArray(bomEnd.getBomLines(), Long.class);
                    originLineIds.addAll(delLineIds);
                    bomEnd.setBomIfAdd(0);
                    bomEnd.setBomAddWerks("");
                    bomEnd.setBomLines(JSONObject.toJSONString(originLineIds));
                }

                this.sysBomEndService.updateById(bomEnd);
            }

        }

        return new ReturnOaResult();
    }

    public String getProductState(int num) {
        if (2 == num) {
            return "■A样  □B样 □C样   □D样";
        }
        if (3 == num) {
            return "□A样  ■B样 □C样   □D样";
        }
        if (4 == num) {
            return "□A样  □B样 ■C样   □D样";
        }
        if (5 == num) {
            return "□A样  □B样 □C样   ■D样";
        }
        return null;
    }

    public void setProductState(int num, Context context) {
        if (2 == num) {
            context.putVar("stageA", "√");
        }
        if (3 == num) {
            context.putVar("stageB", "√");
        }
        if (4 == num) {
            context.putVar("stageC", "√");
        }
        if (5 == num) {
            context.putVar("stageD", "√");
        }
    }

    public String getTransport(String num) {

        if (null == num) {
            return null;
        }

        String result = "";

        List<String> numList = JSON.parseArray(num, String.class);
        Boolean flag1 = false;
        Boolean flag2 = false;
        Boolean flag3 = false;
        Boolean flag4 = false;
        for (int i = 0; i < numList.size(); i++) {
            if ("1".equals(numList.get(i))) {
                flag1 = true;
            }
            if ("2".equals(numList.get(i))) {
                flag2 = true;
            }
            if ("3".equals(numList.get(i))) {
                flag3 = true;
            }
            if ("4".equals(numList.get(i))) {
                flag4 = true;
            }
        }
        if (flag1) {
            result += "■陆运 ";
        } else {
            result += "□陆运 ";
        }
        if (flag2) {
            result += "■海运 ";
        } else {
            result += "□海运 ";
        }

        if (flag3) {
            result += "■空运 ";
        } else {
            result += "□空运 ";
        }

        if (flag4) {
            result += "■其他";
        } else {
            result += "□其他";
        }

        return result;
    }

    /**
     * 获取当前时间的中文时间
     *
     * @return 中文时间
     */
    public String getChineseDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String time = format.format(date);
        String year = time.substring(0, 4); // 得到年
        String month = time.substring(4, 6); // 得到月
        /// String day = time.substring(6); // 得到天

        StringBuilder cnDate = new StringBuilder();
        for (int i = 0; i < year.length(); i++) {
            cnDate.append(toChineseNum(Integer.valueOf(String.valueOf(year.charAt(i)))));
        }
        cnDate.append("年");

        cnDate.append(transformMonAndDay(month));
        cnDate.append("月");

        /*
         * cnDate.append(transformMonAndDay(day));
         * cnDate.append("日");
         */

        return cnDate.toString();
    }

    /**
     * 月 和 日
     *
     * @param time
     * @return
     */
    private String transformMonAndDay(String time) {
        StringBuilder sb = new StringBuilder();
        if (time.length() == 2) {
            String value = String.valueOf(time.charAt(0));
            if ("0".equals(value)) {
                sb.append(toChineseNum(Integer.valueOf(String.valueOf(time.charAt(1)))));
            } else if ("1".equals(value)) {
                sb.append("十");
                value = String.valueOf(time.charAt(1));
                if (!"0".equals(value)) {
                    sb.append(toChineseNum(Integer.valueOf(String.valueOf(time.charAt(1)))));
                }
            } else {
                sb.append(toChineseNum(Integer.valueOf(String.valueOf(time.charAt(0)))));
                sb.append("十");
                value = String.valueOf(time.charAt(1));
                if (!"0".equals(value)) {
                    sb.append(toChineseNum(Integer.valueOf(String.valueOf(time.charAt(1)))));
                }
            }
        } else {
            sb.append(toChineseNum(Integer.valueOf(String.valueOf(time.charAt(0)))));
        }
        return sb.toString();
    }

    /**
     * 把阿拉伯数字转换为汉字简写表示
     */
    public static String toChineseNum(int n) {
        String chineseStr = "";
        String[] chineseNum = new String[] { "〇", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
        if (n > 9 || n < 0) {
            throw new IllegalArgumentException("参数值超出允许范围 (0 ～9)！");
        }
        chineseStr = chineseNum[n];
        return chineseStr;
    }

    public void jiraPush(SysBom bom) {
        Map<String, Object> data = new HashMap<>();
        data.put("parentIssueId", bom.getBomIssueId());
        data.put("factory", bom.getBomIssueId());
        data.put("line", bom.getBomIssueId());
        data.put("fileType", bom.getBomIssueId());
        data.put("productCode", bom.getBomIssueId());
        data.put("revisionContent", bom.getBomIssueId());
        data.put("revisionReason", bom.getBomIssueId());
        data.put("materialCode", bom.getBomIssueId());
        data.put("bomId", bom.getId());
        data.put("fileLink", bom.getId());
    }

    public int setHeight(XSSFRow row, int cellIndex) {
        XSSFCell cell = row.getCell(cellIndex);
        // 1.先取出内容 计算长度 （这个方法在后面...）
        String content = getCellContent(cell);
        //log.info("内容" + content);
        // 计算字体的高度
        XSSFCellStyle cellStyle = cell.getCellStyle();
        XSSFFont font = cellStyle.getFont();
        // 字体的高度
        short fontHeight = font.getFontHeight();
        //log.info("字体的高度" + fontHeight);// 11号字体的高度 这里不重要（本方法也用不着） 这里设置每一行都是18磅
        // 计算字符的宽度
        // 代入默认字符宽度8：//字符像素宽度 = 字体宽度 * 字符个数 + 边距
        // 像素 = 5 + (字符个数 * 7)
        int i = 5 + (content.toCharArray().length * 7);
        // POI中的字符宽度算法是：double 宽度 = (字符个数 * (字符宽度 - 1) + 5) / (字符宽度 - 1) * 256;
        double poiWidth = content.length() * 12 / 7 * 256;
        // 然后再四舍五入成整数。
        int width = (int) Math.ceil(poiWidth);
        //log.info("字符串长度的宽度" + width);
        // 2.除以宽度 计算行数乘以行高 (256*80) 是我设置的我的单元格列的宽度
        double rowNum = (double) width / (256 * 50);
        //log.info("计算出来的行数" + rowNum);
        int rowNums = (int) Math.ceil(rowNum) + (content.split("\\n", -1).length -1);
        //log.info("行数" + rowNums);
        Integer height = rowNums * (fontHeight + 5) ;// 这个420 是我为每一行设置的高度 有点大，可以用字体的高度加一点（前面方法中有计算字体高度的方法）

        height += (100 + 40 * (rowNums - 1));

        if (height < 320) {
            height = 350;
        }

        if (rowNum == 0) {
            return 0;
        }

        return height;// 直接设置行高为这个值就OK了
    }

    private static String getCellContent(XSSFCell cell) {
        if (null == cell) {
            return "";
        }
        String result = cell.getStringCellValue();
        return result;
    }

    @Override
    public void updateCodeVersionFileName(SysBom bom, List<Long> addLines) {

        // 未使用 已驳回
        if (new Integer(0).equals(bom.getJiraIdStatus()) || new Long(4).equals(bom.getBomStatus())) {
            return;
        }

        ProjectDetail detail = null;

        int state = bom.getProductState();
        try {

            detail = productJiraService.getProjectDetail(
                    DocParams.builder().issueId(bom.getBomIssueId()).build());

            //state = detail.getState();

        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }

        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getBomIssueId, bom.getBomIssueId());
        queryWrapper.eq(SysBom::getBomType, bom.getBomType());
        queryWrapper.isNotNull(SysBom::getBomNo);
        List<SysBom> list = this.list(queryWrapper);

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(bom.getBomIssueId());
        List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        bom.setLines(new ArrayList<>());
        if (lineMap.containsKey(bom.getId())) {
            bom.getLines().addAll(
                    lineMap.get(bom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = new ArrayList<>();

        if (new Integer(1).equals(bom.getBomIfAdd())) {
            lines = bom.getLines();

            if (null != addLines) {
                lines.addAll(addLines);
            }


        }
        if (new Integer(2).equals(bom.getBomIfAdd())) {
            lines = bom.getLines();
            if (null != addLines) {
                lines.removeAll(addLines);
            }

        } else {
            lines = bom.getLines();
        }

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        String version = "";
        String code = "";

        // PBRI-M11-C-B08A-电芯BOM-91024999(适用于4050(40J)--5)
        String name = "";

        List<TreeBom> treeBomList = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
        String sapNumber = treeBomList.get(0).getSapNumber();

        if (list.size() == 0 && bom.getNum() == null) {
            version = "A";
            code = "PBRI-" + detail.getProductProjectName() + "-" + getProductState3(state) + "-"
                    + (bom.getBomType() == 0 ? "B01A" : "P01A");
            name = code + "-" + (bom.getBomType() == 0 ? "电芯BOM" : "包装BOM") + "-" + sapNumber
                    + (StrUtil.isBlank(factory) ? "" : ("(适用于" + factory + ")"));
        } else {
            Integer n = 0;
            List<Integer> integers = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                String bomNo = list.get(i).getBomNo();
                if (!StrUtil.isBlank(bomNo)) {
                    String[] split = bomNo.split("-");
                    String num = StrUtil.subWithLength(split[split.length - 1], 1, 2);
                    integers.add(Integer.parseInt(num));
                }
            }

            n = integers.stream().sorted(Comparator.comparing(Integer::intValue).reversed()).findFirst().get() + 1;
            if(bom.getNum() != null){
                n = Integer.valueOf(bom.getNum());
            }

            version = null == bom.getBomNo() ? "A"
                    : getNextUpEn(bom.getBomVersion().substring(bom.getBomVersion().length() - 1));

            String number = "";
            if (n < 10) {
                number = "0" + n;
            } else {
                number += n;
            }

            if (null != bom.getBomNo()) {
                String[] split = bom.getBomNo().split("-");

                number = StrUtil.subWithLength(split[split.length - 1], 1, 2);

            }

            // PBRI-M11-C-B07D
            code = "PBRI-" + detail.getProductProjectName() + "-" + getProductState3(state) + "-"
                    + (bom.getBomType() == 0 ? "B" : "P") + number + version;

            if (null != bom.getBomNo()) {
                String[] split = bom.getBomNo().split("-");
                String oldCode = split[split.length - 2];
                if (!oldCode.equals(getProductState3(state))) {
                    code = "PBRI-" + detail.getProductProjectName() + "-" + getProductState3(state) + "-"
                            + (bom.getBomType() == 0 ? "B" : "P") + number + "A";
                    version = "A";
                }
            }

            // PBRI-M11-C-B08A-电芯BOM-91024999(适用于4050(40J)--5)
            name = code + "-" + (bom.getBomType() == 0 ? "电芯BOM" : "包装BOM") + "-" + sapNumber
                    + (StrUtil.isBlank(factory) ? "" : ("(适用于" + factory + ")"));

        }

        bom.setBomVersion(version);
        bom.setBomName(name);
        bom.setBomNo(code);
        bom.setJiraIdStatus(0);

        System.err.println("版本：" + version);
        System.err.println("编号：" + code);
        System.err.println("名称：" + name);

        this.updateById(bom);

    }

    @Override
    public void updateImportCodeVersionFileName(SysBom bom) {

        int state = 0;
        ProjectDetail detail = null;
        try {

            detail = productJiraService.getProjectDetail(
                    DocParams.builder().issueId(bom.getBomIssueId()).build());

            state = detail.getState();

        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }

        LambdaQueryWrapper<SysBom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysBom::getBomIssueId, bom.getBomIssueId());
        queryWrapper.eq(SysBom::getBomType, bom.getBomType());
        queryWrapper.isNotNull(SysBom::getBomNo);
        List<SysBom> list = this.list(queryWrapper);

        SysBomLineParam sysBomLineParam = new SysBomLineParam();
        sysBomLineParam.setBomIssueId(bom.getBomIssueId());
        List<SysBomLine> sysBomLines = bomLineService.getList(sysBomLineParam);
        Map<String, List<SysWerkLine>> werkLines = getWerkLines();

        Map<Long, List<SysBomLine>> lineMap = sysBomLines.stream().collect(Collectors.groupingBy(e -> e.getBomId()));
        bom.setLines(new ArrayList<>());
        if (lineMap.containsKey(bom.getId())) {
            bom.getLines().addAll(
                    lineMap.get(bom.getId()).stream().map(SysBomLine::getLineId)
                            .collect(Collectors.toList()));
        }

        List<Long> lines = new ArrayList<>();

        lines = bom.getLines();

        String factory = "";
        for (int i = 0; i < lines.size(); i++) {
            for (String key : werkLines.keySet()) {
                List<SysWerkLine> sysWerkLines = werkLines.get(key);
                for (int j = 0; j < sysWerkLines.size(); j++) {
                    if (lines.get(i).equals(sysWerkLines.get(j).getId())) {
                        if (!StrUtil.isBlank(factory)) {
                            factory += "、";
                        }
                        factory += sysWerkLines.get(j).getLineName();
                        break;
                    }
                }

            }

        }

        String version = bom.getBomVersion();
        String code = "";

        // PBRI-M11-C-B08A-电芯BOM-91024999(适用于4050(40J)--5)
        String name = "";

        List<TreeBom> treeBomList = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
        String sapNumber = treeBomList.get(0).getSapNumber();

        Integer n = 0;
        List<Integer> integers = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            String bomNo = list.get(i).getBomNo();
            if (!StrUtil.isBlank(bomNo)) {
                String[] split = bomNo.split("-");
                String num = StrUtil.subWithLength(split[split.length - 1], 1, 2);
                integers.add(Integer.parseInt(num));
            }
        }

        n = integers.size() > 0 ?integers.stream().sorted(Comparator.comparing(Integer::intValue).reversed()).findFirst().get() + 1:1;

        if(bom.getNum() != null){
            n = Integer.valueOf(bom.getNum());
        }

        version = bom.getBomVersion();

        String number = "";
        if (n < 10) {
            number = "0" + n;
        } else {
            number += n;
        }

        // PBRI-M11-C-B07D
        code = "PBRI-" + detail.getProductProjectName() + "-" + getProductState3(state) + "-"
                + (bom.getBomType() == 0 ? "B" : "P") + number + version;

        // PBRI-M11-C-B08A-电芯BOM-91024999(适用于4050(40J)--5)
        name = code + "-" + (bom.getBomType() == 0 ? "电芯BOM" : "包装BOM") + "-" + sapNumber
                + (StrUtil.isBlank(factory) ? "" : ("(适用于" + factory + ")"));

        bom.setBomVersion(version);
        bom.setBomName(name);
        bom.setBomNo(code);
        bom.setJiraIdStatus(0);

        System.err.println("版本：" + version);
        System.err.println("编号：" + code);
        System.err.println("名称：" + name);

        this.updateById(bom);

    }

    public static String getNextUpEn(String en) {
        if (en.length() > 1) {
            char lastE = 'a';
            char st = en.toCharArray()[1];
            if (Character.isUpperCase(st)) {

                if (en.equals("AH")) {
                    return "AJ";
                }
                if (en.equals("AN")) {
                    return "AP";
                }

                if (en.equals("AZ")) {
                    return "BA";
                }
                if (en == null || en.equals("")) {
                    return "AA";
                }
                lastE = 'Z';
            } else {
                if (en.equals("z")) {
                    return "a";
                }
                if (en == null || en.equals("")) {
                    return "a";
                }
                lastE = 'z';
            }
            int lastEnglish = (int) lastE;
            char[] c = en.toCharArray();
            if (c.length > 2) {
                return null;
            } else {
                int now = (int) c[1];
                if (now >= lastEnglish)
                    return null;
                char uppercase = (char) (now + 1);
                return en.toCharArray()[0] + String.valueOf(uppercase);
            }
        } else {
            char lastE = 'a';
            char st = en.toCharArray()[0];
            if (Character.isUpperCase(st)) {
                if (en.equals("H")) {
                    return "J";
                }
                if (en.equals("N")) {
                    return "P";
                }

                if (en.equals("Z")) {
                    return "AA";
                }
                if (en == null || en.equals("")) {
                    return "A";
                }
                lastE = 'Z';
            } else {
                if (en.equals("z")) {
                    return "aa";
                }
                if (en == null || en.equals("")) {
                    return "a";
                }
                lastE = 'z';
            }
            int lastEnglish = (int) lastE;
            char[] c = en.toCharArray();
            if (c.length > 1) {
                return null;
            } else {
                int now = (int) c[0];
                if (now >= lastEnglish)
                    return null;
                char uppercase = (char) (now + 1);
                return String.valueOf(uppercase);
            }
        }

    }

    public String getProductState2(int num) {
        if (2 == num) {
            return "A";
        }
        if (3 == num) {
            return "B";
        }
        if (4 == num) {
            return "C";
        }
        if (5 == num) {
            return "D";
        }
        if (6 == num) {
            return "SOP";
        }
        return null;
    }

    public String getProductState3(int num) {
        if (2 == num) {
            return "A";
        }
        if (3 == num) {
            return "B";
        }
        if (4 == num) {
            return "C";
        }
        if (5 == num || 6 == num) {
            return "S";
        }
        return null;
    }

    public String getSummitor(Long bomId) {
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(SysBomPush::getBomId, bomId);

        queryWrapper.isNotNull(SysBomPush::getSubmitterId);

        queryWrapper.orderByDesc(SysBomPush::getId);

        List<SysBomPush> list = bomPushService.list(queryWrapper);
        if (list.size() > 0) {
            return list.get(list.size()-1).getSubmitter();
        }
        return null;
    }

    public Date getEndBomDate(Long bomId) {
        LambdaQueryWrapper<SysBomPush> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(SysBomPush::getBomId, bomId);
        queryWrapper.eq(SysBomPush::getPushStatus, 4);
        queryWrapper.eq(SysBomPush::getSys, "完成");
        queryWrapper.orderByDesc(SysBomPush::getId);

        List<SysBomPush> list = bomPushService.list(queryWrapper);
        if (list.size() > 0) {
            return list.get(0).getCreateTime();
        }
        return new Date();
    }

    //C样
    @Override
    public Map<String, String> getTechVersionAndCode(SysBom packBom, TechDoc doc,int productStage) {
        if (null == packBom.getBomNo()) {
            throw new ServiceException(500, "电芯bom还没提交审核");
        }
        //样品
        String productStageName = productStage == 9 ? "C" : "S";     //C样为C，其他样为S
        packBom.setProductProjectName(packBom.getProductProjectName().trim());
        String number;
        String[] split = packBom.getBomNo().split("-");
        number = NumberUtil.isInteger(packBom.getBomNo()) ? packBom.getBomNo(): split[split.length - 1].substring(1, 3);
        //流水号 主要是AB样使用
        if(doc.getOrderNum() == null){
            doc.setOrderNum(number);
        }
        //版本号
        String version = null == doc.getTechVersion() ? "A" : getNextUpEn(doc.getTechVersion());
        StringBuilder name = new StringBuilder();           //文件命名规则
        StringBuilder code = new StringBuilder();           //文件编码规则    需求要求编码规则和文件命名规则一致
        /**
         * 0: MI 11: 零部件图纸 12:包装图纸 13:电芯图纸 14:工序图纸 2:特殊特性清单 4：产品规格书 5：产品规范 6:工艺流程图 7：包装标准和规范 8：原材料技术要求
         */
        if (new Long(0).equals(doc.getTechType())) {        //0: MI
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-"+productStageName);      //C样
            name.append("-M");      //文件类型
            name.append(doc.getOrderNum());    //流水号
            name.append(version);  //版本号
        }
        if (new Long(11).equals(doc.getTechType())) {       //11: 零部件图纸
            name.append(packBom.getProductProjectName());   //产品名称
            name.append("-"+ productStageName +"样-结构件图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(12).equals(doc.getTechType())) {       //12:包装图纸
            name.append(packBom.getProductProjectName());   //产品名称
            name.append("-"+ productStageName +"样-工序图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(13).equals(doc.getTechType())) {       //13:电芯图纸
            name.append(packBom.getProductProjectName());   //产品名称
            name.append("-"+ productStageName +"样-电芯图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(14).equals(doc.getTechType())) {       //14:工序图纸
            name.append(packBom.getProductProjectName());   //产品名称
            name.append("-"+ productStageName +"样-工序图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(2).equals(doc.getTechType())) {        //2:特殊特性清单
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-D05");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(4).equals(doc.getTechType())) {        //4：产品规格书
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-D06");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(5).equals(doc.getTechType())) {        // 5：产品规范
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-D07");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(6).equals(doc.getTechType())) {        //6:工艺流程图
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-E13");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(7).equals(doc.getTechType())) {        // 7：包装标准和规范
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-D11");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(8).equals(doc.getTechType())) {
            name.append("PBRI");   //部门代号
            name.append("-"+packBom.getProductProjectName());   //产品名称
            name.append("-原材料技术要求");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
        }

        if(name.length() == 0){
            throw new DataException("参数异常！找不到该上传文件的文件类型");
        }

        Map<String, String> result = new HashMap<>();
        result.put("code", name.toString());
        result.put("version", version);
        result.put("name", name.toString());
        return result;
    }

    @Override
    public Map<String, String> getTechVersionAndCodeAB(TechDoc doc, String productName){
        productName = productName.trim();
        //流水号 主要是AB样使用
        if(ObjectUtil.isEmpty(doc.getOrderNum())){
            doc.setOrderNum("01");
        }

        String version = null == doc.getTechVersion() ? "A" : getNextUpEn(doc.getTechVersion());
        StringBuilder name = new StringBuilder();           //文件命名规则
        StringBuilder code = new StringBuilder();           //文件编码规则    需求要求编码规则和文件命名规则一致
        /**
         *	9：BOM 0：MI 11：零部件图纸 12：包装图纸 13：电芯图纸 14：工序图纸 2：特殊特性清单 3：测试报告 4：产品规格书 5：产品规范
         */
        if (new Long(9).equals(doc.getTechType())) {        //9：BOM
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A");
            }else if(doc.getProductState()==3){
                name.append("-B");
            }else{
                throw new DataException("状态异常！");
            }
            name.append("-B");      //文件类型
            name.append(doc.getOrderNum());    //流水号
            name.append(version);  //版本号
        }
        if (new Long(0).equals(doc.getTechType())) {        //0：MI
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A");
            }else if(doc.getProductState()==3){
                name.append("-B");
            }else{
                throw new DataException("状态异常！");
            }
            name.append("-M");      //文件类型
            name.append(doc.getOrderNum());    //流水号
            name.append(version);  //版本号
        }
        if (new Long(11).equals(doc.getTechType())) {       //11：零部件图纸
            name.append(productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A样");
            }else if(doc.getProductState()==3){
                name.append("-B样");
            }
            name.append("-结构件图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(12).equals(doc.getTechType())) {       // 12：包装图纸
            name.append(productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A样");
            }else if(doc.getProductState()==3){
                name.append("-B样");
            }
            name.append("-包装图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(13).equals(doc.getTechType())) {       // 13：电芯图纸
            name.append(productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A样");
            }else if(doc.getProductState()==3){
                name.append("-B样");
            }
            name.append("-电芯图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(14).equals(doc.getTechType())) {       // 14：工序图纸
            name.append(productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A样");
            }else if(doc.getProductState()==3){
                name.append("-B样");
            }
            name.append("-工序图纸");      //C样-结构件图纸
//            name.append("-"+version+"版");  //版本号
        }
        if (new Long(2).equals(doc.getTechType())) {        //2:特殊特性清单
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            name.append("-D05");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(3).equals(doc.getTechType())) {        //3：测试报告
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            if(doc.getProductState()==2){
                name.append("-A样");
            }else if(doc.getProductState()==3){
                name.append("-B样");
            }else{
                throw new DataException("状态异常！");
            }
            name.append("-T");      //文件类型
            name.append(doc.getOrderNum());    //流水号
//            name.append(version);    //版本号
        }
        if (new Long(4).equals(doc.getTechType())) {        //4：产品规格书
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            name.append("-D06");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }
        if (new Long(5).equals(doc.getTechType())) {        //5：产品规范
            name.append("PBRI");   //部门代号
            name.append("-"+productName);   //产品名称
            name.append("-D07");      //文件类型
            name.append("-"+doc.getOrderNum());    //流水号
//            name.append(version);  //版本号
        }

        if(name.length() == 0){
            throw new DataException("参数异常！找不到该上传文件的文件类型");
        }

        Map<String, String> result = new HashMap<>();
        result.put("code", name.toString());
        result.put("version", version);
        result.put("name", name.toString());
        return result;
    }

    @Override
    public void updateBomData(Long bomId){
        SysBom bom = this.getById(bomId);

        //编辑中
        if(new Long(0L).equals(bom.getBomStatus())){
            List<TreeBom> treeBoms = JSONObject.parseArray(bom.getBomData(), TreeBom.class);
            updateTreeBomData(treeBoms);

            this.updateById(SysBom.builder().bomData(JSON.toJSONString(treeBoms)).id(bom.getId()).build());
        }

    }

    public void updateTreeBomData(List<TreeBom> lists){
        for (int i = 0; i < lists.size(); i++) {
            TreeBom treeBom = lists.get(i);

            LambdaQueryWrapper<SysPart> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysPart::getSapNumber, treeBom.getSapNumber());
            SysPart part = partService.getOne(queryWrapper);
            if(null != part){
                if(part.getPartName() != null && !part.getPartName().equals(treeBom.getPartName())){
                    treeBom.setPartName(part.getPartName());
                }
                if(part.getPartDescription() != null && !part.getPartDescription().equals(treeBom.getPartDescription())){
                    treeBom.setPartDescription(part.getPartDescription());
                }
            }

            List<TreeBom> treeBomList = treeBom.getLists();
            if(treeBomList.size() > 0){
                this.updateTreeBomData(treeBomList);
            }
        }

    }

    @Override
    public void adminConfirm(SysBom param){
        SysBom bom = this.getById(param.getId());
        bom.setAdminComfirm(1);
        this.updateById(bom);

    }




}