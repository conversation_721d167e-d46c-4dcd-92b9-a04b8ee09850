package eve.sys.limsModular.testDataPrimary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.mongoDbModular.shenghong.bean.FlowInfo;

import java.util.List;

/**
 * <p>
 * 测试数据关联mongodb主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
public interface ITLimsTestdataPrimaryService extends IService<TLimsTestdataPrimary> {

    List<TLimsTestdataPrimary> list(TLimsTestdataPrimary param);

    PageResult<TLimsTestdataPrimary> pageList(TLimsTestdataPrimary param);

    void syncTestPrimary();

    void syncTestPrimaryOneDayData();

    void syncData(List<FlowInfo> rows);

    //补充没有启动的电芯数据
    void fullDataNoMongo();

    Boolean hideData(TLimsTestdataPrimary param);

    Boolean showData(TLimsTestdataPrimary param);

    void checkCreateAccount();

    void syncSafeData();

    void syncSafeDataOnlyTesting();
}
