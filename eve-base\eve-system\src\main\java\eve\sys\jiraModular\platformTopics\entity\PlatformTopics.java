package eve.sys.jiraModular.platformTopics.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 平台课题视图
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Getter
@Setter
@TableName("platform_topics_test")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformTopics implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long issueId;

    private Date createDate;

    private Date updateDate;

    private String summary;

    private Integer reviewResult1;

    private Integer reviewResult2;

    private String reviewResult1Name;

    private String reviewResult2Name;

    private String affiliatedPlatform;

    private String affiliatedPlatform1;

    private String affiliatedPlatform2;

    private String deptName;

    private String parentDept;

    private String parentDeptName;

    private String childDept;

    private String childDeptName;

    private String cateName;

    private String parentCate;

    private String childCate;

    private String reviewOpinion1;

    private String reviewOpinion2;

    private String dateMonth;

    private String projectCode;

    private String researchContent;

    private Long secondReview;

    private String projectBackground;

    private Date planProjectInitiationReviewDate;

    private Date planReviewDate;

    private Date putDate;

    private Date stageSummaryDate;

    private Date technicalSummaryDate;

    private Date approvalReviewDate;

    private Date schemeReviewDate;

    private Date approvalSchemeReviewDate;

    private Date technicalSummaryPassDate;

    private Date reviewDate;

    private Date dueDate;

    private Date planApprovalSchemeDate;

    private Date completionDate;

    private Date stopDate;

    private String topicName;

    private String projectPurpose;

    private String topicTarget;

    private BigDecimal developmentBudget;

    private BigDecimal presidentScore;

    private String progress;

    private String risk;

    private String deal;

    private String nextPlan;

    private String changeReason;

    private String approvalReviewResult;

    private String schemeReviewResult;

    private String approvalSchemeReviewResult;

    private String approvalSchemeReviewConclusion;

    private String technicalReviewResult;

    private Integer projectLevel;

    private String projectLevelName;

    private String recommendResults;

    private String recommendResultsVicePresident;

    private String recommendResultsPresident;

    private BigDecimal directorRating;

    private String projectManager;

    private String projectManagerId;

    private String directLeader;

    private String directLeaderId;

    private String director;

    private String directorId;

    private String inspectorGeneral;

    private String issueKey;

    private String issueTypeName;

    private String issuestatusName;

    private String projectName;

    private String priorityName;

    private String projectLeader;

    private String projectLeaderId;

    private String creatorId;

    private String assigneeId;

    private String creatorName;

    private String assigneeName;

    private String resolution;

    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date createDateBegin;
    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date createDateEnd;

    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date reviewDateBegin;
    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date reviewDateEnd;


    /**
     * 是否通过 查询参数 1：通过
     */
    @TableField(exist = false)
    private Integer pass;


}
