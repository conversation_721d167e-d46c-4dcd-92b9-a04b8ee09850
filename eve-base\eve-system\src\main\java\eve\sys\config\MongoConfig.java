package eve.sys.config;


import com.mongodb.client.MongoClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;

import javax.annotation.Resource;


@Configuration
public class MongoConfig {

    @Value("${spring.data.mongodb.database}")
    private String database;

    @Resource
    private MongoClient client;


    @Bean("limsYwlnMongoTemplate")
    @Primary
    public MongoTemplate limsYwlnMongoTemplate() {
        return new MongoTemplate(client, database);
    }

    @Bean("shengHongMongoTemplate")
    public MongoTemplate shengHongMongoTemplate() {
        return new MongoTemplate(client, "ShengHong");
    }

    /*@Bean("shengHong2MongoTemplate")
    public MongoTemplate shengHong2MongoTemplate() {
        return new MongoTemplate(client, "ShengHong2");
    }
*/
    @Bean("xinWeiMongoTemplate")
    public MongoTemplate xinWeiMongoTemplate() {
        return new MongoTemplate(client, "XinWei");
    }

    @Bean("chromaMongoTemplate")
    public MongoTemplate chromaMongoTemplate() {
        return new MongoTemplate(client, "Chroma");
    }

    @Bean("xingYunModuleMongoTemplate")
    public MongoTemplate xingYunModuleMongoTemplate() {
        return new MongoTemplate(client, "nemodule");
    }

    @Bean("xingYunPackMongoTemplate")
    public MongoTemplate xingYunPackMongoTemplate() {
        return new MongoTemplate(client, "nelctmesda");
    }

    @Bean("landianMongoTemplate")
    public MongoTemplate landianMongoTemplate() {
        return new MongoTemplate(client, "landian");
    }

    @Bean("ruinengMongoTemplate")
    public MongoTemplate ruinengMongoTemplate() {
        return new MongoTemplate(client, "ruineng");
    }

 
}