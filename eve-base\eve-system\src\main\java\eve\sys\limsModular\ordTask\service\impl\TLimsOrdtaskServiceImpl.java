package eve.sys.limsModular.ordTask.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import eve.core.context.login.LoginContextHolder;
import eve.core.pojo.page.PageResult;
import eve.core.pojo.response.ResponseData;
import eve.sys.limsModular.folder.constant.MainProcessConstant;
import eve.sys.limsModular.folder.dto.MainProcessDTO;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.impl.TLimsFolderServiceImpl;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.mapper.TLimsOrdtaskMapper;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import eve.sys.limsModular.ordTaskCondition.entity.TLimsOrdtaskCondition;
import eve.sys.limsModular.ordTaskCondition.service.impl.TLimsOrdtaskConditionServiceImpl;
import eve.sys.limsModular.order.entity.TLimsOrder;
import eve.sys.limsModular.order.service.impl.TLimsOrderServiceImpl;
import eve.sys.limsModular.temGradient.entity.TLimsTemGradient;
import eve.sys.limsModular.temGradient.service.impl.TLimsTemGradientServiceImpl;
import eve.sys.limsModular.test.entity.*;
import eve.sys.limsModular.test.service.impl.*;
import eve.sys.limsModular.testMatrix.entity.TLimsTestmatrix;
import eve.sys.limsModular.testMatrix.service.impl.TLimsTestmatrixServiceImpl;
import eve.sys.modular.consts.service.SysConfigService;
import eve.sys.modular.test.equiptUsageRecord.entity.EquiptUsageRecord;
import eve.sys.modular.test.equiptUsageRecord.service.impl.EquiptUsageRecordServiceImpl;
import eve.sys.modular.test.safetyTest.entity.SafetyTest;
import eve.sys.modular.test.safetyTest.service.impl.SafetyTestServiceImpl;
import eve.sys.modular.test.testProgress.entity.TestProgress;
import eve.sys.modular.test.testProgress.service.impl.TestProgressServiceImpl;
import eve.sys.modular.test.testProgressDetail.entity.TestProgressDetail;
import eve.sys.modular.test.testProgressDetail.service.impl.TestProgressDetailServiceImpl;
import eve.sys.modular.test.testProgressSize.entity.TestProgressSize;
import eve.sys.modular.test.testProgressSize.service.impl.TestProgressSizeServiceImpl;
import eve.sys.modular.testProjectTodoTask.entity.CalendarLifeTestEntity;
import eve.sys.modular.testProjectTodoTask.entity.TestProjectTodoTask;
import eve.sys.modular.testProjectTodoTask.service.impl.TestProjectTodoTaskServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 试验项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class TLimsOrdtaskServiceImpl extends ServiceImpl<TLimsOrdtaskMapper, TLimsOrdtask> implements ITLimsOrdtaskService {

    @Autowired
    TLimsOrdtaskMapper tLimsOrdtaskMapper;

    @Autowired
    TLimsOrderServiceImpl  tLimsOrderService;

    @Autowired
    TLimsFolderServiceImpl tLimsFolderService;

    @Autowired
    TLimsAnalyteServiceImpl tLimsAnalyteService;

    @Autowired
    TLimsTestParameterServiceImpl tLimsTestParameterService;

    @Autowired
    TLimsTestMethodConsumableServiceImpl testMethodConsumableService;

    @Autowired
    TLimsOrdtaskConditionServiceImpl tLimsOrdtaskConditionService;

    @Autowired
    TLimsTemGradientServiceImpl tLimsTemGradientService;

    @Autowired
    TLimsTestmatrixServiceImpl tLimsTestmatrixService;

    @Autowired
    TLimsOrdtaskParameterServiceImpl tLimsOrdtaskParameterService;

    @Autowired
    TLimsResultServiceImpl tLimsResultService;

    @Autowired
    TLimsOrdtaskConsumableServiceImpl tLimsOrdtaskConsumableService;

    @Autowired
    TLimsRecordTrackServiceImpl tLimsRecordTrackService;

    @Autowired
    TLimsReportServiceImpl tLimsReportService;

    @Autowired
    TestProjectTodoTaskServiceImpl testProjectTodoTaskService;

    @Autowired
    TestProgressServiceImpl testProgressService;

    @Autowired
    TestProgressDetailServiceImpl testProgressDetailService;

    @Autowired
    TestProgressSizeServiceImpl testProgressSizeService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    SafetyTestServiceImpl safetyTestService;

    @Autowired
    EquiptUsageRecordServiceImpl equiptUsageRecordService;

    private static final List<String> ONLY_DISPLAY_TEST_NAME = Arrays.asList("电池鼓包胀气", "接触角", "激光粒度");

    public static final Map<String, String> sizeTypeOfGCylinderMap = new LinkedHashMap<>();
    public static final Map<String, String> sizeTypeOfCCylinderMap = new LinkedHashMap<>();
    public static final Map<String, String> sizeTypeOfSquareMap = new LinkedHashMap<>();
    public static final Map<String, String> sizeTypeOfVCylinderMap = new LinkedHashMap<>();
    public static final Map<String, String> sizeTypeOfSoftBagOrMzMap = new LinkedHashMap<>();
    public static final Map<String, String> hisSizeTypeOfGCylinderMap = new LinkedHashMap<>();

    static {
        sizeTypeOfGCylinderMap.put("上部直径/mm", "6/去除最大值和最小值，计算平均值/gTopPointDiameter");
        sizeTypeOfGCylinderMap.put("中部直径/mm", "6/去除最大值和最小值，计算平均值/gMiddlePointDiameter");
        sizeTypeOfGCylinderMap.put("下部直径/mm", "6/去除最大值和最小值，计算平均值/gBottomPointDiameter");
        sizeTypeOfGCylinderMap.put("端高/mm","3/去除最大值和最小值，计算平均值/gTotalHeight");
        sizeTypeOfGCylinderMap.put("肩高/mm","3/去除最大值和最小值，计算平均值/gShoulderHeight");
        sizeTypeOfGCylinderMap.put("盖板内圈φ/mm","4/去除最大值和最小值，计算平均值/gCoverInnerRing");
        sizeTypeOfGCylinderMap.put("盖板外圈φ/mm","4/去除最大值和最小值，计算平均值/gCoverOuterRing");
        sizeTypeOfCCylinderMap.put("上部直径/mm", "6/去除最大值和最小值，计算平均值/cTopPointDiameter");
        sizeTypeOfCCylinderMap.put("下部直径/mm", "6/去除最大值和最小值，计算平均值/cBottomPointDiameter");
        sizeTypeOfCCylinderMap.put("总高/mm", "3/去除最大值和最小值，计算平均值/cTotalHeight");
        sizeTypeOfCCylinderMap.put("肩高/mm", "3/去除最大值和最小值，计算平均值/cShoulderHeight");
        sizeTypeOfSquareMap.put("上左厚度/mm", "1/无需计算/sThickTopLeft");
        sizeTypeOfSquareMap.put("上中厚度/mm", "1/无需计算/sThickTopMiddle");
        sizeTypeOfSquareMap.put("上右厚度/mm", "1/无需计算/sThickTopRight");
        sizeTypeOfSquareMap.put("中左厚度/mm", "1/无需计算/sThickMiddleLeft");
        sizeTypeOfSquareMap.put("中心厚度/mm", "1/无需计算/sThickMiddle");
        sizeTypeOfSquareMap.put("中右厚度/mm", "1/无需计算/sThickMiddleRight");
        sizeTypeOfSquareMap.put("下左厚度/mm", "1/无需计算/sThickBottomLeft");
        sizeTypeOfSquareMap.put("下中厚度/mm", "1/无需计算/sThickBottomMiddle");
        sizeTypeOfSquareMap.put("下右厚度/mm", "1/无需计算/sThickBottomRight");
        sizeTypeOfSquareMap.put("上部长度/mm", "1/无需计算/sTopPointLength");
        sizeTypeOfSquareMap.put("中部长度/mm", "1/无需计算/sMiddlePointLength");
        sizeTypeOfSquareMap.put("下部长度/mm", "1/无需计算/sBottomPointLength");
        sizeTypeOfSquareMap.put("左部高度/mm", "1/无需计算/sLeftPointHeight");
        sizeTypeOfSquareMap.put("中部高度/mm", "1/无需计算/sMiddlePointHeight");
        sizeTypeOfSquareMap.put("右部高度/mm", "1/无需计算/sRightPointHeight");
        sizeTypeOfVCylinderMap.put("顶点直径/mm","3/取最大值&最小值/vTopPoint");
        sizeTypeOfVCylinderMap.put("1/3处直径/mm","3/取最大值&最小值/vOneThird");
        sizeTypeOfVCylinderMap.put("1/2处直径/mm","3/取最大值&最小值/vOneSecond");
        sizeTypeOfVCylinderMap.put("2/3处直径/mm","3/取最大值&最小值/vSecondThird");
        sizeTypeOfVCylinderMap.put("尾部直径/mm","3/取最大值&最小值/vBottom");
        sizeTypeOfVCylinderMap.put("总高/mm","1/无需计算/vTotalHeight");
        sizeTypeOfVCylinderMap.put("肩高/mm","3/取最大值/vShoulderHeight");
        sizeTypeOfVCylinderMap.put("底部凸起/mm","1/无需计算/vBottomHump");
        sizeTypeOfSoftBagOrMzMap.put("厚度1/mm","1/无需计算/oThicknessOne");
        sizeTypeOfSoftBagOrMzMap.put("厚度2/mm","1/无需计算/oThicknessTwo");
        sizeTypeOfSoftBagOrMzMap.put("厚度3/mm","1/无需计算/oThicknessThree");
        sizeTypeOfSoftBagOrMzMap.put("厚度4/mm","1/无需计算/oThicknessFour");
        sizeTypeOfSoftBagOrMzMap.put("厚度5/mm","1/无需计算/oThicknessFive");
        // 测试员导入的历史尺寸测试数据
        hisSizeTypeOfGCylinderMap.put("上部直径/mm", "6/去除最大值和最小值，计算平均值/gTopPointDiameter");
        hisSizeTypeOfGCylinderMap.put("中部直径/mm", "6/去除最大值和最小值，计算平均值/gMiddlePointDiameter");
        hisSizeTypeOfGCylinderMap.put("下部直径/mm", "6/去除最大值和最小值，计算平均值/gBottomPointDiameter");
        hisSizeTypeOfGCylinderMap.put("端高/mm","6/去除最大值和最小值，计算平均值/gTotalHeight");
        hisSizeTypeOfGCylinderMap.put("肩高/mm","6/去除最大值和最小值，计算平均值/gShoulderHeight");
        hisSizeTypeOfGCylinderMap.put("盖板内圈φ/mm","6/去除最大值和最小值，计算平均值/gCoverInnerRing");
        hisSizeTypeOfGCylinderMap.put("盖板外圈φ/mm","6/去除最大值和最小值，计算平均值/gCoverOuterRing");
    }

    public List<TLimsOrdtask> getLimsOrdtasksOfPreView(Map param) throws Exception{
        List<Map> resultMaps = tLimsOrdtaskMapper.getLimsOrdtaskOfPreviewList(param);
        return transformTLimsOrdtask(resultMaps);
    }

    public PageResult<TLimsOrdtask> getLimsOrdtaskListOfSafety(Map param) throws Exception{
        String searchAssignDateParam = (String)param.get("searchAssignDateParam");
        // 查询出所有任务分配的测试项目
        param.put("searchAssignDateParam", "all");
        List<TLimsOrdtask> transformResult = getAndTransLimsOrdtasks(param);

        // 过滤掉日历寿命测试项目
        List<String> calendarTestProjectList = tLimsOrdtaskMapper.getCalendarTestProjects(param);
        param.put("testName", calendarTestProjectList);
        transformResult = transformResult.stream().filter(o -> !calendarTestProjectList.contains(o.getTestname())).collect(Collectors.toList());

        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, List<TLimsOrdtask>> folderGroupMap = transformResult.stream().collect(Collectors.groupingBy(TLimsOrdtask::getFolderno));
        for (Map.Entry<String, List<TLimsOrdtask>> entry: folderGroupMap.entrySet()) {
            Map<String, Object> map = new LinkedHashMap<>();
            String folderNo = entry.getKey();
            List<TLimsOrdtask> ordTaskList = entry.getValue();
            TLimsFolder folderBean = tLimsFolderService.getById(ordTaskList.get(0).getFolderid());
            for (TLimsOrdtask item : ordTaskList) {
                //查询测试项目试验矩阵
                LambdaQueryWrapper<TLimsTestmatrix> matrixQueryWrapper = new LambdaQueryWrapper<>();
                matrixQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, item.getId());
                matrixQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
                List<TLimsTestmatrix> testMatrixBeanList = tLimsTestmatrixService.list(matrixQueryWrapper);
                //查询测试项目下的样品
                LambdaQueryWrapper<TLimsOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
                orderQueryWrapper.in(TLimsOrder::getId, testMatrixBeanList.stream().map(TLimsTestmatrix::getOrderid).collect(Collectors.toList()));
                List<TLimsOrder> orderList = tLimsOrderService.list(orderQueryWrapper);
                //查询设备使用记录
                LambdaQueryWrapper<EquiptUsageRecord> equiptUsageRecordWrapper = new LambdaQueryWrapper<>();
                equiptUsageRecordWrapper.in(EquiptUsageRecord::getOrderId, orderList.stream().map(TLimsOrder::getId).collect(Collectors.toList()));
                List<EquiptUsageRecord> allEquiptUsageRecordList = equiptUsageRecordService.list(equiptUsageRecordWrapper);
                List<Map<String, Object>> subList = new ArrayList<>();
                for (TLimsOrder order : orderList) {
                    TLimsTestmatrix currentTestMatrix = testMatrixBeanList.stream()
                            .filter(o -> Objects.equals(o.getOrderid(), order.getId())).findFirst().orElse(null);
                    if (ObjectUtils.isEmpty(currentTestMatrix) || StringUtils.equals(currentTestMatrix.getAssignstatus(), "1")) {
                        continue;
                    }
                    String equiptCodeName = "";
                    List<EquiptUsageRecord> curEquiptUsageRecordList = allEquiptUsageRecordList
                            .stream().filter(o -> o.getMatrixId().equals(currentTestMatrix.getId()))
                            .sorted(Comparator.comparing(EquiptUsageRecord::getCreateTime).reversed()).collect(Collectors.toList());
                    if (curEquiptUsageRecordList.size() > 0) {
                        Map<Date, List<EquiptUsageRecord>> usageRecordMap = curEquiptUsageRecordList.stream().collect(Collectors.groupingBy(EquiptUsageRecord::getCreateTime));
                        List<EquiptUsageRecord> usageRecordList = usageRecordMap.get(curEquiptUsageRecordList.get(0).getCreateTime());
                        equiptCodeName = usageRecordList.stream().map(EquiptUsageRecord::getEquiptCodeAndName).collect(Collectors.joining(","));
                    }
                    // 处理孙项（样品）
                    Map<String, Object> subMap = new LinkedHashMap<>();
                    subMap.put("id", item.getId() + "-" + order.getId());
                    subMap.put("ordtaskid", item.getId());
                    subMap.put("folderid", item.getFolderid());
                    subMap.put("folderno", "样品编号");
                    subMap.put("testcode", order.getOrderno());
                    subMap.put("planstarttime", currentTestMatrix.getPlanstarttime());
                    subMap.put("planendtime", currentTestMatrix.getPlanendtime());
                    subMap.put("equipts", equiptCodeName);
                    // 根据查询条件【未分配/已分配计划时间】来整合结果
                    if (StringUtils.equals(searchAssignDateParam, "alreadyAssign")) {
                        if (ObjectUtils.isNotEmpty(currentTestMatrix.getPlanstarttime()) && ObjectUtils.isNotEmpty(currentTestMatrix.getPlanendtime())) {
                            subList.add(subMap);
                        }
                    } else if (StringUtils.equals(searchAssignDateParam, "notAssign")) {
                        if (ObjectUtils.isEmpty(currentTestMatrix.getPlanstarttime()) && ObjectUtils.isEmpty(currentTestMatrix.getPlanendtime())) {
                            subList.add(subMap);
                        }
                    } else {
                        subList.add(subMap);
                    }
                }
                // 处理子项（测试项目）
                item.setChildren(subList);
                item.setIsMidParent(true);
            }
            // 处理父项（委托单）
            if (ordTaskList.stream().anyMatch(o -> o.getChildren().size() > 0)) {
                map.put("id", folderNo);
                map.put("sorter", folderNo);
                map.put("isParent", true);
                map.put("folderid", ordTaskList.get(0).getFolderid());
                map.put("createdbyname", ordTaskList.get(0).getCreatedbyname());
                ordTaskList = ordTaskList.stream().filter(o -> o.getChildren().size() > 0).collect(Collectors.toList());
                map.put("children", ordTaskList);
                map.put("folderno", "电芯载体");
                map.put("testcode", folderBean.getCylindercellflag());
                map.put("testnumber", "产品名称");
                map.put("testname", folderBean.getProducttype());
                map.put("alias", "测试类型");
                map.put("methodcode", folderBean.getTesttype());
                map.put("methodname", "技术状态");
                String technicalStatus = folderBean.getTechnicalstatus();
                if (technicalStatus.contains("样")) {
                    technicalStatus = technicalStatus.replaceAll("样", "");
                }
                map.put("testcontent", technicalStatus + folderBean.getTechnicalstatusnumber());
                map.put("planstarttime", "额定容量");
                map.put("planendtime", folderBean.getRatedcapacity() + "Ah");
                mapList.add(map);
            }
        }

        return new PageResult(
                new Page<>(Long.parseLong(String.valueOf(param.get("pageNo"))), Long.parseLong(String.valueOf(param.get("pageSize")))),
                mapList);
    }

    public PageResult<TLimsOrdtask> getLimsOrdtaskList(Map param) throws Exception{
        boolean calendarLifeTestFlag = false;
        if ("日历寿命".equals(String.valueOf(param.getOrDefault("testName","")))) {
            List<String> calendarTestProjectList = tLimsOrdtaskMapper.getCalendarTestProjects(param);
            param.put("testName", calendarTestProjectList);
            calendarLifeTestFlag = true;
        }
        List<TLimsOrdtask> transformResult = getAndTransLimsOrdtasks(param);
        if (calendarLifeTestFlag) {
            setInOutDateOfOrdtask(transformResult);
            if (StringUtils.equals("HZ_YJ_DL_AQ", String.valueOf(param.getOrDefault("laboratoryId","")))) {
                transformResult = transformResult.stream().filter(o -> StringUtils.equals(o.getSecondcode(), "STORAGE")).collect(Collectors.toList());
            }
        }
        // 第四实验室非日历寿命测试项目查询需过滤掉日历寿命测试项目
        if (StringUtils.equals("HZ_YJ_DL_AQ", String.valueOf(param.getOrDefault("laboratoryId",""))) &&
                ObjectUtils.isEmpty(param.getOrDefault("testName",""))) {
            List<String> calendarTestProjectList = tLimsOrdtaskMapper.getCalendarTestProjects(param);
            transformResult = transformResult.stream().filter(o -> !calendarTestProjectList.contains(o.getTestname())).collect(Collectors.toList());
        }
        return new PageResult(
               new Page<>(Long.parseLong(String.valueOf(param.get("pageNo"))), Long.parseLong(String.valueOf(param.get("pageSize")))),
                transformResult);
    }

    public void setInOutDateOfOrdtask(List<TLimsOrdtask> transformResult) {
        for (TLimsOrdtask ordtask : transformResult) {
            LambdaQueryWrapper<TestProgressDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.inSql(TestProgressDetail::getProgressId,"select id from TEST_PROGRESS where order_number = 1 and ORDTASKID = " + ordtask.getId());
            List<TestProgressDetail> testProgressDetails = testProgressDetailService.list(queryWrapper);
            if (testProgressDetails.size() > 0) {
                ordtask.setPlanstarttime(testProgressDetails.get(0).getInDate());
                ordtask.setPlanendtime(testProgressDetails.get(0).getOutDate());
            }
        }
    }

    public List<TLimsOrdtask> getAndTransLimsOrdtasks(Map param) {
        List<Map> resultMaps = tLimsOrdtaskMapper.getLimsOrdtaskList(param);
        return transformTLimsOrdtask(resultMaps);
    }

    public List<Map> getTestPerson(Map param) {
        return tLimsOrdtaskMapper.getTestPerson(param);
    }

    public Map<String, Object> getOrdTasksByTesterCode(String testerCode) {
        Map<String, Object> resultMap = new HashMap<>();
        LambdaQueryWrapper<TLimsOrdtask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TLimsOrdtask::getStatus, Arrays.asList("Result","Result_return"));
        queryWrapper.and(wrapper -> wrapper.eq(TLimsOrdtask::getTestercode, testerCode).or().like(TLimsOrdtask::getParticipatorcode, testerCode));
        List<TLimsOrdtask> testingProjects = this.list(queryWrapper);

        // 查询测试项目对应的样品数量
        Set<Long> ordtaskIds = testingProjects.stream().map(TLimsOrdtask::getId).collect(Collectors.toSet());
        if (ordtaskIds.size() > 0) {
            LambdaQueryWrapper<TLimsTestmatrix> testmatrixQueryWrapper = new LambdaQueryWrapper<>();
            testmatrixQueryWrapper.in(TLimsTestmatrix::getOrdtaskid, ordtaskIds);
            testmatrixQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
            List<TLimsTestmatrix> testmatrixList = tLimsTestmatrixService.list(testmatrixQueryWrapper);
            Map<Long, Long> countMap = testmatrixList.stream().collect(Collectors.groupingBy(TLimsTestmatrix::getOrdtaskid, Collectors.counting()));
            for (TLimsOrdtask tLimsOrdtask : testingProjects) {
                Long testnumber = countMap.get(tLimsOrdtask.getId());
                if (testnumber != null) {
                    tLimsOrdtask.setTestnumber(testnumber + "");
                }
            }
        }

        resultMap.put("testingProjects", testingProjects);
        if (testingProjects.size() != 0) {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("testerCode", testerCode);
            List<Map> gatherTestProjects = tLimsOrdtaskMapper.getTestNameOrderNumber(paramMap);
            resultMap.put("gatherTestProjects", gatherTestProjects);
        }
        return resultMap;
    }

    public List<TLimsOrdtask> transformTLimsOrdtask(List<Map> mapList) {
        List<TLimsOrdtask> tLimsOrdtaskList = new ArrayList<>();
        for (Map map : mapList) {
            TLimsOrdtask tLimsOrdtask = new TLimsOrdtask();
            tLimsOrdtask.setAdditionaltime(String.valueOf(map.getOrDefault("ADDITIONALTIME","")));
            tLimsOrdtask.setAlias(String.valueOf(map.getOrDefault("ALIAS","")));
            tLimsOrdtask.setBusinessflag(String.valueOf(map.getOrDefault("BUSINESSFLAG","")));
            tLimsOrdtask.setCreatedbyid(String.valueOf(map.getOrDefault("CREATEDBYID","")));
            tLimsOrdtask.setCreatedbyname(String.valueOf(map.getOrDefault("CREATEDBYNAME","")));
            tLimsOrdtask.setCreatedbyorgid(String.valueOf(map.getOrDefault("CREATEDBYORGID","")));
            tLimsOrdtask.setCreatedbyorgname(String.valueOf(map.getOrDefault("CREATEDBYORGNAME","")));
            tLimsOrdtask.setCreatedtime((Timestamp) map.getOrDefault("CREATEDTIME",null));
            tLimsOrdtask.setErrormsg(String.valueOf(map.getOrDefault("ERRORMSG","")));
            tLimsOrdtask.setFirstcategory(String.valueOf(map.getOrDefault("FIRSTCATEGORY","")));
            tLimsOrdtask.setFirstcode(String.valueOf(map.getOrDefault("FIRSTCODE","")));
            tLimsOrdtask.setFolderid(Long.valueOf(String.valueOf(map.getOrDefault("FOLDERID",-1))));
            tLimsOrdtask.setFolderno(String.valueOf(map.getOrDefault("FOLDERNO","")));
            tLimsOrdtask.setId(Long.valueOf(String.valueOf(map.getOrDefault("ID",-1))));
            tLimsOrdtask.setInitialoutsourceflag(String.valueOf(map.getOrDefault("INITIALOUTSOURCEFLAG","")));
            tLimsOrdtask.setJudgebasis(String.valueOf(map.getOrDefault("JUDGEBASIS","")));
            tLimsOrdtask.setMethodcode(String.valueOf(map.getOrDefault("METHODCODE","")));
            tLimsOrdtask.setMethodname(String.valueOf(map.getOrDefault("METHODNAME","")));
            tLimsOrdtask.setOrdtaskreportflag(String.valueOf(map.getOrDefault("ORDTASKREPORTFLAG","")));
            tLimsOrdtask.setOutsourceflag(String.valueOf(map.getOrDefault("OUTSOURCEFLAG","")));
            tLimsOrdtask.setPlanendtime((Timestamp) map.getOrDefault("PLANENDTIME",null));
            tLimsOrdtask.setPlanstarttime((Timestamp) map.getOrDefault("PLANSTARTTIME",null));
            try {
                tLimsOrdtask.setPrice(Long.valueOf(String.valueOf(map.getOrDefault("PRICE",0))));
            } catch (NumberFormatException ex) {
                double priceValue = Double.parseDouble(String.valueOf(map.getOrDefault("PRICE", 0)));
                long price = (long) priceValue;
                tLimsOrdtask.setPrice(price);
            }
            tLimsOrdtask.setReportorderimgnum(Long.valueOf(String.valueOf(map.getOrDefault("REPORTORDERIMGNUM",0))));
            tLimsOrdtask.setReportstatus(String.valueOf(map.getOrDefault("REPORTSTATUS","")));
            tLimsOrdtask.setReported(String.valueOf(map.getOrDefault("REPORTED","")));
            tLimsOrdtask.setScheduleflag(String.valueOf(map.getOrDefault("SCHEDULEFLAG","")));
            tLimsOrdtask.setSecondcategory(String.valueOf(map.getOrDefault("SECONDCATEGORY","")));
            tLimsOrdtask.setSecondcode(String.valueOf(map.getOrDefault("SECONDCODE","")));
            tLimsOrdtask.setSorter(Long.valueOf(String.valueOf(map.getOrDefault("SORTER",-1))));
            tLimsOrdtask.setStatus(String.valueOf(map.getOrDefault("STATUS","")));
            tLimsOrdtask.setTempchangeflag(String.valueOf(map.getOrDefault("TEMPCHANGEFLAG","")));
            tLimsOrdtask.setTestcode(String.valueOf(map.getOrDefault("TESTCODE","")));
            tLimsOrdtask.setTestcontent(String.valueOf(map.getOrDefault("TESTCONTENT","")));
            tLimsOrdtask.setTestduration(String.valueOf(map.getOrDefault("TESTDURATION","")));
            tLimsOrdtask.setTestmethodid(Long.valueOf(String.valueOf(map.getOrDefault("TESTMETHODID",-1))));
            tLimsOrdtask.setTestname(String.valueOf(map.getOrDefault("TESTNAME","")));
            tLimsOrdtask.setTeststatus(String.valueOf(map.getOrDefault("TESTSTATUS","")));
            tLimsOrdtask.setTeststep(String.valueOf(map.getOrDefault("TESTSTEP","")));
            tLimsOrdtask.setTester(String.valueOf(map.getOrDefault("TESTER","")));
            tLimsOrdtask.setTestercode(String.valueOf(map.getOrDefault("TESTERCODE","")));
            tLimsOrdtask.setParticipator(String.valueOf(map.getOrDefault("PARTICIPATOR","")));
            tLimsOrdtask.setParticipatorcode(String.valueOf(map.getOrDefault("PARTICIPATORCODE","")));
            tLimsOrdtask.setTimeunit(String.valueOf(map.getOrDefault("TIMEUNIT","")));
            tLimsOrdtask.setTodoperson(String.valueOf(map.getOrDefault("TODOPERSON","")));
            tLimsOrdtask.setTestparameter(String.valueOf(map.getOrDefault("TESTPARAMETER","")));
            tLimsOrdtask.setTestnumber(String.valueOf(map.getOrDefault("TESTNUMBER","")));
            Map<String, String> otherProperties = new HashMap<>();
            otherProperties.put("businesstype", String.valueOf(map.getOrDefault("BUSINESSTYPE","")));
            otherProperties.put("folderremark", String.valueOf(map.getOrDefault("FOLDERREMARK","")));
            otherProperties.put("laboratoryid", String.valueOf(map.getOrDefault("LABORATORYID","")));
            otherProperties.put("producttype", String.valueOf(map.getOrDefault("PRODUCTTYPE","")));
            otherProperties.put("projectname", String.valueOf(map.getOrDefault("PROJECTNAME","")));
            otherProperties.put("reportflag", String.valueOf(map.getOrDefault("REPORTFLAG","")));
            otherProperties.put("testid", String.valueOf(map.getOrDefault("TESTID","")));
            otherProperties.put("testproducttype", String.valueOf(map.getOrDefault("TESTPRODUCTTYPE","")));
            otherProperties.put("testtype", String.valueOf(map.getOrDefault("TESTTYPE","")));
            otherProperties.put("theme", String.valueOf(map.getOrDefault("THEME","")));
            otherProperties.put("urgentflag", String.valueOf(map.getOrDefault("URGENTFLAG","")));
            otherProperties.put("wholereportflag", String.valueOf(map.getOrDefault("WHOLEREPORTFLAG","")));
            otherProperties.put("wtoutsourceflag", String.valueOf(map.getOrDefault("WTOUTSOURCEFLAG","")));
            otherProperties.put("wtrid", String.valueOf(map.getOrDefault("WTRID","")));
            otherProperties.put("wtrname", String.valueOf(map.getOrDefault("WTRNAME","")));
            otherProperties.put("wtrorgid", String.valueOf(map.getOrDefault("WTRORGID","")));
            otherProperties.put("plansampletime", String.valueOf(map.getOrDefault("PLANSAMPLETIME","")));
            otherProperties.put("oapasstime", String.valueOf(map.getOrDefault("OAPASSTIME","")));
            otherProperties.put("appointpersonid", String.valueOf(map.getOrDefault("APPOINTPERSONID","")));
            otherProperties.put("logisticsno", String.valueOf(map.getOrDefault("LOGISTICSNO","")));
            otherProperties.put("elntmplid", String.valueOf(map.getOrDefault("ELNTMPLID","")));
            otherProperties.put("description", String.valueOf(map.getOrDefault("DESCRIPTION","")));
            otherProperties.put("testcycle", String.valueOf(map.getOrDefault("TESTCYCLE","")));
            otherProperties.put("auditor", String.valueOf(map.getOrDefault("AUDITOR","")));
            tLimsOrdtask.setOtherProperties(otherProperties);
            tLimsOrdtaskList.add(tLimsOrdtask);
        }
        return tLimsOrdtaskList;
    }

    /**
     * 结果复核退回
     */
    @Override
    public ResponseData reviewSendBack(Map param) {
        List<Map> mapList = (List<Map>)param.get("ordTaskList");
        ObjectMapper objectMapper = new ObjectMapper();
        List<TLimsOrdtask> ordTaskList = objectMapper.convertValue(mapList, new TypeReference<List<TLimsOrdtask>>() {});
        String opinion = (String) param.get("opinion");
        for (TLimsOrdtask ordTask : ordTaskList) {
            UpdateWrapper<TLimsOrdtask> ordUpdateWrapper = new UpdateWrapper<>();
            ordUpdateWrapper.set("STATUS", "Result_return"); // Result_return, 退回
            ordUpdateWrapper.eq("ID", ordTask.getId());
            this.update(ordUpdateWrapper);

            // LIMS更新测试项目状态为【退回】的审核日志
            TLimsOrdtask ordtaskBean = this.getById(ordTask.getId());
            TLimsRecordTrack recordTrackBean = tLimsRecordTrackService.getRecordBean(ordtaskBean.getFolderid(), ordtaskBean.getId(),
                    "测试项目:" + ordtaskBean.getTestcode() + " " + ordtaskBean.getTestname(),
                    MainProcessConstant.TASK_MANAGE, "退回","结果复核", "结果录入", opinion);
            LambdaQueryWrapper<TestProjectTodoTask> todoTaskWrapper = new LambdaQueryWrapper<>();
            todoTaskWrapper.eq(TestProjectTodoTask::getOrdTaskId, ordTask.getId());
            todoTaskWrapper.eq(TestProjectTodoTask::getDeleteStatus, 0);
            TestProjectTodoTask todoTask = testProjectTodoTaskService.getOne(todoTaskWrapper);
            String nextUser = todoTask.getTester();
            if (StringUtils.isNotEmpty(todoTask.getParticipator())) {
                nextUser += "," + todoTask.getParticipator();
            }
            recordTrackBean.setNextuser(nextUser);
            recordTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
            recordTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
            recordTrackBean.setCreatedtime(new Date());
            tLimsRecordTrackService.save(recordTrackBean);

            UpdateWrapper<TestProjectTodoTask> todoUpdateWrapper = new UpdateWrapper<>();
            todoUpdateWrapper.set("REVIEW_SENDBACK_OPINION", opinion);
            todoUpdateWrapper.set("REVIEW_SENDBACK_FLAG", 1);
            todoUpdateWrapper.set("TASK_STATUS", "待处理");
            todoUpdateWrapper.eq("ORD_TASK_ID", ordTask.getId());
            testProjectTodoTaskService.update(todoUpdateWrapper);
            // 结果复核退回时更新委托单信息表的待处理人
            TLimsFolder folderBean = tLimsFolderService.getById(ordtaskBean.getFolderid());
            LambdaQueryWrapper<TestProjectTodoTask> recordWrapper = new LambdaQueryWrapper<>();
            recordWrapper.eq(TestProjectTodoTask::getTaskStatus, "待处理");
            recordWrapper.eq(TestProjectTodoTask::getReviewSendbackFlag, 1);
            recordWrapper.eq(TestProjectTodoTask::getDeleteStatus, 0);
            recordWrapper.eq(TestProjectTodoTask::getFolderNo, folderBean.getFolderno());
            List<TestProjectTodoTask> recordTrackList = testProjectTodoTaskService.list(recordWrapper);
            List<String> testerList = recordTrackList.stream().filter(o -> StringUtils.isNotEmpty(o.getTester()))
                    .map(TestProjectTodoTask::getTester).distinct().collect(Collectors.toList());
            List<String> testerCodeList = recordTrackList.stream().filter(o -> StringUtils.isNotEmpty(o.getTesterCode()))
                    .map(TestProjectTodoTask::getTesterCode).distinct().collect(Collectors.toList());
            if (testerList.size() > 0 && testerCodeList.size() > 0) {
                TLimsFolder updateData = new TLimsFolder();
                updateData.setId(folderBean.getId());
                updateData.setTodoperson(String.join(",", testerList));
                updateData.setTodopersoncode(String.join(",", testerCodeList));
                tLimsFolderService.updateById(updateData);
            }
        }
        return ResponseData.success();
    }

    /**
     * 结果复核提交
     */
    @Override
    public ResponseData reviewSubmit(List<TLimsOrdtask> ordTaskList) {
        for (TLimsOrdtask ordTask : ordTaskList) {
            UpdateWrapper<TLimsOrdtask> ordUpdateWrapper = new UpdateWrapper<>();
            ordUpdateWrapper.set("ORDTASKREPORTFLAG", 0);
            ordUpdateWrapper.set("STATUS", "Done");
            ordUpdateWrapper.eq("ID", ordTask.getId());
            this.update(ordUpdateWrapper);

            // LIMS更新测试项目状态为【完成】的审核日志
            TLimsOrdtask ordtaskBean = this.getById(ordTask.getId());
            TLimsRecordTrack ordTaskTrackBean = tLimsRecordTrackService.getRecordBean(ordtaskBean.getFolderid(), ordtaskBean.getId(),
                    "测试项目:" + ordtaskBean.getTestcode() + " " + ordtaskBean.getTestname(),
                    MainProcessConstant.TASK_MANAGE, "提交","结果复核", "完成", "提交");
            ordTaskTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
            ordTaskTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
            ordTaskTrackBean.setCreatedtime(new Date());
            tLimsRecordTrackService.save(ordTaskTrackBean);

            LambdaQueryWrapper<TLimsOrdtask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TLimsOrdtask::getFolderno, ordTask.getFolderno());
            List<TLimsOrdtask> ordtaskList = this.list(queryWrapper);
            List<TLimsOrdtask> doneOrdtaskList = ordtaskList.stream().filter(p -> StringUtils.equals(p.getStatus(), "Done")).collect(Collectors.toList());
            if (ordtaskList.size() == doneOrdtaskList.size()) {
                UpdateWrapper<TLimsFolder> folderUpdateWrapper = new UpdateWrapper<>();
                folderUpdateWrapper.set("REPORTFLAG", 0);
                folderUpdateWrapper.set("STATUS", "Done");
                folderUpdateWrapper.set("TODOPERSONCODE", null);
                folderUpdateWrapper.set("TODOPERSON", null);
                folderUpdateWrapper.eq("FOLDERNO", ordTask.getFolderno());
                tLimsFolderService.update(folderUpdateWrapper);

                // LIMS更新委托单状态为【完成】的审核日志
                TLimsRecordTrack folderTrackBean = tLimsRecordTrackService.getInsertLimsRecordTrackBean(ordtaskBean.getFolderid(), "单号:" + ordtaskBean.getFolderno(),
                        MainProcessConstant.FOLDER_MANAGE, "委托单测试完成", null, null, "委托单测试完成");
                folderTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
                folderTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
                folderTrackBean.setCreatedtime(new Date());
                tLimsRecordTrackService.save(folderTrackBean);
            }
        }
        return ResponseData.success();
    }

    /**
     * 结果录入提交
     */
    @Override
    public ResponseData InputDataSubmit(Map param) {
        String jobNumber = LoginContextHolder.me().getSysLoginUserAccount();
        LambdaQueryWrapper<TestProjectTodoTask> todoQueryWrapper = new LambdaQueryWrapper<>();
        todoQueryWrapper.eq(TestProjectTodoTask::getId, param.get("todoTaskId"));
        todoQueryWrapper.and(wrapper -> wrapper.eq(TestProjectTodoTask::getTesterCode, jobNumber).or().like(TestProjectTodoTask::getParticipatorCode, jobNumber));
        List<TestProjectTodoTask> todoTaskList = testProjectTodoTaskService.list(todoQueryWrapper);
        if (!"superAdmin".equals(jobNumber) && todoTaskList.size() == 0) {
            return ResponseData.error("提交失败，必须由本人完成待办任务！");
        }
        Long ordTaskId = Long.parseLong((String) param.get("ordTaskId"));
        //暂时在前台校验，更新状态
        UpdateWrapper<TLimsOrdtask> ordUpdateWrapper = new UpdateWrapper<>();
        ordUpdateWrapper.set("STATUS", "Review");
        ordUpdateWrapper.set("REALENDTIME", LocalDateTime.now());
        ordUpdateWrapper.eq("ID", ordTaskId);
        this.update(ordUpdateWrapper);

        // LIMS更新测试项目状态为【结果复核】的审核日志
        insertLimsLogOfResultStatus(ordTaskId);

        UpdateWrapper<TestProjectTodoTask> todoUpdateWrapper = new UpdateWrapper<>();
        todoUpdateWrapper.set("TASK_STATUS", "已完成");
        todoUpdateWrapper.set("UPDATE_TIME", new Date());
        todoUpdateWrapper.set("UPDATE_ACCOUNT", LoginContextHolder.me().getSysLoginUser().getAccount());
        todoUpdateWrapper.set("UPDATE_NAME", LoginContextHolder.me().getSysLoginUser().getName());
        todoUpdateWrapper.eq("ID", param.get("todoTaskId"));
        testProjectTodoTaskService.update(todoUpdateWrapper);

        // 当前委托单的所有测试项目都已经到结果复核状态时，更新委托单待处理人
        LambdaQueryWrapper<TLimsOrdtask> ordTaskQueryWrapper = new LambdaQueryWrapper<>();
        ordTaskQueryWrapper.inSql(TLimsOrdtask::getFolderid, "select FOLDERID from T_LIMS_ORDTASK where id = " + ordTaskId);
        List<TLimsOrdtask> ordtaskList = this.list(ordTaskQueryWrapper);
        List<TLimsOrdtask> reviewOrdtaskList = ordtaskList.stream().filter(p -> StringUtils.equals(p.getStatus(), "Review")).collect(Collectors.toList());
        if (ordtaskList.size() > 0 && ordtaskList.size() == reviewOrdtaskList.size()) {
            TLimsFolder folderBean = tLimsFolderService.getById(ordtaskList.get(0).getFolderid());
            if (StringUtils.equals(folderBean.getLaboratoryid(), "HZ_YJ_DL_JM") || StringUtils.equals(folderBean.getLaboratoryid(), "HZ_YJ_DL_AQ")) {
                LambdaQueryWrapper<TLimsRecordTrack> recordWrapper = new LambdaQueryWrapper<>();
                recordWrapper.eq(TLimsRecordTrack::getTargetid, folderBean.getId());
                recordWrapper.eq(TLimsRecordTrack::getOpera, "分配检测人");
                List<TLimsRecordTrack> recordTrackList = tLimsRecordTrackService.list(recordWrapper);
                List<String> testerList = recordTrackList.stream().filter(o -> StringUtils.isNotEmpty(o.getCreatedbyname()))
                        .map(TLimsRecordTrack::getCreatedbyname).distinct().collect(Collectors.toList());
                List<String> testerCodeList = recordTrackList.stream().filter(o -> StringUtils.isNotEmpty(o.getCreatedbyid()))
                        .map(TLimsRecordTrack::getCreatedbyid).distinct().collect(Collectors.toList());
                if (testerList.size() > 0 && testerCodeList.size() > 0) {
                    TLimsFolder updateData = new TLimsFolder();
                    updateData.setId(folderBean.getId());
                    updateData.setTodoperson(String.join(",", testerList));
                    updateData.setTodopersoncode(String.join(",", testerCodeList));
                    tLimsFolderService.updateById(updateData);
                }
            }
        }

        return ResponseData.success();
    }

    /**
     *  LIMS更新测试项目状态为【结果复核】的审核日志
     */
    public void insertLimsLogOfResultStatus(Long ordTaskId) {
        TLimsOrdtask ordtaskBean = this.getById(ordTaskId);
        TLimsRecordTrack recordTrackBean = tLimsRecordTrackService.getRecordBean(ordtaskBean.getFolderid(), ordtaskBean.getId(),
                "测试项目:" + ordtaskBean.getTestcode() + " " + ordtaskBean.getTestname(),
                MainProcessConstant.TASK_MANAGE, "提交","结果录入", "结果复核", "提交");
        LambdaQueryWrapper<TestProjectTodoTask> todoTaskWrapper = new LambdaQueryWrapper<>();
        todoTaskWrapper.eq(TestProjectTodoTask::getOrdTaskId, ordTaskId);
        todoTaskWrapper.eq(TestProjectTodoTask::getDeleteStatus, 0);
        TestProjectTodoTask todoTask = testProjectTodoTaskService.getOne(todoTaskWrapper);
        recordTrackBean.setNextuser(todoTask.getCreateName());
        recordTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
        recordTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
        recordTrackBean.setCreatedtime(new Date());
        tLimsRecordTrackService.save(recordTrackBean);
    }

    @Override
    public TLimsFolder getFolderInfoByParam(TLimsOrdtask param) {
        String folderNo = param.getFolderno();
        LambdaQueryWrapper<TLimsFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsFolder::getFolderno, folderNo);
        return tLimsFolderService.getOne(queryWrapper);
    }

    @Override
    public List<TLimsOrdtask> getLimsOrdtaskListByParam(TLimsOrdtask param) {

        LambdaQueryWrapper<TLimsOrdtask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(TLimsOrdtask::getFolderno,param.getFolderno());
        queryWrapper.orderByAsc(TLimsOrdtask::getSorter);
        List<TLimsOrdtask> list = this.list(queryWrapper);

        TLimsFolder folderBean = this.getFolderInfoByParam(param);

        //增加温度，soc测试条件
        for (TLimsOrdtask ordtask: list) {
            //查询测试条件
            LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid,ordtask.getId());
            List<TLimsOrdtaskCondition> conditionList = this.tLimsOrdtaskConditionService.list(conditionLambdaQueryWrapper);
            for (int i = 0; i < conditionList.size(); i++) {
                if("温度".equals(conditionList.get(i).getConditionname())){
                    ordtask.setTem(conditionList.get(i).getConditionvalue());
                }
                if("储存时间".equals(conditionList.get(i).getConditionname())){
                    ordtask.setTotalDay(conditionList.get(i).getConditionvalue());
                }
                if("储存SOC".equals(conditionList.get(i).getConditionname())){
                    ordtask.setSoc(conditionList.get(i).getConditionvalue());
                }
                if (StringUtils.equals(folderBean.getLaboratoryid(), "HZ_YJ_DL_AQ")) {
                    if("测试温度".equals(conditionList.get(i).getConditionname())){
                        ordtask.setTem(conditionList.get(i).getConditionvalue());
                    }
                    if("测试SOC".equals(conditionList.get(i).getConditionname())){
                        ordtask.setSoc(conditionList.get(i).getConditionvalue());
                    }
                }
            }
        }

        return list;
    }

    public ResponseData assignTaskOfAq(Map param) throws Exception {
        List<Map> folderTaskOrdersMapList = (List<Map>) param.get("folderTaskOrders");
        List<Long> ordtaskIdList = folderTaskOrdersMapList.stream()
                .map(o -> (String)o.get("ordtaskid")).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<Long> folderIdList = folderTaskOrdersMapList.stream()
                .map(o -> (String)o.get("folderid")).map(Long::parseLong).distinct().collect(Collectors.toList());
        HashMap ordParam = new HashMap();
        ordParam.put("ids", ordtaskIdList);
        List<TLimsOrdtask> ordtaskList = getAndTransLimsOrdtasks(ordParam);

        ResponseData responseData = validTaskAssign(ordtaskList);
        if (!responseData.getSuccess()) {
            return responseData;
        }

        // 任务分配提交
        Map<String, String> person = (Map<String, String>) param.get("person");
        String operation = person.get("operation");
        String opinion = person.get("opinion");
        String type = person.get("type");
        String appointTester = person.get("appointTester");
        String userId = person.get("userId");
        String userName = person.get("userName");

        List<TLimsRecordTrack> limsRecordTrackBeanList = new ArrayList<>();       // 流程日志
        List<TLimsOrdtask> updateOrdTaskList = new ArrayList<>();                  // 需更新的测试项目集合
        List<TLimsTestmatrix> updateTestmatrixList = new ArrayList<>();                  // 需更新的试验矩阵集合
        for (Map folderTaskOrdersMap : folderTaskOrdersMapList) {
            String ordTaskAndOrderId = (String)folderTaskOrdersMap.get("id");
            String orderId = ordTaskAndOrderId.split("-")[1];
            String ordTaskId = (String)folderTaskOrdersMap.get("ordtaskid");
            String orderNo = (String)folderTaskOrdersMap.get("testcode");
            TLimsOrdtask ordtaskBean = this.getById(ordTaskId);
            String status = ordtaskBean.getStatus();    // 当前状态
            MainProcessDTO dto = getOrdtaskNextStatus(type, ordtaskBean, status);//下个节点状态
            ordtaskBean.setStatus(dto.getNextStatus());
            if (!StringUtils.isEmpty(appointTester)) {  // 任务分配,分配检测人
                // 设置测试项目维度的测试负责人
                setTesterAndParterOfAq (ordtaskBean, userId, userName);
                opinion = "检测人:" + userName;
            }
            if (!StringUtils.isEmpty(dto.getNextMenuId())) {
                ordtaskBean.setOtherPropertiesItem("nextmenuid", dto.getNextMenuId());//记录下个节点的菜单ID
            }
            // 查询矩阵信息
            LambdaQueryWrapper<TLimsTestmatrix> matrixQueryWrapper = new LambdaQueryWrapper<>();
            matrixQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, ordTaskId);
            matrixQueryWrapper.eq(TLimsTestmatrix::getOrderid, orderId);
            matrixQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
            List<TLimsTestmatrix> currentTestMatrixList = tLimsTestmatrixService.list(matrixQueryWrapper);
            if (currentTestMatrixList.size() == 0) {
                return ResponseData.error("找不到测试项目的试验矩阵,请联系管理员！");
            }
            TLimsTestmatrix currentTestMatrix = currentTestMatrixList.get(0);
            // 生成测试流程记录
            String targetName = "测试项目:" + ordtaskBean.getTestcode() + " " + ordtaskBean.getAlias() + " 样品编号:" + orderNo;
            TLimsRecordTrack recordTrackBean = tLimsRecordTrackService.getRecordBean(ordtaskBean.getFolderid(), currentTestMatrix.getId(),
                    targetName, MainProcessConstant.TASK_MANAGE, operation, dto.getCruTaskName(), dto.getNextTaskName(), opinion);
            recordTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
            recordTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
            recordTrackBean.setCreatedtime(new Date());
            String nextUser = userName;
            recordTrackBean.setNextuser(nextUser);
            limsRecordTrackBeanList.add(recordTrackBean);
            // 处理待更新的测试项目信息
            TLimsOrdtask updateData = new TLimsOrdtask();
            updateData.setId(ordtaskBean.getId());
            updateData.setTestercode(ordtaskBean.getTestercode());
            updateData.setTester(ordtaskBean.getTester());
            Instant instant = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant();
            if (ObjectUtils.isEmpty(ordtaskBean.getRealstarttime())) {
                updateData.setRealstarttime(Date.from(instant));
            }
            updateOrdTaskList.add(updateData);
            // 更新试验矩阵任务分配状态为已提交
            TLimsTestmatrix updateTestMatrix = new TLimsTestmatrix();
            updateTestMatrix.setId(currentTestMatrix.getId());
            updateTestMatrix.setOrdtaskid(currentTestMatrix.getOrdtaskid());    // 用于【所有电芯都任务分配完成才变为结果录入】
            updateTestMatrix.setPlanstarttime(currentTestMatrix.getPlanstarttime());  // 用于【计划开始：取当前测试项目下已分配检测人的所有电芯中最早的开始时间】
            updateTestMatrix.setPlanendtime(currentTestMatrix.getPlanendtime());  // 用于【计划结束：取当前测试项目下已分配检测人的所有电芯中最晚的结束时间】
            updateTestMatrix.setAssignstatus("1");
            updateTestmatrixList.add(updateTestMatrix);
        }

        Map<Long, List<Map>> safetyTestGroupMap = folderTaskOrdersMapList.stream().collect(Collectors.groupingBy(o -> Long.parseLong((String) o.get("ordtaskid"))));
        TLimsOrdtask testerAndRealStartTime = new TLimsOrdtask();
        testerAndRealStartTime.setTester(userName);
        testerAndRealStartTime.setTestercode(userId);
        List<TestProjectTodoTask> todoTaskList = new ArrayList<>();     // 本次分配的电芯的全部待办任务
        List<SafetyTest> totalSafetyTestList = new ArrayList<>();       // 本次分配的电芯的全部安全测试结果
        for (Map.Entry<Long, List<Map>> entry : safetyTestGroupMap.entrySet()) {
            TLimsOrdtask curUpdateOrdtask = updateOrdTaskList.stream().filter(o -> Objects.equals(entry.getKey(), o.getId())).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(curUpdateOrdtask)) {
                List<Map> testMapList = entry.getValue();
                try {
                    testerAndRealStartTime.setId(curUpdateOrdtask.getId());
                    testerAndRealStartTime.setRealstarttime(curUpdateOrdtask.getRealstarttime());
                    // 生成第四实验室技师工作台待办任务和安全测试结果
                    ResponseData generateResult = generateToDoTaskOfAq(testerAndRealStartTime, testMapList, todoTaskList, totalSafetyTestList);
                    if (!generateResult.getSuccess()) {
                        return generateResult;
                    }
                } catch (Exception ex) {
                    // 某一个测试项目生成待办出现异常时删除选中的所有测试项目生成的待办todoTaskList和安全测试结果totalSafetyTestList
                    if (todoTaskList.size() > 0) {
                        List<Long> todoIdList = todoTaskList.stream().map(TestProjectTodoTask::getId).collect(Collectors.toList());
                        UpdateWrapper<TestProjectTodoTask> todoUpdateWrapper = new UpdateWrapper<>();
                        todoUpdateWrapper.set("DELETE_STATUS", 1);
                        todoUpdateWrapper.in("ID", todoIdList);
                        testProjectTodoTaskService.update(todoUpdateWrapper);
                    }
                    if (totalSafetyTestList.size() > 0) {
                        List<Long> safetyTestIdList = totalSafetyTestList.stream().map(SafetyTest::getId).collect(Collectors.toList());
                        UpdateWrapper<SafetyTest> safetyTestUpdateWrapper = new UpdateWrapper<>();
                        safetyTestUpdateWrapper.set("DELETE_STATUS", 1);
                        safetyTestUpdateWrapper.in("ID", safetyTestIdList);
                        safetyTestService.update(safetyTestUpdateWrapper);
                    }
                    return ResponseData.error(ex.getMessage() + ", 请联系系统管理员");
                }
            }
        }

        // 插入流程日志
        limsRecordTrackBeanList.forEach(l -> {
            tLimsRecordTrackService.save(l);
        });
        // 更新测试项目信息
        updateOrdTaskList.forEach(l -> {
            // 所有电芯都任务分配完成才变为结果录入
            LambdaQueryWrapper<TLimsTestmatrix> matrixQueryWrapper = new LambdaQueryWrapper<>();
            matrixQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, l.getId());
            matrixQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
            List<TLimsTestmatrix> testMatrixList = tLimsTestmatrixService.list(matrixQueryWrapper);
            List<TLimsTestmatrix> oldAssignList = testMatrixList.stream().filter(o -> StringUtils.equals(o.getAssignstatus(), "1")).collect(Collectors.toList());
            List<TLimsTestmatrix> newAssignList = updateTestmatrixList.stream().filter(o -> Objects.equals(o.getOrdtaskid(), l.getId())).collect(Collectors.toList());
            List<TLimsTestmatrix> curAllAssignList = new ArrayList<>();
            curAllAssignList.addAll(oldAssignList);
            curAllAssignList.addAll(newAssignList);
            if (testMatrixList.size() == curAllAssignList.size()) {
                l.setStatus("Result");
            } else {
                l.setStatus("Assign");
            }
            List<Date> planStartDateList = curAllAssignList.stream()
                    .map(TLimsTestmatrix::getPlanstarttime).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
            List<Date> planEndDateList = curAllAssignList.stream()
                    .map(TLimsTestmatrix::getPlanendtime).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
            // 计划开始：取当前测试项目下已分配检测人的所有电芯中最早的开始时间
            Optional<Date> minStartDate = planStartDateList.stream().min(Date::compareTo);
            minStartDate.ifPresent(l::setPlanstarttime);
            // 计划结束：取当前测试项目下已分配检测人的所有电芯中最晚的结束时间
            Optional<Date> maxEndDate = planEndDateList.stream().max(Date::compareTo);
            maxEndDate.ifPresent(l::setPlanendtime);
            this.updateById(l);
        });
        // 更新试验矩阵中的电芯任务分配情况
        updateTestmatrixList.forEach(l -> {
            tLimsTestmatrixService.updateById(l);
        });

        // 更新委托单待处理人
        LambdaQueryWrapper<TLimsFolder> folderQueryWrapper = new LambdaQueryWrapper<>();
        folderQueryWrapper.in(TLimsFolder::getId, folderIdList);
        List<TLimsFolder> folderBeanList = tLimsFolderService.list(folderQueryWrapper);
        for (TLimsFolder item : folderBeanList) {
            LambdaQueryWrapper<TLimsOrdtask> taskQueryWrapper = new LambdaQueryWrapper<>();
            taskQueryWrapper.eq(TLimsOrdtask::getFolderid, item.getId());
            taskQueryWrapper.in(TLimsOrdtask::getId, ordtaskIdList);
            List<TLimsOrdtask> ordtaskBeanList = this.list(taskQueryWrapper);
            TLimsFolder updateData = new TLimsFolder();
            updateData.setId(item.getId());
            List<String> testerList = ordtaskBeanList.stream().filter(o -> StringUtils.isNotEmpty(o.getTester()))
                    .map(TLimsOrdtask::getTester).distinct().collect(Collectors.toList());
            List<String> testerCodeList = ordtaskBeanList.stream().filter(o -> StringUtils.isNotEmpty(o.getTestercode()))
                    .map(TLimsOrdtask::getTestercode).distinct().collect(Collectors.toList());
            Set<String> finalTesterSet = new LinkedHashSet<>();
            Set<String> finalTesterCodeSet = new LinkedHashSet<>();
            for (String curTester : testerList) {
                List<String> curTesterList = Arrays.asList(curTester.split(","));
                finalTesterSet.addAll(curTesterList);
            }
            for (String curTesterCode : testerCodeList) {
                List<String> curTesterCodeList = Arrays.asList(curTesterCode.split(","));
                finalTesterCodeSet.addAll(curTesterCodeList);
            }
            updateData.setTodoperson(String.join(",", finalTesterSet));
            updateData.setTodopersoncode(String.join(",", finalTesterCodeSet));
            updateData.setStatus(item.getStatus());
            tLimsFolderService.updateById(updateData);
        }
        return ResponseData.success();
    }

    public ResponseData genSafetyTest(TLimsFolder folder, TLimsOrdtask ordtaskBean, List<String> sortedOrderIdList, Date planStartTime, Date planEndTime,
                                      List<SafetyTest> totalSafetyTestList, List<TLimsOrdtaskCondition> conditionList) throws Exception {
        Optional<TLimsOrdtaskCondition> tempOptional = conditionList.stream().filter(o -> "测试温度".equals(o.getConditionname())).findFirst();
        Optional<TLimsOrdtaskCondition> socOptional = conditionList.stream().filter(o -> "测试SOC".equals(o.getConditionname())).findFirst();
        String temp = "";
        String soc = "";
        if (tempOptional.isPresent()) {
            temp = tempOptional.get().getConditionvalue() + tempOptional.get().getUnit();
        }
        if (socOptional.isPresent()) {
            soc = socOptional.get().getConditionvalue() + socOptional.get().getUnit();
        }

        // 查询出样品信息后进行排序
        LambdaQueryWrapper<TLimsOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.in(TLimsOrder::getId, sortedOrderIdList);
        List<TLimsOrder> orderList = tLimsOrderService.list(orderWrapper);
        List<TLimsOrder> sortedOrderList = new ArrayList<>();
        List<String> noSortedList = orderList.stream().map(TLimsOrder::getId).map(String::valueOf).collect(Collectors.toList());
        for (String id : sortedOrderIdList) {
            int index = noSortedList.indexOf(id);
            if (index > - 1) {
                sortedOrderList.add(orderList.get(index));
            }
        }

        // 获取测试前后阶段的信息
        ResponseData dataRecordResponse = getTemGradientByConditionName(ordtaskBean.getId(), "数据记录值");
        if (!dataRecordResponse.getSuccess()) {
            return dataRecordResponse;
        }
        List<TLimsTemGradient> temGradientList = (List<TLimsTemGradient>)dataRecordResponse.getData();

        // 获取样品分配的设备
        List<String> equiptCodeAndNameList = new ArrayList<>();
        for (TLimsOrder order : sortedOrderList) {
            LambdaQueryWrapper<EquiptUsageRecord> equiptUsageRecordWrapper = new LambdaQueryWrapper<>();
            equiptUsageRecordWrapper.eq(EquiptUsageRecord::getOrderId, order.getId());
            equiptUsageRecordWrapper.eq(EquiptUsageRecord::getOrdTaskId, ordtaskBean.getId());
            equiptUsageRecordWrapper.eq(EquiptUsageRecord::getFolderNo, folder.getFolderno());
            List<EquiptUsageRecord> curUsageRecordList = equiptUsageRecordService.list(equiptUsageRecordWrapper)
                    .stream().sorted(Comparator.comparing(EquiptUsageRecord::getCreateTime).reversed()).collect(Collectors.toList());
            if (curUsageRecordList.size() > 0) {
                equiptCodeAndNameList.add(curUsageRecordList.get(0).getEquiptCodeAndName());
            } else {
                return ResponseData.error("找不到样品分配的设备，生成待办任务失败！");
            }
        }

        List<String> cellTestCodeList = sortedOrderList.stream().map(TLimsOrder::getCelltestcode).collect(Collectors.toList());
        List<String> sampleNoList = sortedOrderList.stream().map(TLimsOrder::getOrderno).collect(Collectors.toList());
        List<SafetyTest> saveSafetyTest = new ArrayList<>();
        for(TLimsTemGradient temGradient : temGradientList) {
            boolean chooseLarge = StringUtils.equals(temGradient.getLargeinspection(), "1");
            boolean chooseSmall = StringUtils.equals(temGradient.getSmallinspection(), "1");
            boolean chooseRecharge = StringUtils.equals(temGradient.getRecharge(), "1");
            boolean choosePicture = StringUtils.equals(temGradient.getPicture(), "1");
            boolean chooseVideo = StringUtils.equals(temGradient.getVideo(), "1");
            boolean chooseProcess = StringUtils.equals(temGradient.getProcesscurrent(), "1")
                    || StringUtils.equals(temGradient.getProcessvoltage(), "1")
                    || StringUtils.equals(temGradient.getProcesstemp(), "1");
            String middleCheck = "normal";
            if (chooseLarge) {
                middleCheck = "large";
            } else if (chooseSmall) {
                middleCheck = "small";
            } else if (chooseRecharge) {
                middleCheck = "recharge";
            }

            SafetyTest safetyTest = SafetyTest.builder()
                    .folderNo(folder.getFolderno()).testAlias(ordtaskBean.getAlias()).ordTaskId(ordtaskBean.getId()).planStartTime(planStartTime).planEndTime(planEndTime)
                    .stage(temGradient.getOrderno() - 1).middleCheck(middleCheck).testType("before_after").sampleType(folder.getCylindercellflag())
                    .temGradientId(temGradient.getId()).equiptCodeNames(String.join(",",equiptCodeAndNameList)).attachmentFlag(chooseProcess ? "1" : "0")
                    .videoFlag(chooseVideo ? "1" : "0").pictureFlag(choosePicture ? "1" : "0").cellTestCodes(String.join(",",cellTestCodeList))
                    .sampleNos(String.join(",",sampleNoList)).temp(temp).soc(soc).testStatus("Ongoing").build();

            ResponseData responseData = generateFilledDataOfAq(temGradient, safetyTest, ordtaskBean);
            if (!responseData.getSuccess()) {
                return responseData;
            }
            safetyTest.setTestData((String) responseData.getData());

            totalSafetyTestList.add(safetyTest);
            saveSafetyTest.add(safetyTest);
        }
        return ResponseData.success(saveSafetyTest);
    }

    /**
     * 生成第四实验室安全测试结果待录入信息
     */
    public ResponseData generateFilledDataOfAq(TLimsTemGradient temGradient, SafetyTest safetyTest, TLimsOrdtask tLimsOrdtask) throws Exception {
        boolean chooseRecharge = StringUtils.equals(temGradient.getRecharge(), "1");
        boolean chooseSmall = StringUtils.equals(temGradient.getSmallinspection(), "1");
        boolean chooseLarge = StringUtils.equals(temGradient.getLargeinspection(), "1");
        boolean chooseHeight = StringUtils.equals(temGradient.getHeight(), "1");
        boolean chooseWeight = StringUtils.equals(temGradient.getWeight(), "1");
        boolean choosePicture = StringUtils.equals(temGradient.getPicture(), "1");
//        boolean chooseProcessVoltage = StringUtils.equals(temGradient.getProcessVoltage(), "1");
//        boolean chooseProcessCurrent = StringUtils.equals(temGradient.getProcessCurrent(), "1");
//        boolean chooseProcessTemp = StringUtils.equals(temGradient.getProcessTemp(), "1");

        List<String> cellTestCodeList = Arrays.asList(safetyTest.getCellTestCodes().split(","));
        if (cellTestCodeList.size() == 0) {
            return ResponseData.error("安全测试结果的编码为空！");
        }

        List<Map<String, Object>> toBeFilledDataMapList = new ArrayList<>();
        for (String cellTestCode : cellTestCodeList) {
            Map<String, Object> toBeFilledDataMap = new LinkedHashMap<>();
            toBeFilledDataMap.put("alias", tLimsOrdtask.getAlias());
            toBeFilledDataMap.put("cellTestCode", cellTestCode);
            if (chooseLarge) {
                toBeFilledDataMap.put("middleCheck", "large");
                // 工作台数据录入时是否选择过数据,有数据时：选中数据为true,无数据时：点开按钮就为true
                toBeFilledDataMap.put("isMiddleClick", false);
                // 保存中检时的测试数据
                toBeFilledDataMap.put("checkData", null);
            } else if (chooseSmall) {
                toBeFilledDataMap.put("middleCheck", "small");
                toBeFilledDataMap.put("isMiddleClick", false);
                toBeFilledDataMap.put("checkData", null);
            } else if (chooseRecharge) {
                toBeFilledDataMap.put("middleCheck", "recharge");
                toBeFilledDataMap.put("isMiddleClick", false);
                toBeFilledDataMap.put("checkData", null);
            } else {
                toBeFilledDataMap.put("middleCheck", "normal");
                toBeFilledDataMap.put("isMiddleClick", true);
            }
            // 设置电芯状态
            toBeFilledDataMap.put("batteryStatus", "ongoing");
            toBeFilledDataMap.put("beforeInnerres", null);
            toBeFilledDataMap.put("beforeVoltage", null);
            // 用于记录填写内阻的时间
            toBeFilledDataMap.put("timeOfFillInnerres", null);
            // 如果有大中检/小中检/补电则需要生成中检后电压和内阻
            if (chooseLarge || chooseSmall || chooseRecharge) {
                toBeFilledDataMap.put("afterInnerres", null);
                // 用于记录填写中检后内阻的时间
                toBeFilledDataMap.put("timeOfFillInnerres2", null);
                toBeFilledDataMap.put("afterVoltage", null);
            }
            if (chooseWeight) {
                toBeFilledDataMap.put("weight", null);
            }
            toBeFilledDataMap.put("heightType", safetyTest.getSampleType());
            if (chooseHeight) {
                // 获取尺寸信息
                ResponseData sizeInfoResponse = getTemGradientByConditionName(tLimsOrdtask.getId(), "尺寸");
                if (!sizeInfoResponse.getSuccess()) {
                    return sizeInfoResponse;
                }
                List<TLimsTemGradient> sizeInfoList = (List<TLimsTemGradient>)sizeInfoResponse.getData();
                if (sizeInfoList.size() == 0) {
                    // 生成默认的录入数据
                    generateDefaultSizeInfo(safetyTest.getSampleType(), toBeFilledDataMap);
                } else {
                    // 获取尺寸信息
                    List<TestProgressSize> testProgressSizeList = transGradientToSize(sizeInfoList);
                    // 有尺寸信息就根据工程师填写的尺寸信息生成待录入数据，否则生成默认的录入数据
                    generateSizeInfo(safetyTest.getSampleType(), testProgressSizeList, toBeFilledDataMap);
                }
            }
            if (choosePicture) {
                // 生成样品照片录入
                generatePictureInfo(safetyTest.getSampleType(), toBeFilledDataMap);
            }
            toBeFilledDataMapList.add(toBeFilledDataMap);
        }
        // 将待填数据转换成JSON字符串并保存到数据库
        ObjectMapper objectMapper = new ObjectMapper();
        return ResponseData.success(objectMapper.writeValueAsString(toBeFilledDataMapList));
    }

    public List<TestProgressSize> transGradientToSize(List<TLimsTemGradient> sizeInfoList) {
        List<TestProgressSize> testProgressSizeList = new ArrayList<>();
        for (TLimsTemGradient sizeInfo : sizeInfoList) {
            TestProgressSize testProgressSize = TestProgressSize.builder().orderNo(sizeInfo.getOrderno()).calRule(sizeInfo.getCalrule())
                    .sizeType(sizeInfo.getSizetype())
                    .measureTime(sizeInfo.getMeasuretime())
                    .build();
            testProgressSizeList.add(testProgressSize);
        }
        return testProgressSizeList;
    }

    public ResponseData getTemGradientByConditionName (Long ordTaskId, String conditionName) {
        LambdaQueryWrapper<TLimsOrdtaskCondition> conditionQueryWrapper = new LambdaQueryWrapper<>();
        conditionQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid, ordTaskId);
        conditionQueryWrapper.eq(TLimsOrdtaskCondition::getConditionname, conditionName);
        List<TLimsOrdtaskCondition> conditionList = tLimsOrdtaskConditionService.list(conditionQueryWrapper);
        if (conditionList.size() == 0) {
            return ResponseData.error("测试项目条件【" + conditionName + "】不存在，生成待办任务失败！");
        }
        LambdaQueryWrapper<TLimsTemGradient> gradientQueryWrapper = new LambdaQueryWrapper<>();
        gradientQueryWrapper.eq(TLimsTemGradient::getOrdtaskconditionid, conditionList.get(0).getId());
        gradientQueryWrapper.orderByAsc(TLimsTemGradient::getOrderno);
        List<TLimsTemGradient> temGradientList = tLimsTemGradientService.list(gradientQueryWrapper);
        if (temGradientList.size() == 0) {
            return ResponseData.error("测试项目条件【" + conditionName + "】的详细信息不存在，生成待办任务失败！");
        }
        return ResponseData.success(temGradientList);
    }

    /**
     * 根据LIMS系统的委托申请信息生成第四实验室的待办任务和安全测试结果（测试项目维度）
     */
    public ResponseData generateToDoTaskOfAq(TLimsOrdtask testerAndRealStartTime, List<Map> testMapList, List<TestProjectTodoTask> todoTaskList, List<SafetyTest> totalSafetyTestList) throws Exception {
        List<Date> planStartTimeList = new ArrayList<>();
        List<Date> planEndTimeList = new ArrayList<>();
        List<String> orderIdList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map folderTaskOrdersMap : testMapList) {
            String planStartTimeString = (String)folderTaskOrdersMap.get("planstarttime");
            String planEndTimeString = (String)folderTaskOrdersMap.get("planendtime");
            if (StringUtils.isNotEmpty(planStartTimeString) && StringUtils.isNotEmpty(planEndTimeString)) {
                planStartTimeList.add(dateFormat.parse(planStartTimeString));
                planEndTimeList.add(dateFormat.parse(planEndTimeString));
            }
            String ordTaskAndOrderId = (String)folderTaskOrdersMap.get("id");
            String orderId = ordTaskAndOrderId.split("-")[1];
            orderIdList.add(orderId);
        }
        String ordTaskId = (String)testMapList.get(0).get("ordtaskid");
        // 计划开始：取当前测试项目下所有电芯中最早的开始时间
        Date minStartDate = Collections.min(planStartTimeList);
        // 计划结束：取当前测试项目下所有电芯中最晚的结束时间
        Date maxEndDate = Collections.max(planEndTimeList);
        TLimsOrdtask ordtaskBean = this.getById(ordTaskId);

        // 生成测试阶段为测试前后的安全测试结果
        TestProjectTodoTask todoTask = new TestProjectTodoTask();

        TLimsFolder folder = tLimsFolderService.getById(ordtaskBean.getFolderid());

        LambdaQueryWrapper<TLimsOrdtaskCondition> conditionQueryWrapper = new LambdaQueryWrapper<>();
        conditionQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid, ordtaskBean.getId());
        List<TLimsOrdtaskCondition> conditionList = tLimsOrdtaskConditionService.list(conditionQueryWrapper);

        ResponseData responseData = genSafetyTest(folder, ordtaskBean, orderIdList, minStartDate, maxEndDate, totalSafetyTestList, conditionList);
        if (!responseData.getSuccess()) {
            return responseData;
        }
        List<SafetyTest> saveSafetyTest = (List<SafetyTest>)responseData.getData();
        safetyTestService.saveBatch(saveSafetyTest);

        todoTask.setFolderNo(ordtaskBean.getFolderno());
        todoTask.setWtrName(folder.getCreatedbyname());
        todoTask.setTaskStatus("待处理");
        todoTask.setTester(testerAndRealStartTime.getTester());
        todoTask.setTesterCode(testerAndRealStartTime.getTestercode());
        todoTask.setOrdTaskId(ordtaskBean.getId());
        todoTask.setTestName(ordtaskBean.getAlias());
        todoTask.setTestCode(ordtaskBean.getTestcode());
        todoTask.setWtrId(folder.getCreatedbyid());
        todoTask.setTaskType("safety_ba_test");
        todoTask.setSampleNum(testMapList.size());
        todoTask.setProductName(folder.getProducttype());
        todoTask.setTestType(folder.getTesttype());
        todoTask.setOrderNos(saveSafetyTest.get(0).getSampleNos());
        List<String> safetyTestIdList = saveSafetyTest.stream().map(SafetyTest::getId).map(String::valueOf).collect(Collectors.toList());
        todoTask.setSafetyTestIds(String.join(",", safetyTestIdList));
        todoTask.setRealStartTime(testerAndRealStartTime.getRealstarttime());
        todoTask.setTestContent(ordtaskBean.getTestcontent());
        todoTask.setRemark(ordtaskBean.getOtherPropertiesItem("folderremark"));
        todoTask.setTestParameter(ordtaskBean.getTestparameter());
        todoTask.setPlanStartTime(clearTimeByDate(minStartDate));
        todoTask.setPlanEndTime(clearTimeByDate(maxEndDate));
        testProjectTodoTaskService.save(todoTask);
        todoTaskList.add(todoTask);
        return ResponseData.success();
    }

    public void setTesterAndParterOfAq (TLimsOrdtask ordtaskBean, String userId, String userName) {
        if (StringUtils.isEmpty(ordtaskBean.getTestercode())) {
            ordtaskBean.setTestercode(userId);
            ordtaskBean.setTester(userName);
        } else {
            List<String> oriTesterList = Arrays.asList(ordtaskBean.getTester().split(","));
            List<String> curTesterList = new ArrayList<>();
            curTesterList.addAll(oriTesterList);
            List<String> oriTesterCodeList = Arrays.asList(ordtaskBean.getTestercode().split(","));
            List<String> curTesterCodeList = new ArrayList<>();
            curTesterCodeList.addAll(oriTesterCodeList);
            if (!curTesterCodeList.contains(userId)) {
                curTesterList.add(userName);
                curTesterCodeList.add(userId);
                ordtaskBean.setTestercode(String.join(",", curTesterCodeList));
                ordtaskBean.setTester(String.join(",", curTesterList));
            }
        }
    }

    public ResponseData updateOrdtaskStatus(Map param) throws Exception {
        List<Map> ordtaskMapList = (List<Map>) param.get("orderTasks");
        ObjectMapper objectMapper = new ObjectMapper();
        List<TLimsOrdtask> ordtaskList = objectMapper.convertValue(ordtaskMapList, new TypeReference<List<TLimsOrdtask>>() {});
        ResponseData responseData = validTaskAssign(ordtaskList);
        if (!responseData.getSuccess()) {
            return responseData;
        }

        // 任务分配提交
        List<Long> ordtaskIdList = ordtaskList.stream().map(TLimsOrdtask::getId).distinct().collect(Collectors.toList());
        HashMap ordParam = new HashMap();
        ordParam.put("ids", ordtaskIdList);
        ordtaskList = getAndTransLimsOrdtasks(ordParam);

        Map<String, String> person = (Map<String, String>) param.get("person");
        String operation = person.get("operation");
        String opinion = person.get("opinion");
        String type = person.get("type");
        String appointTester = person.get("appointTester");
        String userId = person.get("userId");
        String userName = person.get("userName");
        String participator = person.getOrDefault("participator", "");
        String participatorCode = person.getOrDefault("participatorCode", "");
        String planStartTimeString = person.get("planStartTime");
        String planEndTimeString = person.get("planEndTime");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date planStartTime = null;
        Date planEndTime = null;
        if (StringUtils.isNotEmpty(planStartTimeString) && StringUtils.isNotEmpty(planEndTimeString)) {
            planStartTime = dateFormat.parse(planStartTimeString);
            planEndTime = dateFormat.parse(planEndTimeString);
        }
        List<Long> folderIdList = ordtaskList.stream().map(TLimsOrdtask::getFolderid).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<TLimsFolder> folderQueryWrapper = new LambdaQueryWrapper<>();
        folderQueryWrapper.in(TLimsFolder::getId, folderIdList);
        List<TLimsFolder> folderBeanList = tLimsFolderService.list(folderQueryWrapper);
        List<Long> testMethodIdList = ordtaskList.stream().map(TLimsOrdtask::getTestmethodid).distinct().collect(Collectors.toList());

        //分析项
        LambdaQueryWrapper<TLimsAnalyte> analyteQueryWrapper = new LambdaQueryWrapper<>();
        analyteQueryWrapper.in(TLimsAnalyte::getTestmethodid, testMethodIdList);
        List<TLimsAnalyte> limsAnalyteBeanList = tLimsAnalyteService.list(analyteQueryWrapper);
        //测试参数
        LambdaQueryWrapper<TLimsTestParameter> paramQueryWrapper = new LambdaQueryWrapper<>();
        paramQueryWrapper.in(TLimsTestParameter::getTestmethodid, testMethodIdList);
        List<TLimsTestParameter> testParameterBeanList = tLimsTestParameterService.list(paramQueryWrapper);
        //耗材
        LambdaQueryWrapper<TLimsTestMethodConsumable> consumQueryWrapper = new LambdaQueryWrapper<>();
        consumQueryWrapper.in(TLimsTestMethodConsumable::getTestmethodid, testMethodIdList);
        List<TLimsTestMethodConsumable> testMethodConsumableBeanList = testMethodConsumableService.list(consumQueryWrapper);
        //流程测试条件
        LambdaQueryWrapper<TLimsOrdtaskCondition> conditQueryWrapper = new LambdaQueryWrapper<>();
        conditQueryWrapper.in(TLimsOrdtaskCondition::getOrdtaskid, ordtaskIdList);
        List<TLimsOrdtaskCondition> ordtaskConditionBeanList = tLimsOrdtaskConditionService.list(conditQueryWrapper);

        List<TLimsTemGradient> temGradientBeanList = new ArrayList<>();
        List<TLimsOrdtaskCondition> temList = ordtaskConditionBeanList.stream()
                .filter(o -> MainProcessConstant.ORDTASK_CONDITION_REQUIREMENT_DATATYPE.contains(o.getDatatype())
                        || "plateThickness".equals(o.getDatatype())
                        || "safetyTest".equals(o.getDatatype())).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(temList)) {//有客户要求相关的测试条件
            List<Long> temIdList = temList.stream().map(TLimsOrdtaskCondition::getId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<TLimsTemGradient> temQueryWrapper = new LambdaQueryWrapper<>();
            temQueryWrapper.in(TLimsTemGradient::getOrdtaskconditionid, temIdList);
            temQueryWrapper.orderByAsc(TLimsTemGradient::getOrderno);
            temGradientBeanList =  tLimsTemGradientService.list(temQueryWrapper);
        }

        LambdaQueryWrapper<TLimsTestmatrix> matrixQueryWrapper = new LambdaQueryWrapper<>();
        matrixQueryWrapper.in(TLimsTestmatrix::getOrdtaskid, ordtaskIdList);
        matrixQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
        List<TLimsTestmatrix> testMatrixBeanList = tLimsTestmatrixService.list(matrixQueryWrapper);

//        List<Long> orderIds = testMatrixBeanList.stream().map(TLimsTestmatrix::getOrderid).distinct().collect(Collectors.toList());
//        LambdaQueryWrapper<TLimsOrder> orderWrapper = new LambdaQueryWrapper<>();
//        orderWrapper.in(TLimsOrder::getId, orderIds);
//        List<TLimsOrder> allOrderBeanList = tLimsOrderService.list(orderWrapper);

        List<TLimsRecordTrack> limsRecordTrackBeanList = new ArrayList<>();
        List<TLimsResult> insertResultBeanList = new ArrayList<>();                         // 插入的分析项
        List<TLimsOrdtaskParameter> insertOrdtaskParameterBeanList = new ArrayList<>();     // 插入的测试参数
        List<TLimsOrdtaskConsumable> insertOrdtaskConsumableBeanList = new ArrayList<>();   // 插入的耗材
        for (TLimsOrdtask ordtaskBean : ordtaskList) {
            String status = ordtaskBean.getStatus();//当前状态
            MainProcessDTO dto = getOrdtaskNextStatus(type, ordtaskBean, status);//下个节点状态
            ordtaskBean.setStatus(dto.getNextStatus());

            if (!StringUtils.isEmpty(appointTester)) {//任务分配,分配检测人
                ordtaskBean.setTestercode(userId);
                ordtaskBean.setTester(userName);
                ordtaskBean.setParticipator(participator);
                ordtaskBean.setParticipatorcode(participatorCode);
                opinion = "检测人:" + userName;
                if (StringUtils.isNotEmpty(participator)) {
                    opinion += ",参与人:" + participator;
                }
            }

            if (!StringUtils.isEmpty(dto.getNextMenuId())) {
                ordtaskBean.setOtherPropertiesItem("nextmenuid", dto.getNextMenuId());//记录下个节点的菜单ID
            }

            //任务分配提交,插入RESULT表,ORDTASKCONSUMABLE表------start------
            if (MainProcessConstant.ORDTASK_STATUS_ASSIGN.equals(status) && MainProcessConstant.ORDTASK_STATUS_RESULT.equals(dto.getNextStatus())) {
                //业务类型
                //1.客户要求类型此时需要找测试条件详情
                //2.储存类型此时不需要插入分析项
                String businessType = ordtaskBean.getOtherPropertiesItem("businesstype");
                List<TLimsTemGradient> temGradientBeans = new ArrayList<>();//客户要求List
                boolean exportImportFlag = false;//动力的储存测试 和 质量中心电池的循环中检测试 ->标识(用于导出导入分析项)

                if (MainProcessConstant.ANALYTE_REQUIREMENT_BUSINESSTYPE.contains(businessType)) {//客户要求
                    List<TLimsOrdtaskCondition> conditionBeanList = ordtaskConditionBeanList.stream()
                            .filter(o -> ObjectUtils.equals(o.getOrdtaskid(), ordtaskBean.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(conditionBeanList)) {
                        List<TLimsOrdtaskCondition> tems = conditionBeanList.stream()
                                .filter(o -> MainProcessConstant.ORDTASK_CONDITION_REQUIREMENT_DATATYPE.contains(o.getDatatype())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(tems) && !CollectionUtils.isEmpty(temGradientBeanList)) {
                            List<Long> temIdList = tems.stream().map(TLimsOrdtaskCondition::getId).distinct().collect(Collectors.toList());
                            temGradientBeans = temGradientBeanList.stream().filter(o -> temIdList.contains(o.getOrdtaskconditionid())).collect(Collectors.toList());
                        }

                        //粒径分析, 测试条件为检测方法,根据其下拉项生成分析项
                        TLimsOrdtaskCondition testMethodCondition = conditionBeanList.stream()
                                .filter(o -> StringUtils.equals("检测方法", o.getConditionname())).findFirst().orElse(null);
                        if ("sizeAnalyse".equals(businessType) && !ObjectUtils.isEmpty(testMethodCondition)) {
                            ordtaskBean.setOtherPropertiesItem("testmethodvalue", testMethodCondition.getFillvalue());
                        }
                    }
                } else if ("storageTest".equals(businessType) || "cycleInspectionTest".equals(businessType)) {//动力的储存测试 或 质量中心电池的循环中检测试
                    exportImportFlag = true;
                } else if ("plateThickness".equals(businessType)) {//镀层厚度
                    List<TLimsOrdtaskCondition> conditionBeanList = ordtaskConditionBeanList.stream()
                            .filter(o -> ObjectUtils.equals(o.getOrdtaskid(), ordtaskBean.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(conditionBeanList)) {
                        List<TLimsOrdtaskCondition> tems = conditionBeanList.stream()
                                .filter(o -> "plateThickness".equals(o.getDatatype())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(tems) && !CollectionUtils.isEmpty(temGradientBeanList)) {
                            List<Long> temIdList = tems.stream().map(TLimsOrdtaskCondition::getId).distinct().collect(Collectors.toList());
                            temGradientBeans = temGradientBeanList.stream().filter(o -> temIdList.contains(o.getOrdtaskconditionid())).collect(Collectors.toList());
                        }
                    }
                } else if ("safetyTest".equals(businessType)) {//安全测试
                    List<TLimsOrdtaskCondition> conditionBeanList = ordtaskConditionBeanList.stream()
                            .filter(o -> ObjectUtils.equals(o.getOrdtaskid(), ordtaskBean.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(conditionBeanList)) {
                        List<TLimsOrdtaskCondition> tems = conditionBeanList.stream()
                                .filter(o -> "safetyTest".equals(o.getDatatype())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(tems) && !CollectionUtils.isEmpty(temGradientBeanList)) {
                            List<Long> temIdList = tems.stream().map(TLimsOrdtaskCondition::getId).distinct().collect(Collectors.toList());
                            temGradientBeans = temGradientBeanList.stream().filter(o -> temIdList.contains(o.getOrdtaskconditionid())).collect(Collectors.toList());
                        }
                    }
                } else if ("measureValue".equals(businessType)) {//测量值类型
                    List<TLimsOrdtaskCondition> conditionBeanList = ordtaskConditionBeanList.stream()
                            .filter(o -> ObjectUtils.equals(o.getOrdtaskid(), ordtaskBean.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(conditionBeanList)) {
                        TLimsOrdtaskCondition testMethodCondition = conditionBeanList.stream()
                                .filter(o -> StringUtils.equals("测量值要求", o.getConditionname())).findFirst().orElse(null);
                        if (!ObjectUtils.isEmpty(testMethodCondition) && !StringUtils.isEmpty(testMethodCondition.getFillvalue())) {
                            ordtaskBean.setOtherPropertiesItem("measurevalue", testMethodCondition.getFillvalue());
                        }
                    }
                } else if ("fieldSign".equals(businessType)) {//字段标记类型
                    TLimsOrdtaskCondition conditionBean = ordtaskConditionBeanList.stream()
                            .filter(o -> ObjectUtils.equals(o.getOrdtaskid(), ordtaskBean.getId())
                                    && !StringUtils.isEmpty(o.getFieldsign())).findFirst().orElse(null);
                    if (!ObjectUtils.isEmpty(conditionBean)) {
                        ordtaskBean.setOtherPropertiesItem("fieldsign", conditionBean.getFieldsign());
                        ordtaskBean.setOtherPropertiesItem("fieldsignvalue", conditionBean.getFillvalue());
                    }
                }

                List<TLimsTestmatrix> testMatrixBeans = testMatrixBeanList.stream()
                        .filter(o -> o.getOrdtaskid().equals(ordtaskBean.getId())).collect(Collectors.toList());
                //分析项
                List<TLimsAnalyte> analyteBeans = limsAnalyteBeanList.stream()
                        .filter(o -> o.getTestmethodid().equals(ordtaskBean.getTestmethodid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(testMatrixBeans) && !exportImportFlag) {
                    List<TLimsResult> resultBeans = tLimsResultService.getInsertLimsResultBean(ordtaskBean, testMatrixBeans, analyteBeans, businessType, temGradientBeans);
                    // ONLY_DISPLAY_TEST_NAME该集合下的项目不需要录入值但是又需要展示，所以直接默认给/
                    if (ONLY_DISPLAY_TEST_NAME.stream().anyMatch(item -> ordtaskBean.getTestname().contains(item))) {
                        resultBeans.forEach(item -> {
                            item.setOriginalresult("/");
                            item.setFinalresult("/");
                            item.setStatus("Done");
                        });
                    }
                    insertResultBeanList.addAll(resultBeans);
                }
                //测试参数
                List<TLimsTestParameter> testParameterBeans = testParameterBeanList.stream()
                        .filter(o -> o.getTestmethodid().equals(ordtaskBean.getTestmethodid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(testParameterBeans)) {
                    List<TLimsOrdtaskParameter> ordtaskParameterBeans = tLimsOrdtaskParameterService.getInsertOrdtaskParameterBean(ordtaskBean, testParameterBeans);
                    insertOrdtaskParameterBeanList.addAll(ordtaskParameterBeans);
                }

                //耗材
                List<TLimsTestMethodConsumable> testMethodConsumableBeans = testMethodConsumableBeanList.stream()
                        .filter(o -> o.getTestmethodid().equals(ordtaskBean.getTestmethodid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(testMethodConsumableBeans)) {
                    List<TLimsOrdtaskConsumable> ordtaskConsumableBeans = tLimsOrdtaskConsumableService.getInsertOrdtaskConsumableBean(ordtaskBean, testMethodConsumableBeans);
                    insertOrdtaskConsumableBeanList.addAll(ordtaskConsumableBeans);
                }

                if (StringUtils.equals("0", ordtaskBean.getScheduleflag())) {// 不排程的测试项目
                    Instant instant = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant();
                    ordtaskBean.setRealstarttime(Date.from(instant));
                }
            }
            //任务分配提交,插入RESULT表,ORDTASKCONSUMABLE表------end--------

            //审核记录
            TLimsRecordTrack recordTrackBean = tLimsRecordTrackService.getRecordBean(ordtaskBean.getFolderid(), ordtaskBean.getId(), "测试项目:" + ordtaskBean.getTestcode() + " " + ordtaskBean.getTestname(),
                    MainProcessConstant.TASK_MANAGE, operation, dto.getCruTaskName(), dto.getNextTaskName(), opinion);
//            recordTrackBean.setExt$Item("ordtaskid", String.valueOf(ordtaskBean.getId()));
            recordTrackBean.setCreatedbyid(LoginContextHolder.me().getSysLoginUserAccount());
            recordTrackBean.setCreatedbyname(LoginContextHolder.me().getSysLoginUser().getName());
            recordTrackBean.setCreatedtime(new Date());
            String nextUser = userName;
            if (StringUtils.isNotEmpty(participator)) {
                nextUser += "," + participator;
            }
            recordTrackBean.setNextuser(nextUser);
//            recordTrackBean.setCreatedbyorgid(LoginContextHolder.me().getSysLoginUserOrgId().toString());
//            recordTrackBean.setCreatedbyorgname(LoginContextHolder.me().getSysLoginUser().getLoginEmpInfo().getOrgName());
            limsRecordTrackBeanList.add(recordTrackBean);
        }

        // 生成技师工作台待办任务:1.精密/安全实验室任务,2.研发检测中心日历寿命测试任务
        try {
            TLimsOrdtask testerAndRealStartTime = new TLimsOrdtask();
            testerAndRealStartTime.setTester(userName);
            testerAndRealStartTime.setTestercode(userId);
            testerAndRealStartTime.setParticipator(participator);
            testerAndRealStartTime.setParticipatorcode(participatorCode);
            Instant instant = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant();
            testerAndRealStartTime.setRealstarttime(Date.from(instant));
            generateToDoTaskByLims(testerAndRealStartTime, ordtaskIdList, planStartTime, planEndTime);
        } catch (Exception ex) {
            // 某一个测试项目生成待办出现异常时删除选中的所有测试项目生成的待办
            UpdateWrapper<TestProjectTodoTask> todoUpdateWrapper = new UpdateWrapper<>();
            todoUpdateWrapper.set("DELETE_STATUS", 1);
            todoUpdateWrapper.in("ORD_TASK_ID", ordtaskIdList);
            testProjectTodoTaskService.update(todoUpdateWrapper);
            return ResponseData.error(ex.getMessage() + ", 请联系系统管理员");
        }
        for (TLimsOrdtask item : ordtaskList) {
            TLimsOrdtask updateData = new TLimsOrdtask();
            updateData.setId(item.getId());
            updateData.setStatus(item.getStatus());
            updateData.setTodoperson(item.getTodoperson());
            updateData.setTestercode(item.getTestercode());
            updateData.setTester(item.getTester());
            updateData.setParticipator(item.getParticipator());
            updateData.setParticipatorcode(item.getParticipatorcode());
            updateData.setRealstarttime(item.getRealstarttime());
            updateData.setRealendtime(item.getRealendtime());
            updateData.setPlanstarttime(planStartTime);
            updateData.setPlanendtime(planEndTime);
            this.updateById(updateData);
        }
        for (TLimsResult item : insertResultBeanList) {
            // 分析项, 给分析项设置样品编号
            LambdaQueryWrapper<TLimsOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
            orderQueryWrapper.inSql(TLimsOrder::getId, "SELECT ORDERID FROM T_LIMS_TESTMATRIX WHERE id = " + item.getTestmatrixid());
            TLimsOrder order = tLimsOrderService.list(orderQueryWrapper).get(0);
            item.setSampleno(order.getOrderno());
            item.setSamplename(order.getSamplename());
            item.setPartno(order.getPartno());
            item.setBatchno(order.getBatchno());
            tLimsResultService.save(item);
        }
        for (TLimsOrdtaskParameter item : insertOrdtaskParameterBeanList) { // 测试参数
            tLimsOrdtaskParameterService.save(item);
        }
        for (TLimsOrdtaskConsumable item : insertOrdtaskConsumableBeanList) { // 耗材
            tLimsOrdtaskConsumableService.save(item);
        }
        //更新委托单待处理人------start------
        LambdaQueryWrapper<TLimsOrdtask> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.in(TLimsOrdtask::getFolderid, folderIdList);
        List<TLimsOrdtask> ordtaskBeanList = this.list(taskQueryWrapper);
        LambdaQueryWrapper<TLimsReport> reportQueryWrapper = new LambdaQueryWrapper<>();
        reportQueryWrapper.in(TLimsReport::getFolderid, folderIdList);
        List<TLimsReport> reportBeanList = tLimsReportService.list(reportQueryWrapper);
        setFolderTodoPerson(folderBeanList, ordtaskBeanList, reportBeanList);
        //更新委托单待处理人------end------

        //tLimsRecordTrackService.saveBatch(limsRecordTrackBeanList); // 审核记录
        limsRecordTrackBeanList.forEach(l -> {
            tLimsRecordTrackService.save(l);
        });

        for (TLimsFolder item : folderBeanList) {
            // 更新委托单待处理人
            TLimsFolder updateData = new TLimsFolder();
            updateData.setId(item.getId());
            List<String> testerList = ordtaskBeanList.stream().filter(o -> StringUtils.isNotEmpty(o.getTester()))
                    .map(TLimsOrdtask::getTester).distinct().collect(Collectors.toList());
            List<String> testerCodeList = ordtaskBeanList.stream().filter(o -> StringUtils.isNotEmpty(o.getTestercode()))
                    .map(TLimsOrdtask::getTestercode).distinct().collect(Collectors.toList());
            updateData.setTodoperson(String.join(",", testerList));
            updateData.setTodopersoncode(String.join(",", testerCodeList));
            updateData.setStatus(item.getStatus());
            tLimsFolderService.updateById(updateData);
        }
        return ResponseData.success();
    }

    // 模拟任务分配接口，Lims的日历寿命测试存储第1阶段
//    public ResponseData updateOrdtaskStatus2(Map param) throws Exception {
//        List<Long> ordtaskIdList = new ArrayList<>();
//        ordtaskIdList.add(1199066512501184L);
//        String planStartTimeString = "2023-08-11 00:00:00";
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Date planStartTime = dateFormat.parse(planStartTimeString);
//        generateToDoTaskByLims(ordtaskIdList, planStartTime, planStartTime);
//        return ResponseData.success();
//    }

    // 模拟LIMS系统同步过来的数据, 把测试技师、进箱开始日期planEndTime同步到计划主表(TEST_PROGRESS)和计划详细表(TEST_PROGRESS_DETAIL)
//    public ResponseData updateOrdtaskStatus1(Map param) throws Exception {
//        TLimsOrdtask ordtask = new TLimsOrdtask();
//        ordtask.setId(Long.parseLong(param.get("id").toString()));
//        ordtask.setTester(param.get("testerName").toString());
//        ordtask.setTestercode(param.get("testerCode").toString());
//        return ResponseData.success(limsSyncTestProgress(ordtask, new Date()));
//    }

    /**
     * LIMS系统同步过来的数据需要把测试技师同步到计划主表(TEST_PROGRESS)
     * PBI不需要：在新增数据时就有这些数据
     */
    public TestProgressDetail limsSyncTestProgress(TLimsOrdtask ordtask, TLimsOrdtask testerAndRealStartTime) throws Exception{
        LambdaQueryWrapper<TestProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TestProgress::getStatus, 0);
        queryWrapper.eq(TestProgress::getOrdtaskid, ordtask.getId());
        TestProgress resultTestProgress = testProgressService.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(resultTestProgress)) {
            throw new Exception("LIMS系统数据还没同步到PBI！");
        }
        // 把测试技师同步到计划主表(TEST_PROGRESS)
        TestProgress updateTestProgress = new TestProgress();
        updateTestProgress.setId(resultTestProgress.getId());
        updateTestProgress.setTestMan(testerAndRealStartTime.getTester());
        updateTestProgress.setTestManId(testerAndRealStartTime.getTestercode());
        testProgressService.updateById(updateTestProgress);
        // 获取中检信息所有存储阶段并按orderNumber升序排序
        LambdaQueryWrapper<TestProgressDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(TestProgressDetail::getStatus, 0);
        detailQueryWrapper.eq(TestProgressDetail::getProgressId, resultTestProgress.getId());
        detailQueryWrapper.orderByAsc(TestProgressDetail::getOrderNumber);
        List<TestProgressDetail> testProgressDetailList = testProgressDetailService.list(detailQueryWrapper);
        // 计算中检信息所有存储阶段的计划开始时间和结束时间,并同步到计划详细表(TEST_PROGRESS_DETAIL)
        // 20231109最新需求，初始存储阶段不计算后面阶段的存储日期
//        syncAllTestProDetailDateOfLims(testProgressDetailList, inBoxStartDate);
        // 因为是第1条的待办任务，所以返回testProgressDetailList的第1条数据的id
        return testProgressDetailList.get(0);
    }

    /**
     * 阶段式日历寿命测试：根据当前存储阶段的结束时间计算中检信息剩余存储阶段的计划结束时间（计划开始时间是空的）,并同步到计划详细表(TEST_PROGRESS_DETAIL)
     * 只有点击完成初始存储阶段对应的待办任务才会同步计划详细表
     * planEndDayOfLastData 上一阶段的计划结束时间
     * totalStageSize 总阶段数
     */
    public void syncRemindTestProDetailDateOfStage(List<TestProgressDetail> remindTestProDetailList, Date planEndDayOfFirstData) {
        LocalDate planEndLocalDayOfFirstData = planEndDayOfFirstData.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        List<TestProgressDetail> updateItemList = new ArrayList<>();
        for (TestProgressDetail item : remindTestProDetailList) {
            TestProgressDetail updateItem = new TestProgressDetail();
            updateItem.setId(item.getId());
            // 获取总存储天数
            Long storeDay = item.getTotalDay();
            // 计划结束时间 = 上一阶段的计划结束时间 + 总存储天数，将LocalDate转换成Date
            updateItem.setOutDate(Date.from(planEndLocalDayOfFirstData.plusDays(storeDay).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            updateItemList.add(updateItem);
        }
        testProgressDetailService.updateBatchById(updateItemList);
    }

    /**
     * 根据当前存储阶段的结束时间重新计算中检信息剩余存储阶段的计划开始时间和结束时间,并同步到计划详细表(TEST_PROGRESS_DETAIL)
     * planEndDayOfLastData 上一阶段的计划结束时间
     * totalStageSize 总阶段数
     * needAddDay 上一阶段工程师勾选情况计算得出的工作天数，下一阶段的inDate = planEndDayOfLastData + needAddDay
     */
    public void syncRemindTestProDetailDate(List<TestProgressDetail> remindTestProDetailList, Date planEndDayOfLastData, int totalStageSize, int needAddDay) {
        List<TestProgressDetail> updateItemList = new ArrayList<>();
        for (TestProgressDetail item : remindTestProDetailList) {
            TestProgressDetail updateItem = new TestProgressDetail();
            updateItem.setId(item.getId());
            // 计划开始时间 = 上一阶段的计划结束时间 + 上一阶段工程师勾选情况计算得出的工作天数
            LocalDate planStartLocalDate = planEndDayOfLastData.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(needAddDay);
            Date inDate = Date.from(planStartLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            updateItem.setInDate(inDate);
            // 获取存储天数
            Long storeDay = item.getDay();
            // 计划结束时间 = 计划开始时间 + 存储天数，将LocalDate转换成Date
            updateItem.setOutDate(Date.from(planStartLocalDate.plusDays(storeDay).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            updateItemList.add(updateItem);
            // 重新计算上一阶段的计划结束时间
            planEndDayOfLastData = updateItem.getOutDate();
            // 重新计算上一阶段工程师勾选情况得出的工作天数
            needAddDay = calWorkDayByTestProDetail(item, totalStageSize);
        }
        if (updateItemList.size() > 0) {
            testProgressDetailService.updateBatchById(updateItemList);
        }
    }


    // 根据工程师勾选的数据计算得出工作天数
    public int calWorkDayByTestProDetail(TestProgressDetail testProDetail, int totalStageSize) {
        int dayNum = 0;
        boolean haveInsFlag = false;
//        List<String> testContentList = new ArrayList<>();
//        testContentList.add(testProDetail.getVoltage());
//        testContentList.add(testProDetail.getInnerres());
//        testContentList.add(testProDetail.getHeight());
//        testContentList.add(testProDetail.getVolume());
//        testContentList.add(testProDetail.getWeight());
//        testContentList.add(testProDetail.getIsolateres());
        if ("1".equals(testProDetail.getSmallinspection())) {
            haveInsFlag = true;
            dayNum = dayNum + 2;
        }
        if ("1".equals(testProDetail.getLargeinspection())) {
            haveInsFlag = true;
            dayNum = dayNum + 5;
        }
        if ("1".equals(testProDetail.getRecharge())) {
            haveInsFlag = true;
            dayNum = dayNum + 1;
        }
        // 2023-11-17需求：默认生成内阻、电压
//        if (testContentList.stream().anyMatch("1"::equals)) {
            if (testProDetail.getOrderNumber() == 1 || testProDetail.getOrderNumber() == totalStageSize || !haveInsFlag) {
                dayNum = dayNum + 1;
            } else {
                dayNum = dayNum + 2;
            }
//        }
        return dayNum;
    }

    /**
     * 计算中检信息所有存储阶段的计划开始时间和结束时间,并同步到计划详细表(TEST_PROGRESS_DETAIL)
     */
    public void syncAllTestProDetailDateOfLims(List<TestProgressDetail> allTestProDetailList, Date inBoxStartDate) {
        List<TestProgressDetail> updateItemList = new ArrayList<>();
        // 上一条数据的计划结束时间
        Date planEndDayOfLastData = null;
        for (TestProgressDetail item : allTestProDetailList) {
            TestProgressDetail updateItem = new TestProgressDetail();
            updateItem.setId(item.getId());
            // 存储阶段首条数据
            if (item.getOrderNumber() == 1) {
                // lims的第一个存储阶段的存储天数一定为0
                // lims的第一个存储阶段：计划开始时间 = 计划结束时间 = 进箱开始时间
                updateItem.setInDate(inBoxStartDate);
                updateItem.setOutDate(inBoxStartDate);
            } else if (item.getOrderNumber() == 2) {
                // lims的第二个存储阶段：计划开始时间 = 进箱开始时间，计划结束时间 = 计划开始时间 + 存储天数
                updateItem.setInDate(inBoxStartDate);
                Long storeDay = item.getDay();
                LocalDate planStartLocalDate = inBoxStartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                updateItem.setOutDate(Date.from(planStartLocalDate.plusDays(storeDay).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            } else {
                // 除了第一和第二存储阶段的场景
                // 除了第一和第二存储阶段的计划开始时间 = 上一条数据的计划结束时间 + 3
                LocalDate planStartLocalDate = planEndDayOfLastData.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(3);
                Date inDate = Date.from(planStartLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                updateItem.setInDate(inDate);
                // 获取存储天数
                Long storeDay = item.getDay();
                // 计划结束时间 = 计划开始时间 + 存储天数，然后将LocalDate转换成Date
                updateItem.setOutDate(Date.from(planStartLocalDate.plusDays(storeDay).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            updateItemList.add(updateItem);
            planEndDayOfLastData = updateItem.getOutDate();
        }
        testProgressDetailService.updateBatchById(updateItemList);
    }

    /**
     * 判断是阶段式日历寿命测试还是普通日历寿命测试，并设置待办任务的测试样品数量和是否是阶段式日历寿命测试的标识
     */
    public boolean judgeStageCalTestAndSetSampleNum(Long ordTaskId, TestProjectTodoTask todoTask) {
        LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid, ordTaskId);
        List<TLimsOrdtaskCondition> ordtaskConditionBeans = this.tLimsOrdtaskConditionService.list(conditionLambdaQueryWrapper);
        List<TLimsOrdtaskCondition> outBoxBatteryNums = ordtaskConditionBeans.stream()
                .filter(o -> "每次出箱电池数".equals(o.getConditionname())).collect(Collectors.toList());
        boolean stageFlag = outBoxBatteryNums.size() == 1;
        if (stageFlag && todoTask != null) {
            // 阶段式日历寿命测试
            Integer sampleNum = Integer.valueOf(outBoxBatteryNums.get(0).getConditionvalue());
            todoTask.setStageFlag(1);
            todoTask.setSampleNum(sampleNum);
        }
        return stageFlag;
    }

    /**
     * 根据LIMS系统的测试任务生成待办任务
     */
    public void generateToDoTaskByLims(TLimsOrdtask testerAndRealStartTime, List<Long> ordtaskIdList, Date planStartTime, Date planEndTime) throws Exception{
        HashMap ordParam = new HashMap();
        ordParam.put("ids", ordtaskIdList);
        List<TLimsOrdtask> ordtaskList = getAndTransLimsOrdtasks(ordParam);
        for (TLimsOrdtask item : ordtaskList) {
            //查询出测试项目对应的委托单信息
            TLimsFolder folderBean = tLimsFolderService.getById(item.getFolderid());
            TestProjectTodoTask todoTask = new TestProjectTodoTask();
            todoTask.setFolderNo(item.getFolderno());
            todoTask.setWtrName(item.getOtherPropertiesItem("wtrname"));
            todoTask.setTaskStatus("待处理");
            todoTask.setTester(testerAndRealStartTime.getTester());
            todoTask.setTesterCode(testerAndRealStartTime.getTestercode());
            todoTask.setParticipator(testerAndRealStartTime.getParticipator());
            todoTask.setParticipatorCode(testerAndRealStartTime.getParticipatorcode());
            // TestProgress有数据就生成待办，而不是看testname等于日历寿命
            LambdaQueryWrapper<TestProgress> testProgressWrapper = new LambdaQueryWrapper<>();
            testProgressWrapper.eq(TestProgress::getOrdtaskid, item.getId());
            testProgressWrapper.eq(TestProgress::getStatus, 0);
            testProgressWrapper.eq(TestProgress::getDeleteStatus, 0);
            List<TestProgress> testProgressList = testProgressService.list(testProgressWrapper);
            if (testProgressList.size() == 1) {
                // 判断是阶段式日历寿命测试还是普通日历寿命测试
                boolean stageFlag = judgeStageCalTestAndSetSampleNum(item.getId(), todoTask);
                // 生成日历寿命待办任务(对应中检信息的第一条)
                // 把测试技师同步到计划主表(TEST_PROGRESS)
                TestProgressDetail firstTestProDetail = limsSyncTestProgress(item, testerAndRealStartTime);
                todoTask.setInBoxPosition("样品室");
                todoTask.setProductName(testProgressList.get(0).getProductName());
                todoTask.setTestType(testProgressList.get(0).getTestType());
                if (!stageFlag) {
                    List<String> cellTestCodes = (List<String>)JSONArray.parse(testProgressList.get(0).getCellTestCodes());
                    Integer sampleNum = cellTestCodes == null ? null : cellTestCodes.size();
                    todoTask.setSampleNum(sampleNum);
                }
                todoTask.setOrdTaskId(firstTestProDetail.getId());
                todoTask.setTestName(item.getAlias());
                todoTask.setTestProgressId(firstTestProDetail.getProgressId());
                todoTask.setTaskType("rlsmcs_first");
                todoTask.setVideoFlag(firstTestProDetail.getVideo());
                todoTask.setPictureFlag(firstTestProDetail.getPicture());
                boolean haveInspectionFlag = "1".equals(firstTestProDetail.getLargeinspection())
                        || "1".equals(firstTestProDetail.getSmallinspection()) || "1".equals(firstTestProDetail.getRecharge());
                todoTask.setHaveInspection(haveInspectionFlag ? 1 : 0);
                // 分配完任务生成首个待办任务，待办任务的计划开始日期是PMC分配的日期，计划结束日期是根据工程师勾选的数据计算得来
                todoTask.setPlanStartTime(firstTestProDetail.getInDate());
                todoTask.setPlanEndTime(firstTestProDetail.getOutDate());
                // 生成待办任务的待填测试数据
                CalendarLifeTestEntity calendarLifeTestEntity = CalendarLifeTestEntity.builder()
                        .voltage(firstTestProDetail.getVoltage()).innerres(firstTestProDetail.getInnerres())
                        .largeInspection(firstTestProDetail.getLargeinspection()).smallInspection(firstTestProDetail.getSmallinspection())
                        .recharge(firstTestProDetail.getRecharge()).orderNumber(firstTestProDetail.getOrderNumber())
                        .volume(firstTestProDetail.getVolume()).weight(firstTestProDetail.getWeight()).picture(firstTestProDetail.getPicture())
                        .isolateres(firstTestProDetail.getIsolateres()).height(firstTestProDetail.getHeight()).video(firstTestProDetail.getVideo())
                        .totalDay(firstTestProDetail.getTotalDay()).progressId(firstTestProDetail.getProgressId()).testProgressDetailId(firstTestProDetail.getId()).build();
                boolean needGenerateFlag = generateFilledDataByTaskType(calendarLifeTestEntity, item, "rlsmcs_first", stageFlag, null);
                LambdaQueryWrapper<TestProjectTodoTask> todoTaskWrapper = new LambdaQueryWrapper<>();
                todoTaskWrapper.eq(TestProjectTodoTask::getOrdTaskId, firstTestProDetail.getId());
                List<TestProjectTodoTask> existTodoTaskList = testProjectTodoTaskService.list(todoTaskWrapper);
                if (needGenerateFlag && existTodoTaskList.size() == 0) {
                    testProjectTodoTaskService.save(todoTask);
                    // 生成待办后计划表的状态从Plan变为Ongoing
                    TestProgress testProgress = new TestProgress();
                    testProgress.setId(firstTestProDetail.getProgressId());
                    testProgress.setTestStatus("Ongoing");
                    testProgressService.updateById(testProgress);
                }
            } else if (MainProcessConstant.LABORATORYID_JMSYS.equals(folderBean.getLaboratoryid())) {
                todoTask.setOrdTaskId(item.getId());
                todoTask.setTestName(item.getTestname());
                todoTask.setTestCode(item.getTestcode());
                todoTask.setWtrId(item.getOtherPropertiesItem("wtrid"));
                todoTask.setTaskType("jmcs");
                todoTask.setPlanStartTime(clearTimeByDate(planStartTime));
                todoTask.setPlanEndTime(clearTimeByDate(planEndTime));
                todoTask.setRealStartTime(testerAndRealStartTime.getRealstarttime());
                todoTask.setRealEndTime(item.getRealendtime());
                todoTask.setTestContent(item.getTestcontent());
                todoTask.setRemark(item.getOtherPropertiesItem("folderremark"));
                todoTask.setTestParameter(item.getTestparameter());
                testProjectTodoTaskService.save(todoTask);
            }
        }
    }

    // 根据工程师勾选的数据计算得出计划结束日期
    public Date calByTestProDetail(TestProgressDetail currentTestProDetail, Date startDate, String stage) {
        int dayNum = 0;
        boolean haveInsFlag = false;
//        List<String> testContentList = new ArrayList<>();
//        testContentList.add(currentTestProDetail.getVoltage());
//        testContentList.add(currentTestProDetail.getInnerres());
//        testContentList.add(currentTestProDetail.getHeight());
//        testContentList.add(currentTestProDetail.getVolume());
//        testContentList.add(currentTestProDetail.getWeight());
//        testContentList.add(currentTestProDetail.getIsolateres());
        if ("1".equals(currentTestProDetail.getSmallinspection())) {
            haveInsFlag = true;
            dayNum = dayNum + 2;
        }
        if ("1".equals(currentTestProDetail.getLargeinspection())) {
            haveInsFlag = true;
            dayNum = dayNum + 5;
        }
        if ("1".equals(currentTestProDetail.getRecharge())) {
            haveInsFlag = true;
            dayNum = dayNum + 1;
        }
        // 2023-11-17需求：默认生成内阻、电压
//        if (testContentList.stream().anyMatch("1"::equals)) {
            if ("first".equals(stage) || "last".equals(stage) || !haveInsFlag) {
                dayNum = dayNum + 1;
            } else {
                dayNum = dayNum + 2;
            }
//        }
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(dayNum);
        Date endDate = Date.from(startLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        return clearTimeByDate(endDate);
    }

    public Date clearTimeByDate(Date sourceDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sourceDate); // 将Date对象设置到Calendar对象中
        // 设置时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时（24小时制）
        calendar.set(Calendar.MINUTE, 0); // 设置分钟
        calendar.set(Calendar.SECOND, 0); // 设置秒
        // 重新获取Date对象
        sourceDate = calendar.getTime();
        return sourceDate;
    }

    /**
     * 根据任务类型生成存储阶段的待办任务的待填测试数据
     */
    public boolean generateFilledDataByTaskType(CalendarLifeTestEntity testProDetail, TLimsOrdtask item, String taskType, boolean stageFlag, TestProjectTodoTask todoTask) throws Exception {
        LambdaQueryWrapper<TestProgressDetail> detailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        detailLambdaQueryWrapper.eq(TestProgressDetail::getProgressId, testProDetail.getProgressId());
        detailLambdaQueryWrapper.eq(TestProgressDetail::getStatus, 0);
        detailLambdaQueryWrapper.eq(TestProgressDetail::getDeleteStatus, 0);
        detailLambdaQueryWrapper.orderByAsc(TestProgressDetail::getOrderNumber);
        List<TestProgressDetail> testProgressDetailList = testProgressDetailService.list(detailLambdaQueryWrapper);
        // 阶段式日历寿命转移箱子
        boolean needTransferBoxFlag = false;
        if (stageFlag) {
            if ("rlsmcs_middle".equals(taskType) && testProDetail.getTotalDay() >= 60) {
                List<TestProgressDetail> previousDetailList = testProgressDetailList.stream()
                        .filter(o -> o.getOrderNumber() < testProDetail.getOrderNumber()).collect(Collectors.toList());
                // 判断之前是否转移过箱子，没转移过则transferBoxFlag设置为1(需要转移温箱)
                if (previousDetailList.stream().noneMatch(o -> Objects.equals(o.getTransferBoxFlag(), 2))) {
                    needTransferBoxFlag = true;
                }
            }
        }
        // 2023-11-17需求：默认生成内阻、电压
//        boolean chooseVoltage = StringUtils.equals(testProDetail.getVoltage(), "1");
//        boolean chooseInnerres = StringUtils.equals(testProDetail.getInnerres(), "1");
        boolean chooseLargeinspection = StringUtils.equals(testProDetail.getLargeInspection(), "1");
        boolean chooseSmallinspection = StringUtils.equals(testProDetail.getSmallInspection(), "1");
        boolean chooseRecharge = StringUtils.equals(testProDetail.getRecharge(), "1");
        boolean chooseVolume = StringUtils.equals(testProDetail.getVolume(), "1");
        boolean chooseWeight = StringUtils.equals(testProDetail.getWeight(), "1");
        boolean chooseIsolateres = StringUtils.equals(testProDetail.getIsolateres(), "1");
        boolean chooseHeight = StringUtils.equals(testProDetail.getHeight(), "1");
        boolean choosePicture = StringUtils.equals(testProDetail.getPicture(), "1");
//        boolean chooseVideo = StringUtils.equals(testProDetail.getVideo(), "1");
//        if (chooseInnerres || chooseVoltage || chooseHeight || chooseVolume || chooseWeight || chooseIsolateres) {
            LambdaQueryWrapper<TestProgress> testProWrapper = new LambdaQueryWrapper<>();
            testProWrapper.eq(TestProgress::getId, testProDetail.getProgressId());
            testProWrapper.eq(TestProgress::getStatus, 0);
            testProWrapper.eq(TestProgress::getDeleteStatus, 0);
            TestProgress testProgress = testProgressService.getOne(testProWrapper);
            List<String> cellTestCodeList = (List<String>)JSONArray.parse(testProgress.getCellTestCodes());
            // 阶段式日历寿命测试【初始性能检测】阶段全部电芯进箱且不出箱，其余测试阶段部分电芯出箱后不再进箱
            if (stageFlag && !"rlsmcs_first".equals(taskType)) {
                // 计算本阶段出箱的电芯编码
                // 总电池数 = 每次出箱电池数*(中检次数-1)
                // 总电池数: totalBatteryNum
                Integer totalBatteryNum = cellTestCodeList.size();
                // checkNum: 中检次数-1
                Integer checkNum = testProgressDetailList.size() - 1;
                // outBoxBatteryNum: 每次出箱电池数
                int outBoxBatteryNum = totalBatteryNum/checkNum;
                // 获取除了初始阶段外的所有存储阶段及其对应出箱的电池
                List<List<String>> cellTestCodeListList = Lists.partition(cellTestCodeList, outBoxBatteryNum);
                cellTestCodeList = cellTestCodeListList.get(testProDetail.getOrderNumber() - 2);

                // 判断之前是否转移过箱子来设置待办任务的存储位置
                List<TestProgressDetail> previousDetailList = testProgressDetailList.stream()
                        .filter(o -> o.getOrderNumber() < testProDetail.getOrderNumber()).collect(Collectors.toList());
                List<JSONObject> inBoxPositionDataList;
                if (previousDetailList.stream().noneMatch(o -> Objects.equals(o.getTransferBoxFlag(), 2))) {
                    // 没转移过则取初始存储阶段的进箱位置
                    inBoxPositionDataList = JSONArray.parseArray(testProgressDetailList.get(0).getLifeTestRecord(), JSONObject.class);
                } else {
                    // 转移过则取转移后的进箱位置
                    TestProgressDetail alreadyTransferData = testProgressDetailList.stream()
                            .filter(o -> Objects.equals(o.getTransferBoxFlag(), 2)).collect(Collectors.toList()).get(0);
                    inBoxPositionDataList = JSONArray.parseArray(alreadyTransferData.getTransferBoxInfo(), JSONObject.class);
                }
                List<String> inBoxPositionList = new ArrayList<>();
                for (JSONObject jsonObject : inBoxPositionDataList) {
                    String cellTestCode = jsonObject.getString("cellTestCode");
                    if (cellTestCodeList.stream().anyMatch(o -> o.equals(cellTestCode))) {
                        inBoxPositionList.add(jsonObject.getString("inBoxPosition"));
                    }
                }
                if (todoTask != null) {
                    inBoxPositionList = inBoxPositionList.stream().distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                    todoTask.setInBoxPosition(String.join(",",inBoxPositionList));
                    todoTask.setProductName(testProgress.getProductName());
                    todoTask.setTestType(testProgress.getTestType());
                }
            }

            List<Map<String, Object>> toBeFilledDataMapList = new ArrayList<>();
            if (cellTestCodeList == null || cellTestCodeList.size() == 0) {
                throw new Exception("日历寿命测试计划主表电芯集合为空！");
            }
            for (String cellTestCode : cellTestCodeList) {
                Map<String, Object> toBeFilledDataMap = new LinkedHashMap<>();
                toBeFilledDataMap.put("alias", item.getAlias());
                toBeFilledDataMap.put("cellTestCode", cellTestCode);
                if (chooseLargeinspection) {
                    toBeFilledDataMap.put("middleCheck", "large");
                    // 工作台数据录入时是否选择过数据,有数据时：选中数据为true,无数据时：点开按钮就为true
                    toBeFilledDataMap.put("isMiddleClick", false);
                    // 保存中检时的测试数据
                    toBeFilledDataMap.put("checkData", null);
                } else if (chooseSmallinspection) {
                    toBeFilledDataMap.put("middleCheck", "small");
                    toBeFilledDataMap.put("isMiddleClick", false);
                    toBeFilledDataMap.put("checkData", null);
                } else if (chooseRecharge) {
                    toBeFilledDataMap.put("middleCheck", "recharge");
                    toBeFilledDataMap.put("isMiddleClick", false);
                    toBeFilledDataMap.put("checkData", null);
                } else {
                    toBeFilledDataMap.put("middleCheck", "normal");
                    toBeFilledDataMap.put("isMiddleClick", true);
                }
                // 设置电芯状态
                setDetailBatteryStatus(cellTestCode, taskType, testProDetail.getOrderNumber() - 1, toBeFilledDataMap, testProgressDetailList, stageFlag);
                // 2023-11-17需求：默认生成内阻、电压
//                if (chooseInnerres) {
                    toBeFilledDataMap.put("beforeInnerres", null);
//                }
//                if (chooseVoltage) {
                    toBeFilledDataMap.put("beforeVoltage", null);
//                }
                // 用于记录填写内阻的时间
                toBeFilledDataMap.put("timeOfFillInnerres", null);
                // 中间存储阶段如果有大/小中检需要生成中检后电压和内阻
                if ("rlsmcs_middle".equals(taskType)) {
                    if (chooseLargeinspection || chooseSmallinspection || chooseRecharge) {
//                        if (chooseInnerres) {
                            toBeFilledDataMap.put("afterInnerres", null);
                            // 用于记录填写中检后内阻的时间
                            toBeFilledDataMap.put("timeOfFillInnerres2", null);
//                        }
//                        if (chooseVoltage) {
                            toBeFilledDataMap.put("afterVoltage", null);
//                        }
                    }
                }
                if (chooseVolume) {
                    toBeFilledDataMap.put("volume", null);
                }
                if (chooseWeight) {
                    toBeFilledDataMap.put("weight", null);
                }
                if (chooseIsolateres) {
                    toBeFilledDataMap.put("isolateres", null);
                }
                toBeFilledDataMap.put("heightType", testProgress.getSampleType());
                if (chooseHeight) {
                    // 获取尺寸信息
                    List<TestProgressSize> testProgressSizeList = testProgressSizeService.getByProgressId(testProDetail.getProgressId());
                    // 有尺寸信息就根据工程师填写的尺寸信息生成待录入数据，否则生成默认的录入数据
                    if (testProgressSizeList.size() > 0) {
                        generateSizeInfo(testProgress.getSampleType(), testProgressSizeList, toBeFilledDataMap);
                    } else {
                        // 生成默认的录入数据
                        generateDefaultSizeInfo(testProgress.getSampleType(), toBeFilledDataMap);
                    }
                }
                if (choosePicture) {
                    // 生成样品照片录入
                    generatePictureInfo(testProgress.getSampleType(), toBeFilledDataMap);
                }
                if (stageFlag) { // 阶段式日历寿命测试
                    // 只有初始存储阶段才需要填写进箱位置
                    if ("rlsmcs_first".equals(taskType)) {
                        toBeFilledDataMap.put("inBoxPosition", null);
                        toBeFilledDataMap.put("inBoxPositionId", null);
                    }
                } else { // 普通日历寿命测试
                    // 最后一个存储阶段不需要填写进箱位置
                    if (!"rlsmcs_last".equals(taskType)) {
                        toBeFilledDataMap.put("inBoxPosition", null);
                        toBeFilledDataMap.put("inBoxPositionId", null);
                    }
                }
                toBeFilledDataMapList.add(toBeFilledDataMap);
            }
        // 将待填数据转换成JSON字符串并保存到数据库
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(toBeFilledDataMapList);
        LambdaUpdateWrapper<TestProgressDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TestProgressDetail::getCurrentStep, null);
        updateWrapper.set(TestProgressDetail::getVideoId, null);
        updateWrapper.set(TestProgressDetail::getVideoName, null);
        updateWrapper.set(TestProgressDetail::getLifeTestRecord, jsonString);
        // 阶段式日历寿命转移箱子
        if (needTransferBoxFlag) {
            updateWrapper.set(TestProgressDetail::getTransferBoxFlag, 1);
            List<Map<String, String>> transferBatteryInfos = getTransferBatteryInfo(testProgressDetailList, testProDetail.getTestProgressDetailId(), cellTestCodeList);
            updateWrapper.set(TestProgressDetail::getTransferBoxInfo, objectMapper.writeValueAsString(transferBatteryInfos));
        }
        updateWrapper.eq(TestProgressDetail::getId, testProDetail.getTestProgressDetailId());
        testProgressDetailService.update(updateWrapper);
        return true;
//        } else {
//            return false;
//        }
    }

    public void setDetailBatteryStatus(String cellTestCode, String taskType, int previousOrderNumber, Map<String, Object> toBeFilledDataMap,
                                       List<TestProgressDetail> testProgressDetailList, boolean stageFlag) {
        if ("rlsmcs_first".equals(taskType)) {
            toBeFilledDataMap.put("batteryStatus", "ongoing");
        } else {
            List<TestProgressDetail> detailList;
            if (stageFlag) {
                // 阶段式日历寿命测试：当前阶段的电池状态与初始阶段的电池状态保持一致
                detailList = testProgressDetailList.stream()
                        .filter(o -> o.getOrderNumber() == 1).collect(Collectors.toList());
            } else {
                // 普通日历寿命测试：当前阶段的电池状态与上一阶段的电池状态保持一致
                detailList = testProgressDetailList.stream()
                        .filter(o -> o.getOrderNumber() == previousOrderNumber).collect(Collectors.toList());
            }
            if (detailList.size() == 1) {
                List<JSONObject> recordList = JSONArray.parseArray(detailList.get(0).getLifeTestRecord(), JSONObject.class);
                List<JSONObject> currentCellTestCodes = recordList.stream()
                        .filter(o -> StringUtils.equals(o.getString("cellTestCode"),cellTestCode)).collect(Collectors.toList());
                if (currentCellTestCodes.size() == 1) {
                    String curBatteryStatus = currentCellTestCodes.get(0).getString("batteryStatus");
                    if (StringUtils.isEmpty(curBatteryStatus)) {
                        curBatteryStatus = "ongoing";
                    }
                    toBeFilledDataMap.put("batteryStatus", curBatteryStatus);
                } else {
                    toBeFilledDataMap.put("batteryStatus", "ongoing");
                }
            } else {
                toBeFilledDataMap.put("batteryStatus", "ongoing");
            }
        }
    }

    public List<Map<String, String>> getTransferBatteryInfo (List<TestProgressDetail> testProgressDetailList, Long testProgressDetailId, List<String> curCellTestCodeList) {
        List<Map<String, String>> resultMapList = new ArrayList<>();
        int previousIndex = testProgressDetailList.stream().map(TestProgressDetail::getId).collect(Collectors.toList()).indexOf(testProgressDetailId);
        List<TestProgressDetail> alreadyOutBoxDetails = testProgressDetailList.subList(1, previousIndex);
        TestProgressDetail firstStoreStage = testProgressDetailList.get(0);
        String jsonString = firstStoreStage.getLifeTestRecord();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 将 JSON 字符串转换为 List
            if (jsonString != null) {
                List<Map<String, Object>> resultList = objectMapper.readValue(jsonString, new TypeReference<List<Map<String, Object>>>() {});
                List<String> allCellTestCodeList = resultList.stream().map(o -> String.valueOf(o.get("cellTestCode"))).collect(Collectors.toList());
                List<String> alreadyOutBoxCellTestCodeList = new ArrayList<>();
                for (TestProgressDetail itemDetails : alreadyOutBoxDetails) {
                    List<Map<String, Object>> mapList = objectMapper.readValue(itemDetails.getLifeTestRecord(), new TypeReference<List<Map<String, Object>>>() {});
                    alreadyOutBoxCellTestCodeList.addAll(mapList.stream().map(o -> String.valueOf(o.get("cellTestCode"))).collect(Collectors.toList()));
                }
                alreadyOutBoxCellTestCodeList.addAll(curCellTestCodeList);
                List<String> restCellTestCodeList = allCellTestCodeList.stream().filter(o -> !alreadyOutBoxCellTestCodeList.contains(o)).collect(Collectors.toList());
                for (String cellTestCode : restCellTestCodeList) {
                    Map<String, Object> currentMap = resultList.stream().filter(o -> Objects.equals(cellTestCode, o.get("cellTestCode"))).collect(Collectors.toList()).get(0);
                    Map<String, String> itemMap = new HashMap<>();
                    String originInBoxPosition = ObjectUtils.isNotEmpty(currentMap.get("inBoxPosition")) ? String.valueOf(currentMap.get("inBoxPosition")) : "";
                    String originInBoxPositionId = ObjectUtils.isNotEmpty(currentMap.get("inBoxPositionId")) ? String.valueOf(currentMap.get("inBoxPositionId")) : "";
                    String batteryStatus = ObjectUtils.isNotEmpty(currentMap.get("batteryStatus")) ? String.valueOf(currentMap.get("batteryStatus")) : "ongoing";
                    itemMap.put("cellTestCode", cellTestCode);
                    itemMap.put("originInBoxPosition", originInBoxPosition);
                    itemMap.put("originInBoxPositionId", originInBoxPositionId);
                    itemMap.put("inBoxPosition", null);
                    itemMap.put("inBoxPositionId", null);
                    itemMap.put("transferBatteryTime", null);
                    itemMap.put("actTransferBatteryTime", null);
                    itemMap.put("batteryStatus", batteryStatus);
                    resultMapList.add(itemMap);
                }
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return resultMapList;
    }

    public void generateDefaultSizeInfo(String sampleType, Map<String, Object> toBeFilledDataMap) {
        switch (sampleType) {
            case "G圆柱" :
                putDefaultSizeInfoBySizeType(toBeFilledDataMap, sizeTypeOfGCylinderMap);
                break;
            case "C圆柱":
                putDefaultSizeInfoBySizeType(toBeFilledDataMap, sizeTypeOfCCylinderMap);
                break;
            case "V圆柱":
                putDefaultSizeInfoBySizeType(toBeFilledDataMap, sizeTypeOfVCylinderMap);
                break;
            case "方型":
                putDefaultSizeInfoBySizeType(toBeFilledDataMap, sizeTypeOfSquareMap);
                break;
            default:
                // 软包/模组/其它
                putDefaultSizeInfoBySizeType(toBeFilledDataMap, sizeTypeOfSoftBagOrMzMap);
        }
    }

    public void generatePictureInfo(String sampleType, Map<String, Object> toBeFilledDataMap) {
        Map<String, Object> picMap = new LinkedHashMap<>();
        Map<String, String> idNameMap = new LinkedHashMap<>();
        idNameMap.put("id", null);
        idNameMap.put("name", null);
        idNameMap.put("time", null);
        switch (sampleType) {
            case "方型" :
                picMap.put("squareTop",idNameMap);       // 顶部
                picMap.put("squareBottom",idNameMap);    // 底部
                picMap.put("squareLarge1",idNameMap);    // 大面1
                picMap.put("squareLarge2",idNameMap);    // 大面2
                picMap.put("squareSide1",idNameMap);     // 侧面1
                picMap.put("squareSide2",idNameMap);     // 侧面2
                picMap.put("squareAssemblyTop",idNameMap);      // 工装装配顶部
                picMap.put("squareAssemblyFront",idNameMap);    // 工装装配正面
                picMap.put("squareAssemblyWhole",idNameMap);    // 工装装配整体
                break;
            case "软包":
                picMap.put("softLarge1",idNameMap);    // 大面1
                picMap.put("softLarge2",idNameMap);    // 大面2
                picMap.put("softAssemblyTop",idNameMap);      // 工装装配顶部
                picMap.put("softAssemblyFront",idNameMap);    // 工装装配正面
                picMap.put("softAssemblyWhole",idNameMap);    // 工装装配整体
                break;
            case "模组":
               picMap.put("mzTop",idNameMap);       // 顶部
               picMap.put("mzBottom",idNameMap);    // 底部
               picMap.put("mzLarge1",idNameMap);    // 大面1
               picMap.put("mzLarge2",idNameMap);    // 大面2
               picMap.put("mzSide1",idNameMap);     // 侧面1
               picMap.put("mzSide2",idNameMap);     // 侧面2
                break;
            default:
                // G/C/V圆柱
                picMap.put("cylTerminalTop",idNameMap);      // 端子面
                picMap.put("cylVentBottom",idNameMap);       // vent面
                picMap.put("cylLarge1",idNameMap);           // 大面1
                picMap.put("cylLarge2",idNameMap);           // 大面2
                picMap.put("cylAssemblyTop",idNameMap);      // 工装装配顶部
                picMap.put("cylAssemblyFront",idNameMap);    // 工装装配正面
                picMap.put("cylAssemblyWhole",idNameMap);    // 工装装配整体
        }
        toBeFilledDataMap.put("samplePicture", picMap);
    }

    public void generateSizeInfo(String sampleType, List<TestProgressSize> testProgressSizeList, Map<String, Object> toBeFilledDataMap) {
            switch (sampleType) {
                case "G圆柱" :
                    putSizeInfoBySizeType(testProgressSizeList, toBeFilledDataMap, sizeTypeOfGCylinderMap);
                    break;
                case "C圆柱":
                    putSizeInfoBySizeType(testProgressSizeList, toBeFilledDataMap, sizeTypeOfCCylinderMap);
                    break;
                case "V圆柱":
                    putSizeInfoBySizeType(testProgressSizeList, toBeFilledDataMap, sizeTypeOfVCylinderMap);
                    break;
                case "方型":
                    putSizeInfoBySizeType(testProgressSizeList, toBeFilledDataMap, sizeTypeOfSquareMap);
                    break;
                default:
                    // 软包/模组/其它
                    putSizeInfoBySizeType(testProgressSizeList, toBeFilledDataMap, sizeTypeOfSoftBagOrMzMap);
            }
    }

    private void putSizeInfoBySizeType(List<TestProgressSize> testProgressSizeList, Map<String, Object> toBeFilledDataMap, Map<String, String> fieldMap) {
        for (TestProgressSize item : testProgressSizeList) {
            String[] dataInfo = fieldMap.get(item.getSizeType()).split("/");
            String field = dataInfo[2];
            int measureTime = item.getMeasureTime();
            for (int i = 1; i <= measureTime; i++) {
                toBeFilledDataMap.put(field + i, null);
            }
        }
    }

    private void putDefaultSizeInfoBySizeType(Map<String, Object> toBeFilledDataMap, Map<String, String> fieldMap) {
        List<TestProgressSize> defaultTestProgressSizeList = new ArrayList<>();
        for (String key : fieldMap.keySet()) {
            String[] fieldList = (fieldMap.get(key)).split("/");
            TestProgressSize testProgressSize = new TestProgressSize();
            testProgressSize.setSizeType(key);
            testProgressSize.setMeasureTime(Integer.valueOf(fieldList[0]));
            testProgressSize.setCalRule(fieldList[1]);
            defaultTestProgressSizeList.add(testProgressSize);
        }
        for (TestProgressSize item : defaultTestProgressSizeList) {
            String[] dataInfo = fieldMap.get(item.getSizeType()).split("/");
            String field = dataInfo[2];
            int measureTime = item.getMeasureTime();
            for (int i = 1; i <= measureTime; i++) {
                toBeFilledDataMap.put(field + i, null);
            }
        }
    }

    /**
     * 尺寸类型为方型/模组/其他
     * 2023.9.11修改：每个测试点从1个变为6个
     */
    public void generateHeightOfSquareModule(Map<String, Object> toBeFilledDataMap) {
        toBeFilledDataMap.put("thickTopLeft1", null);       // 上左厚度1
        toBeFilledDataMap.put("thickTopMiddle1", null);     // 上中厚度1
        toBeFilledDataMap.put("thickTopRight1", null);      // 上右厚度1
        toBeFilledDataMap.put("thickMiddleLeft1", null);    // 中左厚度1
        toBeFilledDataMap.put("thickMiddle1", null);        // 中心厚度1
        toBeFilledDataMap.put("thickMiddleRight1", null);   // 中右厚度1
        toBeFilledDataMap.put("thickBottomLeft1", null);    // 下左厚度1
        toBeFilledDataMap.put("thickBottomMiddle1", null);  // 下中厚度1
        toBeFilledDataMap.put("thickBottomRight1", null);   // 下右厚度1
        toBeFilledDataMap.put("thickTopLeft2", null);       // 上左厚度2
        toBeFilledDataMap.put("thickTopMiddle2", null);     // 上中厚度2
        toBeFilledDataMap.put("thickTopRight2", null);      // 上右厚度2
        toBeFilledDataMap.put("thickMiddleLeft2", null);    // 中左厚度2
        toBeFilledDataMap.put("thickMiddle2", null);        // 中心厚度2
        toBeFilledDataMap.put("thickMiddleRight2", null);   // 中右厚度2
        toBeFilledDataMap.put("thickBottomLeft2", null);    // 下左厚度2
        toBeFilledDataMap.put("thickBottomMiddle2", null);  // 下中厚度2
        toBeFilledDataMap.put("thickBottomRight2", null);   // 下右厚度2
        toBeFilledDataMap.put("thickTopLeft3", null);       // 上左厚度3
        toBeFilledDataMap.put("thickTopMiddle3", null);     // 上中厚度3
        toBeFilledDataMap.put("thickTopRight3", null);      // 上右厚度3
        toBeFilledDataMap.put("thickMiddleLeft3", null);    // 中左厚度3
        toBeFilledDataMap.put("thickMiddle3", null);        // 中心厚度3
        toBeFilledDataMap.put("thickMiddleRight3", null);   // 中右厚度3
        toBeFilledDataMap.put("thickBottomLeft3", null);    // 下左厚度3
        toBeFilledDataMap.put("thickBottomMiddle3", null);  // 下中厚度3
        toBeFilledDataMap.put("thickBottomRight3", null);   // 下右厚度3
        toBeFilledDataMap.put("thickTopLeft4", null);       // 上左厚度4
        toBeFilledDataMap.put("thickTopMiddle4", null);     // 上中厚度4
        toBeFilledDataMap.put("thickTopRight4", null);      // 上右厚度4
        toBeFilledDataMap.put("thickMiddleLeft4", null);    // 中左厚度4
        toBeFilledDataMap.put("thickMiddle4", null);        // 中心厚度4
        toBeFilledDataMap.put("thickMiddleRight4", null);   // 中右厚度4
        toBeFilledDataMap.put("thickBottomLeft4", null);    // 下左厚度4
        toBeFilledDataMap.put("thickBottomMiddle4", null);  // 下中厚度4
        toBeFilledDataMap.put("thickBottomRight4", null);   // 下右厚度4
        toBeFilledDataMap.put("thickTopLeft5", null);       // 上左厚度5
        toBeFilledDataMap.put("thickTopMiddle5", null);     // 上中厚度5
        toBeFilledDataMap.put("thickTopRight5", null);      // 上右厚度5
        toBeFilledDataMap.put("thickMiddleLeft5", null);    // 中左厚度5
        toBeFilledDataMap.put("thickMiddle5", null);        // 中心厚度5
        toBeFilledDataMap.put("thickMiddleRight5", null);   // 中右厚度5
        toBeFilledDataMap.put("thickBottomLeft5", null);    // 下左厚度5
        toBeFilledDataMap.put("thickBottomMiddle5", null);  // 下中厚度5
        toBeFilledDataMap.put("thickBottomRight5", null);   // 下右厚度5
        toBeFilledDataMap.put("thickTopLeft6", null);       // 上左厚度6
        toBeFilledDataMap.put("thickTopMiddle6", null);     // 上中厚度6
        toBeFilledDataMap.put("thickTopRight6", null);      // 上右厚度6
        toBeFilledDataMap.put("thickMiddleLeft6", null);    // 中左厚度6
        toBeFilledDataMap.put("thickMiddle6", null);        // 中心厚度6
        toBeFilledDataMap.put("thickMiddleRight6", null);   // 中右厚度6
        toBeFilledDataMap.put("thickBottomLeft6", null);    // 下左厚度6
        toBeFilledDataMap.put("thickBottomMiddle6", null);  // 下中厚度6
        toBeFilledDataMap.put("thickBottomRight6", null);   // 下右厚度6
    }

    /**
     * 尺寸类型为软包，每个测试点1个
     */
    public void generateHeightOfSoftBag(Map<String, Object> toBeFilledDataMap) {
        toBeFilledDataMap.put("thicknessOne", null);   // 厚度1
        toBeFilledDataMap.put("thicknessTwo", null);   // 厚度2
        toBeFilledDataMap.put("thicknessThree", null); // 厚度3
        toBeFilledDataMap.put("thicknessFour", null);  // 厚度4
        toBeFilledDataMap.put("thicknessFive", null);  // 厚度5
    }

    /**
     * 尺寸类型为V圆柱
     */
    public void generateHeightOfVCylinder(Map<String, Object> toBeFilledDataMap) {
        toBeFilledDataMap.put("topPoint1", null);      // 顶点1
        toBeFilledDataMap.put("oneThird1", null);      // 1/3处1
        toBeFilledDataMap.put("oneSecond1", null);     // 1/2处1
        toBeFilledDataMap.put("secondThird1", null);   // 2/3处1
        toBeFilledDataMap.put("bottom1", null);        // 尾部1
        toBeFilledDataMap.put("topPoint2", null);      // 顶点2
        toBeFilledDataMap.put("oneThird2", null);      // 1/3处2
        toBeFilledDataMap.put("oneSecond2", null);     // 1/2处2
        toBeFilledDataMap.put("secondThird2", null);   // 2/3处2
        toBeFilledDataMap.put("bottom2", null);        // 尾部2
        toBeFilledDataMap.put("topPoint3", null);      // 顶点3
        toBeFilledDataMap.put("oneThird3", null);      // 1/3处3
        toBeFilledDataMap.put("oneSecond3", null);     // 1/2处3
        toBeFilledDataMap.put("secondThird3", null);   // 2/3处3
        toBeFilledDataMap.put("bottom3", null);        // 尾部3
        toBeFilledDataMap.put("topPoint4", null);      // 顶点4
        toBeFilledDataMap.put("oneThird4", null);      // 1/3处4
        toBeFilledDataMap.put("oneSecond4", null);     // 1/2处4
        toBeFilledDataMap.put("secondThird4", null);   // 2/3处4
        toBeFilledDataMap.put("bottom4", null);        // 尾部4
        toBeFilledDataMap.put("topPoint5", null);      // 顶点5
        toBeFilledDataMap.put("oneThird5", null);      // 1/3处5
        toBeFilledDataMap.put("oneSecond5", null);     // 1/2处5
        toBeFilledDataMap.put("secondThird5", null);   // 2/3处5
        toBeFilledDataMap.put("bottom5", null);        // 尾部5
        toBeFilledDataMap.put("topPoint6", null);      // 顶点6
        toBeFilledDataMap.put("oneThird6", null);      // 1/3处6
        toBeFilledDataMap.put("oneSecond6", null);     // 1/2处6
        toBeFilledDataMap.put("secondThird6", null);   // 2/3处6
        toBeFilledDataMap.put("bottom6", null);        // 尾部6
    }

    /**
     * 尺寸类型为G&C圆柱，
     * 2023.9.11修改：每个测试点从1个变为6个
     * 原始：A点直径，aPointDiameter  现在：上部直径，topPointDiameter
     * 原始：B点直径，bPointDiameter  现在：中部直径，middlePointDiameter
     * 原始：C点直径，cPointDiameter  现在：下部直径，bottomPointDiameter
     * 新增：盖板内圈、盖板外圈
     */
    public void generateHeightOfCylinder(Map<String, Object> toBeFilledDataMap) {
        toBeFilledDataMap.put("topPointDiameter1", null);     // 上部直径1
        toBeFilledDataMap.put("middlePointDiameter1", null);  // 中部直径1
        toBeFilledDataMap.put("bottomPointDiameter1", null);  // 下部直径1
        toBeFilledDataMap.put("totalHeight1", null);          // 端高1
        toBeFilledDataMap.put("shoulderHeight1", null);       // 肩高1
        toBeFilledDataMap.put("coverInnerRing1", null);      // 盖板内圈1
        toBeFilledDataMap.put("coverOuterRing1", null);      // 盖板外圈1
        toBeFilledDataMap.put("topPointDiameter2", null);     // 上部直径2
        toBeFilledDataMap.put("middlePointDiameter2", null);  // 中部直径2
        toBeFilledDataMap.put("bottomPointDiameter2", null);  // 下部直径2
        toBeFilledDataMap.put("totalHeight2", null);          // 端高2
        toBeFilledDataMap.put("shoulderHeight2", null);       // 肩高2
        toBeFilledDataMap.put("coverInnerRing2", null);      // 盖板内圈2
        toBeFilledDataMap.put("coverOuterRing2", null);      // 盖板外圈2
        toBeFilledDataMap.put("topPointDiameter3", null);     // 上部直径3
        toBeFilledDataMap.put("middlePointDiameter3", null);  // 中部直径3
        toBeFilledDataMap.put("bottomPointDiameter3", null);  // 下部直径3
        toBeFilledDataMap.put("totalHeight3", null);          // 端高3
        toBeFilledDataMap.put("shoulderHeight3", null);       // 肩高3
        toBeFilledDataMap.put("coverInnerRing3", null);      // 盖板内圈3
        toBeFilledDataMap.put("coverOuterRing3", null);      // 盖板外圈3
        toBeFilledDataMap.put("topPointDiameter4", null);     // 上部直径4
        toBeFilledDataMap.put("middlePointDiameter4", null);  // 中部直径4
        toBeFilledDataMap.put("bottomPointDiameter4", null);  // 下部直径4
        toBeFilledDataMap.put("totalHeight4", null);          // 端高4
        toBeFilledDataMap.put("shoulderHeight4", null);       // 肩高4
        toBeFilledDataMap.put("coverInnerRing4", null);      // 盖板内圈4
        toBeFilledDataMap.put("coverOuterRing4", null);      // 盖板外圈4
        toBeFilledDataMap.put("topPointDiameter5", null);     // 上部直径5
        toBeFilledDataMap.put("middlePointDiameter5", null);  // 中部直径5
        toBeFilledDataMap.put("bottomPointDiameter5", null);  // 下部直径5
        toBeFilledDataMap.put("totalHeight5", null);          // 端高5
        toBeFilledDataMap.put("shoulderHeight5", null);       // 肩高5
        toBeFilledDataMap.put("coverInnerRing5", null);      // 盖板内圈5
        toBeFilledDataMap.put("coverOuterRing5", null);      // 盖板外圈5
        toBeFilledDataMap.put("topPointDiameter6", null);     // 上部直径6
        toBeFilledDataMap.put("middlePointDiameter6", null);  // 中部直径6
        toBeFilledDataMap.put("bottomPointDiameter6", null);  // 下部直径6
        toBeFilledDataMap.put("totalHeight6", null);          // 端高6
        toBeFilledDataMap.put("shoulderHeight6", null);       // 肩高6
        toBeFilledDataMap.put("coverInnerRing6", null);      // 盖板内圈6
        toBeFilledDataMap.put("coverOuterRing6", null);      // 盖板外圈6
    }

    /**
     * 根据PBI系统【日历寿命测试计划】页面新增的测试任务生成待办任务
     * 生成日历寿命待办任务(对应中检信息的第一条)
     */
    public void generateToDoTaskByPBI(TestProgress testProgress, Long firstTestProDetailId) {
        // PBI中第1阶段存储天数为0时才生成待办任务，不为0时只需要在定时任务中生成待办任务即可
        if (testProgress.getData().get(0).getDay() == 0) {
            TestProjectTodoTask todoTask = new TestProjectTodoTask();
            todoTask.setFolderNo(testProgress.getTestCode());
            todoTask.setWtrName(testProgress.getApplicant());
            todoTask.setTaskStatus("待处理");
            todoTask.setTester(testProgress.getTestMan());
            todoTask.setTesterCode(testProgress.getTestManId());
            // 待办任务的计划开始日期是分配该任务时的日期，计划结束日期是进箱开始日期
            todoTask.setPlanStartTime(clearTimeByDate(new Date()));
            todoTask.setPlanEndTime(clearTimeByDate(testProgress.getData().get(0).getInDate()));
            todoTask.setTestName("日历寿命");
            todoTask.setTaskType("rlsmcs_first");
            todoTask.setOrdTaskId(firstTestProDetailId);
            testProjectTodoTaskService.save(todoTask);
        }
    }

    // 任务分配提交校验
    public ResponseData validTaskAssign(List<TLimsOrdtask> ordtaskBeanList) {
        if (CollectionUtils.isEmpty(ordtaskBeanList)) {
            return ResponseData.error("数据不能为空");
        }
        Map<Long, String> ordtaskStatusMap = new HashMap<>();
        List<Long> ordtaskIdList = ordtaskBeanList.stream().map(o -> {
            Long ordtaskId = o.getId();
            ordtaskStatusMap.put(ordtaskId, o.getStatus());
            return ordtaskId;
        }).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<TLimsOrdtask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TLimsOrdtask::getId, ordtaskIdList);
        ordtaskBeanList = this.list(queryWrapper);
        for (TLimsOrdtask ordtaskBean : ordtaskBeanList) {
            String status = ordtaskStatusMap.get(ordtaskBean.getId());// 界面状态
            if (StringUtils.isEmpty(status)) {
                continue;
            }
            String dbStatus = ordtaskBean.getStatus();// 数据库状态
            if (!StringUtils.equals(status, "assignPlanStartDate") && !StringUtils.equals(status, dbStatus)) {
                return ResponseData.error("所选数据已被操作,请先刷新界面再操作");
            }
        }
        List<Long> folderIdList = ordtaskBeanList.stream().map(TLimsOrdtask::getFolderid).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<TLimsOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
        orderQueryWrapper.in(TLimsOrder::getFolderid, folderIdList);
        List<TLimsOrder> orderBeanList = tLimsOrderService.list(orderQueryWrapper);
        for (TLimsOrdtask ordtaskBean : ordtaskBeanList) {
            List<TLimsOrder> orderBeans = orderBeanList.stream()
                    .filter(o -> ObjectUtils.equals(Long.parseLong(o.getFolderid().toString()), ordtaskBean.getFolderid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderBeans)) {
                continue;
            }

            boolean orderReceiveFlag = orderBeans.stream().anyMatch(o -> StringUtils.equals("0", o.getReceiveflag()));
            if (orderReceiveFlag) {
                return ResponseData.error("委托单:" + ordtaskBean.getFolderno() + "<br>样品全部接收后才能分配人,请检查");
            }
        }
        List<TLimsFolder> folderList = tLimsFolderService.listByIds(folderIdList);
        // 第六实验室日历寿命:判断数据是否从lims同步到pbi了
        folderList = folderList.stream().filter(o -> StringUtils.isNotEmpty(o.getTestarea()) && o.getTestarea().contains("日历寿命测试")).collect(Collectors.toList());
        // 第四实验室日历寿命:判断数据是否从lims同步到pbi了
        boolean aqCalendarFlag = folderList.stream().allMatch(o -> StringUtils.equals(o.getLaboratoryid(), "HZ_YJ_DL_AQ")) &&
                ordtaskBeanList.stream().allMatch(o -> StringUtils.equals(o.getFirstcode(), "SA") && StringUtils.equals(o.getSecondcode(), "STORAGE"));
        if (folderList.size() > 0 || aqCalendarFlag) {
            LambdaQueryWrapper<TestProgress> query = new LambdaQueryWrapper<>();
            query.in(TestProgress::getOrdtaskid, ordtaskIdList);
            query.eq(TestProgress::getDeleteStatus, 0);
            List<TestProgress> testProgressList = testProgressService.list(query);
            for (TLimsOrdtask item: ordtaskBeanList) {
                if (testProgressList.stream().noneMatch(o -> Objects.equals(o.getOrdtaskid(), item.getId()))) {
                    return ResponseData.error("委托单【"+ item.getFolderno() + "】下的测试任务【" + item.getAlias() + "】在PBI没有数据,无法进行任务分配!");
                }
            }
        }
        return ResponseData.success();
    }

    /**
     * ORDTASK_STATUS_DRAFT = "Draft";//新建
     * ORDTASK_STATUS_PRESCHEDULE = "Preschedule";//待排程
     * ORDTASK_STATUS_ASSIGN = "Assign";//任务分配
     * ORDTASK_STATUS_RESULT = "Result";//结果录入
     * ORDTASK_STATUS_REVIEW = "Review";//结果复核
     * ORDTASK_STATUS_REPORT = "Report";//报告编制
     * ORDTASK_STATUS_DONE = "Done";//完成
     */
    private MainProcessDTO getOrdtaskNextStatus(String type, TLimsOrdtask ordtaskBean, String status) {
        MainProcessDTO dto = new MainProcessDTO();

        String nextStatus = null;//下一个状态
        String cruTaskName = null;//当前节点
        String nextTaskName = null;//下一个节点
        String nextMenuId = null;//下个节点菜单ID
        String reportFlag = "0";//是否需要报告
        String ordtaskReportFlag = ordtaskBean.getOrdtaskreportflag();//测试项目是否需要报告
        String originalFlag = ordtaskBean.getOriginalflag();//草本 or 正本

        List<String> resultList = new ArrayList<>();
        resultList.add(MainProcessConstant.ORDTASK_STATUS_RESULT);
        resultList.add(MainProcessConstant.ORDTASK_STATUS_RESULT_RETURN);
        boolean resultFlag = resultList.stream().anyMatch(o -> o.equals(status));

        if (MainProcessConstant.ORDTASK_STATUS_OUTSOURCE.equals(status)) {//任务委外提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_OUTTESTASSIGN;//委外测试任务分配
            cruTaskName = MainProcessConstant.INTERFACE_OUTSOURCE;
            nextTaskName = MainProcessConstant.INTERFACE_OUTTESTASSIGN;
            nextMenuId = MainProcessConstant.INTERFACE_ORDTASK_OUTTESTASSIGN_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_OUTTESTASSIGN.equals(status)) {//委外测试任务分配提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_BACKFILL;//数据回填
            cruTaskName = MainProcessConstant.INTERFACE_OUTTESTASSIGN;
            nextTaskName = MainProcessConstant.INTERFACE_BACKFILL;
            nextMenuId = MainProcessConstant.INTERFACE_ORDTASK_BACKFILL_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_BACKFILL.equals(status)) {//数据回填
            nextStatus = MainProcessConstant.ORDTASK_STATUS_DONE;//完成
            cruTaskName = MainProcessConstant.INTERFACE_BACKFILL;
            nextTaskName = MainProcessConstant.ALL_DONE;
        } else if (MainProcessConstant.ORDTASK_STATUS_CFMSCHEDULE.equals(status)) {//排程确认提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_TESTASSIGN;//测试任务分配
            cruTaskName = MainProcessConstant.INTERFACE_CFMSCHEDULE;
            nextTaskName = MainProcessConstant.INTERFACE_TESTASSIGN;
            nextMenuId = MainProcessConstant.INTERFACE_TESTASSIGN_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_TESTASSIGN.equals(status)) {//测试任务分配提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_ASSIGN;//任务分配
            cruTaskName = MainProcessConstant.INTERFACE_TESTASSIGN;
            nextTaskName = MainProcessConstant.INTERFACE_ASSIGN;
            nextMenuId = MainProcessConstant.INTERFACE_ASSIGN_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_ASSIGN.equals(status)) {//任务分配提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_RESULT;//结果录入
            cruTaskName = MainProcessConstant.INTERFACE_ASSIGN;
            nextTaskName = MainProcessConstant.INTERFACE_RESULT;
            nextMenuId = MainProcessConstant.INTERFACE_RESULT_MENUID;
        } else if (resultFlag) {//结果录入
            nextStatus = MainProcessConstant.ORDTASK_STATUS_REVIEW;//结果复核
            cruTaskName = MainProcessConstant.INTERFACE_RESULT;
            nextTaskName = MainProcessConstant.INTERFACE_REVIEW;
            nextMenuId = MainProcessConstant.INTERFACE_REVIEW_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_EXAMINE.equals(status)) {//结果审核提交
            nextStatus = MainProcessConstant.ORDTASK_STATUS_REVIEW;//结果复核
            cruTaskName = MainProcessConstant.INTERFACE_EXAMINE;
            nextTaskName = MainProcessConstant.INTERFACE_REVIEW;
            nextMenuId = MainProcessConstant.INTERFACE_REVIEW_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_REVIEW.equals(status)) {//结果复核
            if (!StringUtils.isEmpty(reportFlag) && "1".equals(reportFlag)) {
                nextStatus = MainProcessConstant.ORDTASK_STATUS_REPORT;//报告编制
                cruTaskName = MainProcessConstant.INTERFACE_REVIEW;
                nextTaskName = MainProcessConstant.INTERFACE_REPORT_DRAFT;
                nextMenuId = MainProcessConstant.INTERFACE_REPORT_DRAFT_MENUID;

                //测试项目不需要报告,直接DONE
                if (StringUtils.equals("0", ordtaskReportFlag)) {
                    nextStatus = MainProcessConstant.ORDTASK_STATUS_DONE;//完成
                    cruTaskName = MainProcessConstant.INTERFACE_REVIEW;
                    nextTaskName = MainProcessConstant.ALL_DONE;
                    nextMenuId = null;
                }
            } else {//不需要报告
                nextStatus = MainProcessConstant.ORDTASK_STATUS_DONE;//完成
                cruTaskName = MainProcessConstant.INTERFACE_REVIEW;
                nextTaskName = MainProcessConstant.ALL_DONE;
            }
        } else if (MainProcessConstant.ORDTASK_STATUS_REPORT.equals(status)) {//报告编制
            nextStatus = MainProcessConstant.ORDTASK_STATUS_DONE;//完成
            cruTaskName = MainProcessConstant.INTERFACE_REPORT_DRAFT;
            nextTaskName = MainProcessConstant.ALL_DONE;
        } else if (MainProcessConstant.ORDTASK_TESTREPORT_LIST.contains(status)) {//测试报告上传
            if ("herbaceous".equals(originalFlag)) {//草本
                nextStatus = MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAPPROVE;//草本审核
                cruTaskName = MainProcessConstant.INTERFACE_TESTREPORT;
                nextTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAPPROVE;
                nextMenuId = MainProcessConstant.INTERFACE_HERBACEOUSAPPROVE_MENUID;
            } else {//正本
                nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALAPPROVE;//正本审核
                cruTaskName = MainProcessConstant.INTERFACE_TESTREPORT;
                nextTaskName = MainProcessConstant.INTERFACE_ORIGINALAPPROVE;
                nextMenuId = MainProcessConstant.INTERFACE_ORIGINALAPPROVE_MENUID;
            }
        } else if (MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAPPROVE.equals(status)) {//草本审核
            nextStatus = MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAUDIT;//草本审批
            cruTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAPPROVE;
            nextTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAUDIT;
            nextMenuId = MainProcessConstant.INTERFACE_HERBACEOUSAUDIT_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAUDIT.equals(status)) {//草本审批
            nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALUPLOAD;//正本上传
            cruTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAUDIT;
            nextTaskName = MainProcessConstant.INTERFACE_ORIGINALUPLOAD;
            nextMenuId = MainProcessConstant.INTERFACE_ORIGINALUPLOAD_MENUID;
        } else if (MainProcessConstant.ORDTASK_ORIGINALUPLOAD_LIST.contains(status)) {//正本上传
            nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALAPPROVE;//正本审核
            cruTaskName = MainProcessConstant.INTERFACE_ORIGINALUPLOAD;
            nextTaskName = MainProcessConstant.INTERFACE_ORIGINALAPPROVE;
            nextMenuId = MainProcessConstant.INTERFACE_ORIGINALAPPROVE_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_ORIGINALAPPROVE.equals(status)) {//正本审核
            nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALAUDIT;//正本审批
            cruTaskName = MainProcessConstant.INTERFACE_ORIGINALAPPROVE;
            nextTaskName = MainProcessConstant.INTERFACE_ORIGINALAUDIT;
            nextMenuId = MainProcessConstant.INTERFACE_ORIGINALAUDIT_MENUID;
        } else if (MainProcessConstant.ORDTASK_STATUS_ORIGINALAUDIT.equals(status)) {//正本审批
            nextStatus = MainProcessConstant.ORDTASK_STATUS_DONE;//完成
            cruTaskName = MainProcessConstant.INTERFACE_ORIGINALAUDIT;
            nextTaskName = MainProcessConstant.ALL_DONE;
        }

        if ("reject".equals(type)) {//退回, 结果复核退回到结果录入
            nextStatus = MainProcessConstant.ORDTASK_STATUS_RESULT_RETURN;//退回
            cruTaskName = MainProcessConstant.INTERFACE_REVIEW;
            nextTaskName = MainProcessConstant.INTERFACE_RESULT;
            nextMenuId = MainProcessConstant.INTERFACE_RESULT_MENUID;

            if (MainProcessConstant.ORDTASK_STATUS_EXAMINE.equals(status)) {// 结果审核退回
                cruTaskName = MainProcessConstant.INTERFACE_EXAMINE;
            }
        }

        if ("dldcWwReject".equals(type)) {//退回, 动力电池委外这边的退回
            if (MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAPPROVE.equals(status)) {//草本审核
                nextStatus = MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAPPROVEREJECT;
                cruTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAPPROVE;
                nextTaskName = MainProcessConstant.INTERFACE_TESTREPORT;
                nextMenuId = MainProcessConstant.INTERFACE_TESTREPORT_MENUID;
            } else if (MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAUDIT.equals(status)) {//草本审批
                nextStatus = MainProcessConstant.ORDTASK_STATUS_HERBACEOUSAUDITREJECT;
                cruTaskName = MainProcessConstant.INTERFACE_HERBACEOUSAUDIT;
                nextTaskName = MainProcessConstant.INTERFACE_TESTREPORT;
                nextMenuId = MainProcessConstant.INTERFACE_TESTREPORT_MENUID;
            } else if (MainProcessConstant.ORDTASK_STATUS_ORIGINALAPPROVE.equals(status)) {//正本审核
                nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALAPPROVEREJECT;
                cruTaskName = MainProcessConstant.INTERFACE_ORIGINALAPPROVE;
                nextTaskName = MainProcessConstant.INTERFACE_ORIGINALUPLOAD;
                nextMenuId = MainProcessConstant.INTERFACE_ORIGINALUPLOAD_MENUID;
            } else if (MainProcessConstant.ORDTASK_STATUS_ORIGINALAUDIT.equals(status)) {//正本审批
                nextStatus = MainProcessConstant.ORDTASK_STATUS_ORIGINALAUDITREJECT;
                cruTaskName = MainProcessConstant.INTERFACE_ORIGINALAUDIT;
                nextTaskName = MainProcessConstant.INTERFACE_ORIGINALUPLOAD;
                nextMenuId = MainProcessConstant.INTERFACE_ORIGINALUPLOAD_MENUID;
            }
        }

        dto.setCruStatus(status);
        dto.setNextStatus(nextStatus);
        dto.setCruTaskName(cruTaskName);
        dto.setNextTaskName(nextTaskName);
        dto.setNextMenuId(nextMenuId);

        return dto;
    }

    public static void setFolderTodoPerson(List<TLimsFolder> folderBeanList, List<TLimsOrdtask> ordtaskBeanList, List<TLimsReport> reportBeanList) {
        for (TLimsFolder folderBean : folderBeanList) {
            Set<String> setAll = new HashSet<>();
            if (!CollectionUtils.isEmpty(ordtaskBeanList)) {
                List<TLimsOrdtask> ordtaskBeans = ordtaskBeanList.stream()
                        .filter(o -> ObjectUtils.equals(o.getFolderid(), folderBean.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(ordtaskBeans)) {
                    ordtaskBeans.forEach(o -> {
                        String todoPerson = o.getTodoperson();
                        if (!StringUtils.isEmpty(todoPerson)) {
                            Set<String> set = Arrays.stream(todoPerson.split(",")).collect(Collectors.toSet());
                            setAll.addAll(set);
                        }
                    });
                }
            }

            if (!CollectionUtils.isEmpty(reportBeanList)) {
                List<TLimsReport> reportBeans = reportBeanList.stream()
                        .filter(o -> ObjectUtils.equals(o.getFolderid(), folderBean.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(reportBeans)) {
                    reportBeans.forEach(o -> {
                        String todoPerson = o.getTodoperson();
                        if (!StringUtils.isEmpty(todoPerson)) {
                            Set<String> set = Arrays.stream(todoPerson.split(",")).collect(Collectors.toSet());
                            setAll.addAll(set);
                        }
                    });
                }
            }

            if (!CollectionUtils.isEmpty(setAll)) {
                String todoPerson = StringUtils.join(setAll, ",");
                folderBean.setTodoperson(todoPerson);
            }
        }
    }
}
