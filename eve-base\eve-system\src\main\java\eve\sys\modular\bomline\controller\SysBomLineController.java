package eve.sys.modular.bomline.controller;


import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@RestController
public class SysBomLineController {

    @Resource 
    private ISysBomLineService bomLineService;
    
    @PostMapping("/sysBomLine/addlines")
    public ResponseData addLines(@RequestBody SysBomLineParam param) {
        bomLineService.addWerks(param);
        return new SuccessResponseData();
    }

    @PostMapping("/sysBomLine/addmutillines")
    public ResponseData addMultiWerks(@RequestBody SysBomLineParam param) {
        bomLineService.addMultiWerks(param);
        return new SuccessResponseData();
    }

    @PostMapping("/sysBomLine/setlines")
    public ResponseData setWerks(@RequestBody SysBomLineParam param) {
        bomLineService.setWerks(param);
        return new SuccessResponseData();
    }

    @PostMapping("/sysBomLine/deletelines")
    public ResponseData deleteLines(@RequestBody SysBomLineParam param) {
        bomLineService.deleteWerks(param);
        return new SuccessResponseData();
    }
}

