package eve.sys.modular.ai.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.sys.modular.aiQuestion.entity.AiQuestion;
import eve.sys.modular.aiQuestion.service.IAiQuestionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
public class ChatController {

    @Resource
    private IAiQuestionService questionService;

    @GetMapping("/open/chat")
    public Flux<String> chat(@RequestParam String question,@RequestParam(required = false) Long questionId,@RequestParam(required = false) String model) throws Exception {
        JSONArray objects = new JSONArray();

        if(null != questionId){
            AiQuestion question1 = this.questionService.getById(questionId);
            if(null != question1){
//                objects.put(new JSONObject().put("role", "system").put("content", question1.getResult()));
                if(StrUtil.isNotBlank(question1.getResult())){
                    question += ",请用中文回答";
                }
            }
            List<AiQuestion> list = this.questionService.list(AiQuestion.builder().questionId(questionId).build());
            for (int i = 0; i < list.size(); i++) {
                AiQuestion l = list.get(i);
                if(i == 0 && null != question1 && StrUtil.isNotBlank(question1.getResult())){
                    objects.put(new JSONObject().put("role", "user").put("content", "背景数据"+question1.getResult()+"。" +l.getQuestion()));
                }else{
                    objects.put(new JSONObject().put("role", "user").put("content", l.getQuestion()));
                }


                objects.put(new JSONObject().put("role", "assistant").put("content", l.getResult()));
            }
            list.forEach(l -> {
                objects.put(new JSONObject().put("role", "user").put("content", l.getQuestion()));
                objects.put(new JSONObject().put("role", "assistant").put("c    ontent", l.getResult()));
            });
        }
        AiQuestion build = AiQuestion.builder().questionId(questionId).question(question).build();
        this.questionService.save(build);

        objects.put(new JSONObject().put("role", "user").put("content",question ));

        Sinks.Many<String> sink = Sinks.many().multicast().onBackpressureBuffer();
        Flux<String> flux = sink.asFlux();

        new Thread(() -> {
            try {

                String apiUrl = "http://172.30.1.100:11434/v1/chat/completions";
                HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();

                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);



                JSONObject data = new JSONObject()
                        .put("messages", objects)
                        .put("temperature", 0.3)
                        .put("top_p", 0.95)
                        .put("max_tokens", 512)
                        .put("model", StrUtil.isNotBlank(model)?model:"qwen2:7b")
                        .put("stream", true)
                        .put("n", 1)
                        .put("best_of", 1)
                        .put("presence_penalty", 1.2)
                        .put("frequency_penalty", 0.2)
                        .put("top_k", 50)
                        .put("use_beam_search", false)
                        .put("stop", new JSONArray())
                        .put("ignore_eos", false)
                        .put("logprobs", JSONObject.DEFAULT_CAPACITY)
                        .put("options",new JSONObject().put("num_ctx",40960));


                try (OutputStream os = connection.getOutputStream()) {
                    os.write(data.toString().getBytes(StandardCharsets.UTF_8));
                }

                String resultMsg = "";
                try (BufferedReader in = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = in.readLine()) != null) {
                        line = line.trim();
                        if (line.isEmpty() || line.equals("[DONE]") || !line.startsWith("data: ")) {
                            continue;
                        }
                        line = line.substring(6).trim();

                        try {
                            JSONObject result = new JSONObject(line);
                            JSONArray choices = result.getJSONArray("choices");
                            JSONObject delta = choices.getJSONObject(0).getJSONObject("delta");
                            String content = delta.getStr("content");
                            resultMsg += content;
                            sink.tryEmitNext(content); // 发送数据
                        } catch (JSONException e) {
                            // 处理异常
                        }
                    }
                    sink.tryEmitComplete(); // 完成流
                }finally {
                    this.questionService.updateById(AiQuestion.builder().id(build.getId()).result(resultMsg).build());
                }
            } catch (Exception e) {
                sink.tryEmitError(e); // 发送错误
            }
        }).start();

        return flux;
    }


    @GetMapping("/open/checkHealth")
    @BusinessLog(title = "校验后端系统是否正常", opType = LogAnnotionOpTypeEnum.QUERY)
    public Boolean checkHealth() {
       return true;
    }
}