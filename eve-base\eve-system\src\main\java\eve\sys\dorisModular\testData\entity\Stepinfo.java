package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sync_data.stepinfo")
public class Stepinfo implements Serializable {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 工步号
     */
    private Long stepId;

    /**
     * 工步名称
     */
    private String stepName;

    /**
     * 数据关联id
     */
    private String flowId;

    /**
     * 数据生成时间
     */
    private String absoluteTime;

    /**
     * 参数
     */
    private String stepPara;

    /**
     * 截止参数
     */
    private String cutoffCondition;

    /**
     * 记录条件
     */
    private String recordCondition;

    /**
     * 设备编号
     */
    private String unitNum;


    /**
     * 通道id
     */
    private String channelId;

    /**
     * 测试编码
     */
    private String barcode;

    /**
     * 同步doris时间
     */
    private Date syncDate;

    /**
     * 数据上传时间
     */
    private String createTime;


}
