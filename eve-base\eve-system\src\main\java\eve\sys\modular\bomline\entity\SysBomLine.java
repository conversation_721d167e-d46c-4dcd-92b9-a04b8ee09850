package eve.sys.modular.bomline.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import eve.core.pojo.base.entity.BaseVo;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Data
@TableName("SYS_BOM_LINE")
public class SysBomLine  extends BaseVo {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long bomId;

    private Long lineId;

    private Long bomIssueId;

    private int bomType;
}
