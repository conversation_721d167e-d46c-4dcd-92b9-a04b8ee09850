package eve.sys.modular.bookcorner.controller;


import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bookcorner.entity.BookCorner;
import eve.sys.modular.bookcorner.service.IBookCornerService;


/**
 * <p>
 * 图书角 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@RequestMapping("/bookCorner")
public class BookCornerController {
    @Resource
    private IBookCornerService bookCornerService;

    @PostMapping("/page")
    public ResponseData page(@RequestBody BookCorner param) {
        return new SuccessResponseData(bookCornerService.page(param));
    }

    @PostMapping("/list")
    public ResponseData list(@RequestBody BookCorner param) {
        return new SuccessResponseData(bookCornerService.getList(param));
    }

    @GetMapping("/getById")
    public ResponseData getById(BookCorner param) {
        return new SuccessResponseData(bookCornerService.getById(param));
    }

    @PostMapping("/delete")
    @BusinessLog(title = "图书_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody BookCorner param) {
        bookCornerService.delete(param);
        return new SuccessResponseData();
    }

    @PostMapping("/add")
    @BusinessLog(title = "图书_添加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody BookCorner param) {
        bookCornerService.add(param);
        return new SuccessResponseData();
    }

    @PostMapping("/edit")
    @BusinessLog(title = "图书_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody BookCorner param) {
        bookCornerService.edit(param);
        return new SuccessResponseData();
    }
}

