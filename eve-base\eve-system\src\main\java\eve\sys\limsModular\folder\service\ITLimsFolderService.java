package eve.sys.limsModular.folder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.limsModular.folder.entity.TLimsFolder;

import java.util.List;

/**
 * <p>
 * 委托单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface ITLimsFolderService extends IService<TLimsFolder> {

    PageResult<TLimsFolder> listPage(TLimsFolder param);

    List<TLimsFolder> getByFolderNo(TLimsFolder param);
}
