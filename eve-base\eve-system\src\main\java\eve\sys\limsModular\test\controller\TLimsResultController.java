package eve.sys.limsModular.test.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.limsModular.test.entity.TLimsResult;
import eve.sys.limsModular.test.service.impl.TLimsResultServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 检测结果 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@RestController
@RequestMapping("/tLimsResult")
public class TLimsResultController {

    @Autowired
    TLimsResultServiceImpl tLimsResultService;

    @PostMapping("/getLimsResultByOrdTaskId")
    @BusinessLog(title = "工作台-获取详情", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLimsResultByOrdTaskId(@RequestBody TLimsResult param) {
        return new SuccessResponseData(tLimsResultService.getLimsResultByOrdTaskId(param));
    }

    @PostMapping("/updateLimsResultByOrdTaskId")
    @BusinessLog(title = "工作台-录入数据", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData updateLimsResultByOrdTaskId(@RequestBody TLimsResult param) {
        tLimsResultService.updateLimsResultByOrdTaskId(param);
        return new SuccessResponseData();
    }
}

