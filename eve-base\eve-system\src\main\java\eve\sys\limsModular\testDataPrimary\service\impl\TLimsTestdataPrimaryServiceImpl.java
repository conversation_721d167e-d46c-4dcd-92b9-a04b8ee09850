package eve.sys.limsModular.testDataPrimary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.context.login.LoginContextHolder;
import eve.core.pojo.login.SysLoginUser;
import eve.core.pojo.page.PageResult;
import eve.sys.limsModular.coreRoleUser.entity.TCoreRoleUser;
import eve.sys.limsModular.coreRoleUser.service.ITCoreRoleUserService;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import eve.sys.limsModular.ordTaskCondition.entity.TLimsOrdtaskCondition;
import eve.sys.limsModular.ordTaskCondition.service.ITLimsOrdtaskConditionService;
import eve.sys.limsModular.order.entity.TLimsOrder;
import eve.sys.limsModular.order.service.ITLimsOrderService;
import eve.sys.limsModular.roleDimension.entity.TLimsRoleDimension;
import eve.sys.limsModular.roleDimension.service.ITLimsRoleDimensionService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.mapper.TLimsTestdataPrimaryMapper;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.limsModular.testMatrix.entity.TLimsTestmatrix;
import eve.sys.limsModular.testMatrix.service.ITLimsTestmatrixService;
import eve.sys.modular.test.testProgress.entity.TestProgress;
import eve.sys.modular.test.testProgress.service.ITestProgressService;
import eve.sys.modular.test.testProgressDetail.entity.TestProgressDetail;
import eve.sys.modular.test.testProgressDetail.service.ITestProgressDetailService;
import eve.sys.mongoDbModular.chroma.bean.ChromaTestData;
import eve.sys.mongoDbModular.chroma.service.ChromaService;
import eve.sys.mongoDbModular.landian.bean.LandianFlowInfo;
import eve.sys.mongoDbModular.landian.service.LandianService;
import eve.sys.mongoDbModular.ruineng.bean.RuinengFlowInfo;
import eve.sys.mongoDbModular.ruineng.service.RuinengService;
import eve.sys.mongoDbModular.shenghong.bean.FlowInfo;
import eve.sys.mongoDbModular.shenghong.service.ShenghongService;
import eve.sys.mongoDbModular.xinwei.bean.XinWeiStepInfo;
import eve.sys.mongoDbModular.xinwei.service.XinWeiService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Collation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 测试数据关联mongodb主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Service
public class TLimsTestdataPrimaryServiceImpl extends ServiceImpl<TLimsTestdataPrimaryMapper, TLimsTestdataPrimary> implements ITLimsTestdataPrimaryService {

    @Resource
    private ITCoreRoleUserService roleUserService;

    @Resource
    private ITLimsRoleDimensionService roleDimensionService;

    @Resource
    private ITLimsTestmatrixService testmatrixService;

    @Resource
    private ITLimsFolderService folderService;

    @Resource
    private ITLimsOrderService orderService;

    @Resource
    private ITLimsOrdtaskService ordtaskService;

    @Resource
    private ShenghongService shenghongService;

    @Resource
    private XinWeiService xinWeiService;

    @Resource(name = "shengHongMongoTemplate")
    private MongoTemplate shengHongMongoTemplate;

    @Resource(name = "xinWeiMongoTemplate")
    private MongoTemplate xinWeiMongoTemplate;

    @Resource(name = "chromaMongoTemplate")
    private MongoTemplate chromaMongoTemplate;

    @Resource(name = "landianMongoTemplate")
    private MongoTemplate landianMongoTemplate;

    @Resource(name = "ruinengMongoTemplate")
    private MongoTemplate ruinengMongoTemplate;

    @Resource
    private ChromaService chromaService;

    @Resource
    private LandianService landianService;

    @Resource
    private RuinengService ruinengService;

    @Resource
    private ITLimsOrdtaskConditionService ordtaskConditionService;

    @Resource
    private ITestProgressService progressService;

    @Resource
    private ITestProgressDetailService progressDetailService;

    @Override
    public List<TLimsTestdataPrimary> list(TLimsTestdataPrimary param) {

        LambdaQueryWrapper<TLimsTestdataPrimary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
        if(null != param.getFolderid()){
            queryWrapper.eq(TLimsTestdataPrimary::getFolderid,param.getFolderid());
        }
        if(null != param.getOrdtaskid()){
            queryWrapper.eq(TLimsTestdataPrimary::getOrdtaskid,param.getOrdtaskid());
        }
        if(StrUtil.isNotBlank(param.getAlias())){
            queryWrapper.eq(TLimsTestdataPrimary::getAlias,param.getAlias());
        }
        if(StrUtil.isNotBlank(param.getCelltestcode())){
            queryWrapper.eq(TLimsTestdataPrimary::getCelltestcode,param.getCelltestcode());
        }
        queryWrapper.isNotNull(TLimsTestdataPrimary::getStartTime);

        queryWrapper.orderByDesc(TLimsTestdataPrimary::getFolderno);
        queryWrapper.orderByAsc(TLimsTestdataPrimary::getOrderno);
        queryWrapper.orderByAsc(TLimsTestdataPrimary::getStartTime);
        List<TLimsTestdataPrimary> list = list(queryWrapper);
        list.forEach(l -> l.setUuid(l.getId()));

        List<TLimsTestdataPrimary> resultList = new ArrayList<>();
        Map<String, List<TLimsTestdataPrimary>> testCodeMap = list.stream().collect(Collectors.groupingBy(TLimsTestdataPrimary::getOrderno
                ,LinkedHashMap::new, Collectors.toList()));
        for (String key:testCodeMap.keySet()) {
            List<TLimsTestdataPrimary> testdataPrimaries = testCodeMap.get(key);
            testdataPrimaries = testdataPrimaries.stream().sorted(Comparator.comparing(TLimsTestdataPrimary::getOrderno))
                    .sorted(Comparator.comparing(TLimsTestdataPrimary::getStartTime)).collect(Collectors.toList());
            TLimsTestdataPrimary testdataPrimary = testdataPrimaries.get(0);
            List<TLimsTestdataPrimary> children = new ArrayList<>();
            for (int i = 1; i < testdataPrimaries.size(); i++) {
                testdataPrimaries.get(i).setIsChild(true);
                testdataPrimaries.get(i).setUuid(testdataPrimaries.get(i).getId());
                children.add(testdataPrimaries.get(i));
            }
            testdataPrimary.setChildren(children.size()>0?children:null);
            resultList.add(testdataPrimary);
        }

        return resultList;
    }

    @Override
    public PageResult<TLimsTestdataPrimary> pageList(TLimsTestdataPrimary param) {
        List<String> loginUserRoleIds = LoginContextHolder.me().getLoginUserRoleIds();
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        LambdaQueryWrapper<TLimsTestdataPrimary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
        queryWrapper.select(TLimsTestdataPrimary::getFolderno,TLimsTestdataPrimary::getCelltestcode,TLimsTestdataPrimary::getAlias,TLimsTestdataPrimary::getOrderno);

        //权限过滤 管理员或领导角色
        if(!"superAdmin".equals(sysLoginUser.getAccount()) && !loginUserRoleIds.contains("1694647012972855298")){
            //查询角色
            LambdaQueryWrapper<TCoreRoleUser> roleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            roleUserLambdaQueryWrapper.eq(TCoreRoleUser::getUserid,sysLoginUser.getAccount());
            List<TCoreRoleUser> roleUserList = this.roleUserService.list(roleUserLambdaQueryWrapper);

            //是否有委托单查询权限
            Boolean haveQuery = false;
            List<TCoreRoleUser> adminList = roleUserList.stream().filter(r -> r.getRoleid() == 1).collect(Collectors.toList());
            if(adminList.size() > 0){
                haveQuery = true;
            }

            if(roleUserList.size() > 0) {
                List<Long> roleIds = roleUserList.stream().map(TCoreRoleUser::getRoleid).collect(Collectors.toList());
                LambdaQueryWrapper<TLimsRoleDimension> dimensionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                dimensionLambdaQueryWrapper.in(TLimsRoleDimension::getRoleid, roleIds);
                dimensionLambdaQueryWrapper.eq(TLimsRoleDimension::getMenuid, 20210825002L);
                List<TLimsRoleDimension> dimensionList = this.roleDimensionService.list(dimensionLambdaQueryWrapper);
                //角色过滤
                if (dimensionList.size() > 0) {
                    List<String> dimensionTypes = dimensionList.stream().map(TLimsRoleDimension::getDimensiontype).distinct().collect(Collectors.toList());
                    queryWrapper.and((wrapper) -> {
                        wrapper.exists("SELECT 1 FROM T_LIMS_CIRCULATE C WHERE T_LIMS_TESTDATA_PRIMARY.FOLDERID = C.FOLDERID AND C.CIRCULATEID = '" + sysLoginUser.getAccount() + "'")
                                .or().exists("SELECT 1 FROM T_LIMS_USERINFO_LEADER D WHERE D.USERACCOUNT = T_LIMS_TESTDATA_PRIMARY.CREATEDBYID AND D.LEADERACCOUNT = '" + sysLoginUser.getAccount() + "'")
                                .or().eq(TLimsTestdataPrimary::getCreatedbyid, sysLoginUser.getAccount());
                        dimensionTypes.forEach(d -> {
                            if ("clientLeaderRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_USER_UNIT_AUTHORITY UA WHERE UA.USERID = '" + sysLoginUser.getAccount() + "'AND UA.ORGID = T_LIMS_TESTDATA_PRIMARY.CREATEDBYORGID");
                            }
                            if ("labUserRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_USER_UNIT_AUTHORITY UA WHERE UA.USERID = '" + sysLoginUser.getAccount() + "'AND UA.ORGID = T_LIMS_TESTDATA_PRIMARY.LABORATORYID");
                            }
                            if ("testerRange".equals(d)) {
                                wrapper.or().exists("SELECT 1 FROM T_LIMS_ORDTASK O WHERE T_LIMS_TESTDATA_PRIMARY.FOLDERID = O.FOLDERID AND O.TESTERCODE  = '" + sysLoginUser.getAccount() + "'");
                            }
                            if ("productManagerRange".equals(d)) {
                                wrapper.or().eq(TLimsTestdataPrimary::getProjectleaderid, sysLoginUser.getAccount());
                            }

                        });
                    });
                } //只看自己
                else if(!haveQuery){
                    queryWrapper.and((wrapper) -> {
                        wrapper.exists("SELECT 1 FROM T_LIMS_CIRCULATE C WHERE T_LIMS_TESTDATA_PRIMARY.FOLDERID = C.FOLDERID AND C.CIRCULATEID = '" + sysLoginUser.getAccount() + "'")
                                .or().exists("SELECT 1 FROM T_LIMS_USERINFO_LEADER D WHERE D.USERACCOUNT = T_LIMS_TESTDATA_PRIMARY.CREATEDBYID AND D.LEADERACCOUNT = '" + sysLoginUser.getAccount() + "'")
                                .or().eq(TLimsTestdataPrimary::getCreatedbyid, sysLoginUser.getAccount());
                    });
                }
            }
        }


        if(StrUtil.isNotBlank(param.getFolderno())){
            queryWrapper.like(TLimsTestdataPrimary::getFolderno,param.getFolderno());
        }
        if(StrUtil.isNotBlank(param.getTestcode())){
            queryWrapper.like(TLimsTestdataPrimary::getTestcode,param.getTestcode());
        }
        if(StrUtil.isNotBlank(param.getTestname())){
            queryWrapper.like(TLimsTestdataPrimary::getTestname,param.getTestname());
        }
        if(StrUtil.isNotBlank(param.getAlias())){
            queryWrapper.like(TLimsTestdataPrimary::getAlias,param.getAlias());
        }

        if(StrUtil.isNotBlank(param.getOrderno())){
            queryWrapper.like(TLimsTestdataPrimary::getOrderno,param.getOrderno());
        }

        if(StrUtil.isNotBlank(param.getCelltestcode())){
            queryWrapper.like(TLimsTestdataPrimary::getCelltestcode,param.getCelltestcode());
        }
        if(StrUtil.isNotBlank(param.getTheme())){
            queryWrapper.like(TLimsTestdataPrimary::getTheme,param.getTheme());
        }
        if(StrUtil.isNotBlank(param.getDay())){
            queryWrapper.eq(TLimsTestdataPrimary::getDay,param.getDay());
        }

        queryWrapper.groupBy(TLimsTestdataPrimary::getFolderno,TLimsTestdataPrimary::getCelltestcode,TLimsTestdataPrimary::getAlias,TLimsTestdataPrimary::getOrderno);

        queryWrapper.orderByDesc(TLimsTestdataPrimary::getFolderno);
        queryWrapper.orderByAsc(TLimsTestdataPrimary::getOrderno);
        //queryWrapper.orderByAsc(TLimsTestdataPrimary::getStarttime);

        Page<TLimsTestdataPrimary> page = this.page(new Page<>(param.getPageNo().longValue(), param.getPageSize().longValue()), queryWrapper);

        List<TLimsTestdataPrimary> records = page.getRecords();
        List<TLimsTestdataPrimary> newRecords = new ArrayList<>();
        records.forEach(r -> {
            LambdaQueryWrapper<TLimsTestdataPrimary> childQueryWrapper = new LambdaQueryWrapper<>();
            childQueryWrapper.eq(TLimsTestdataPrimary::getCelltestcode,r.getCelltestcode());
            childQueryWrapper.eq(TLimsTestdataPrimary::getAlias,r.getAlias());
            if(StrUtil.isNotBlank(param.getDay())){
                childQueryWrapper.eq(TLimsTestdataPrimary::getDay,param.getDay());
            }
            childQueryWrapper.orderByAsc(TLimsTestdataPrimary::getStartTime);
            List<TLimsTestdataPrimary> flowInfoList = this.list(childQueryWrapper);

            Boolean haveHide = false;
            long count = flowInfoList.stream().filter(f -> null != f.getIsHide() && 1 == f.getIsHide()).count();
            if(count > 0){
                haveHide = true;
            }
            flowInfoList = flowInfoList.stream().filter(f -> null == f.getIsHide() || 0 == f.getIsHide()).collect(Collectors.toList());

            TLimsTestdataPrimary primary = new TLimsTestdataPrimary();
            if(flowInfoList.size() >= 1){
                primary = flowInfoList.get(0);
                primary.setUuid(primary.getId());
                primary.setShowHide(haveHide);
            }
            List<TLimsTestdataPrimary> children = new ArrayList<>();
            for (int i = 1; i < flowInfoList.size(); i++) {
                flowInfoList.get(i).setIsChild(true);
                flowInfoList.get(i).setUuid(flowInfoList.get(i).getId());
                children.add(flowInfoList.get(i));
            }
            primary.setChildren(children.size()>0?children:null);
            newRecords.add(primary);
        });

        page.setRecords(newRecords);

        return new PageResult(page);
    }


    /**
     * 查找电芯是否为测试项目单独所有，如果是，则只需要匹配电芯编码
     * @param rows
     */
    public void syncDataXinWei(List<XinWeiStepInfo> rows){

        rows = rows.stream().filter(r -> StrUtil.isNotBlank(r.getRemark())).collect(Collectors.toList());

        for (int j = 0; j < rows.size(); j++) {
            XinWeiStepInfo flowInfo = rows.get(j);
            String flowJson = flowInfo.get_id();
            XinWeiStepInfo finalStepInfo = JSON.parseObject(flowJson, XinWeiStepInfo.class);
            finalStepInfo.setBarCode(flowInfo.getBarCode());
            finalStepInfo.setRemark(flowInfo.getRemark());
            finalStepInfo.setStartTime(flowInfo.getStartTime());
            finalStepInfo.setEndTime(flowInfo.getEndTime());
            finalStepInfo.setComputerName(flowInfo.getComputerName());

            String text = finalStepInfo.getRemark();
            String day = null;

            // 正则表达式匹配"第"后面跟着任意字符（除了换行符），直到遇到"天"
            String regex = "第(.*?)天";  // 使用非贪婪模式匹配
            if(StrUtil.isNotBlank(text)){
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(text);

                if (matcher.find()) {
                    // 提取出匹配的内容
                    day = matcher.group(1);

                }
            }



            //根据测试编码查询样品信息
            LambdaQueryWrapper<TLimsOrder> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderLambdaQueryWrapper.and(
                    wrapper -> {
                        wrapper.eq(TLimsOrder::getCelltestcode, finalStepInfo.getBarCode()).or()
                                .eq(TLimsOrder::getOrderno, finalStepInfo.getBarCode());
                    }
            );

            TLimsOrder order = orderService.getOne(orderLambdaQueryWrapper);
            if (order == null) {
                continue;
            }
            //查询委托单
            TLimsFolder folder = folderService.getById(order.getFolderid());
            //查询所有测试项
            LambdaQueryWrapper<TLimsOrdtask> ordtaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
            ordtaskLambdaQueryWrapper.eq(TLimsOrdtask::getFolderid, order.getFolderid());
            List<TLimsOrdtask> ordtaskList = ordtaskService.list(ordtaskLambdaQueryWrapper);
            for (int k = 0; k < ordtaskList.size(); k++) {
                TLimsOrdtask tLimsOrdtask = ordtaskList.get(k);

                String tem = null;
                String soc = null;

                LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid,tLimsOrdtask.getId());
                conditionLambdaQueryWrapper.in(TLimsOrdtaskCondition::getConditionname,Arrays.asList("温度","储存SOC"));
                List<TLimsOrdtaskCondition> conditionList = ordtaskConditionService.list(conditionLambdaQueryWrapper);
                if(CollUtil.isNotEmpty(conditionList)){
                    Optional<TLimsOrdtaskCondition> first = conditionList.stream().filter(l -> "温度".equals(l.getConditionname())).findFirst();
                    Optional<TLimsOrdtaskCondition> second = conditionList.stream().filter(l -> "储存SOC".equals(l.getConditionname())).findFirst();
                    if(first.isPresent()){
                        tem = first.get().getConditionvalue();
                    }
                    if(second.isPresent()){
                        soc = second.get().getConditionvalue();
                    }
                }

                //查询测试项目包含的所有测试样品
                LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
                testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, tLimsOrdtask.getId());
                List<TLimsTestmatrix> testmatrices = this.testmatrixService.list(testmatrixLambdaQueryWrapper);
                for (int l = 0; l < testmatrices.size(); l++) {
                    if ("1".equals(testmatrices.get(l).getCheckstatus())) {
                        //查询是否已经创建
                        TLimsOrder limsOrder = orderService.getById(testmatrices.get(l).getOrderid());
                        //匹配测试编号及测试项目别名
                        if ((limsOrder.getCelltestcode().equals(finalStepInfo.getBarCode()) || limsOrder.getOrderno().equals(finalStepInfo.getBarCode()) )
                        ) {

                            LambdaQueryWrapper<TLimsTestmatrix> checkOnlyOneOrdTaskQueryWrapper = new LambdaQueryWrapper<>();
                            checkOnlyOneOrdTaskQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
                            checkOnlyOneOrdTaskQueryWrapper.eq(TLimsTestmatrix::getOrderid, testmatrices.get(l).getOrderid());
                            if(testmatrixService.count(checkOnlyOneOrdTaskQueryWrapper) > 1){
                                if(!(StrUtil.isNotBlank(finalStepInfo.getRemark()) &&
                                        finalStepInfo.getRemark().contains(tLimsOrdtask.getAlias()))){
                                    continue;
                                }
                            }

                            //将补充数据删除
                            LambdaQueryWrapper<TLimsTestdataPrimary> testdataDeletePrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getCelltestcode, limsOrder.getCelltestcode());
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getAlias, tLimsOrdtask.getAlias());
                            testdataDeletePrimaryLambdaQueryWrapper.isNull(TLimsTestdataPrimary::getQueryparam);
                            List<TLimsTestdataPrimary> deleteList = this.list(testdataDeletePrimaryLambdaQueryWrapper);
                            for (TLimsTestdataPrimary testdataPrimary: deleteList) {
                                this.removeById(testdataPrimary.getId());
                            }


                            LambdaQueryWrapper<TLimsTestdataPrimary> testdataPrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getCelltestcode, limsOrder.getCelltestcode());
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getAlias, tLimsOrdtask.getAlias());
                            List<TLimsTestdataPrimary> primaryList = this.list(testdataPrimaryLambdaQueryWrapper);
                            //新增
                            if (primaryList.size() == 0) {
                                TLimsTestdataPrimary addPrimary = TLimsTestdataPrimary.builder()
                                        .folderid(order.getFolderid())
                                        .folderno(order.getFolderno())
                                        .ordtaskid(tLimsOrdtask.getId())
                                        .testcode(tLimsOrdtask.getTestcode())
                                        .testname(tLimsOrdtask.getTestname())
                                        .orderid(limsOrder.getId())
                                        .orderno(limsOrder.getOrderno())
                                        .equiptcode(finalStepInfo.getComputerName())
                                        .dataPath(finalStepInfo.getRemark())
                                        .queryparam(flowInfo.get_id())
                                        .startTime(StrUtil.isNotBlank(finalStepInfo.getStartTime())? DateUtil.parseLocalDateTime(finalStepInfo.getStartTime()):null)
                                        .endTime(StrUtil.isNotBlank(finalStepInfo.getEndTime())?DateUtil.parseLocalDateTime(finalStepInfo.getEndTime()):null)
                                        .endstatusflag(StrUtil.isNotBlank(finalStepInfo.getEndTime())?"1":"0")
                                        .eptfactoryname("新威")
                                        .channelno(finalStepInfo.getChlId())
                                        .alias(tLimsOrdtask.getAlias())
                                        .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                        .createdbyid(folder.getCreatedbyid())
                                        .createdbyorgid(folder.getCreatedbyorgid())
                                        .laboratoryid(folder.getLaboratoryid())
                                        .projectleaderid(folder.getProjectleaderid())
                                        .flowId(flowInfo.get_id())
                                        .createtime(new Date())
                                        .theme(folder.getTheme())
                                        .day(day)
                                        .soc(soc)
                                        .tem(tem)
                                        .celltestcode(limsOrder.getCelltestcode()).build();

                                this.save(addPrimary);

                            } else {
                                //更新
                                    /*//查询FlowInfo
                                    String pattern = tLimsOrdtask.getAlias();
                                    //转义特殊字符
                                    pattern = escapeExprSpecialWord(pattern);
                                    Criteria criteria = Criteria.where("DataPath").regex(pattern).and("BarCode").is(flowInfo.getBarCode());
                                    List<FlowInfo> flowInfoList = shengHongMongoTemplate.find(new Query(criteria), FlowInfo.class);*/
                                Boolean add = true;
                                for (int m = 0; m < primaryList.size(); m++) {
                                    TLimsTestdataPrimary primary = primaryList.get(m);
                                    //已有数据更新
                                    if (StrUtil.isNotBlank(primary.getQueryparam()) && primary.getQueryparam().contains(flowInfo.get_id())) {
                                        TLimsTestdataPrimary updatePrimary = TLimsTestdataPrimary.builder()
                                                .id(primary.getId())
                                                .folderid(order.getFolderid())
                                                .folderno(order.getFolderno())
                                                .ordtaskid(tLimsOrdtask.getId())
                                                .testcode(tLimsOrdtask.getTestcode())
                                                .testname(tLimsOrdtask.getTestname())
                                                .orderid(limsOrder.getId())
                                                .orderno(limsOrder.getOrderno())
                                                .equiptcode(finalStepInfo.getComputerName())
                                                .dataPath(finalStepInfo.getRemark())
                                                .queryparam(flowInfo.get_id())
                                                .startTime(StrUtil.isNotBlank(finalStepInfo.getStartTime())?DateUtil.parseLocalDateTime(finalStepInfo.getStartTime()):null)
                                                .endTime(StrUtil.isNotBlank(finalStepInfo.getEndTime())?DateUtil.parseLocalDateTime(finalStepInfo.getEndTime()):null)
                                                .endstatusflag(StrUtil.isNotBlank(finalStepInfo.getEndTime())?"1":"0")
                                                .eptfactoryname("新威")
                                                .channelno(finalStepInfo.getChlId())
                                                .alias(tLimsOrdtask.getAlias())
                                                .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                                .createdbyid(folder.getCreatedbyid())
                                                .createdbyorgid(folder.getCreatedbyorgid())
                                                .laboratoryid(folder.getLaboratoryid())
                                                .projectleaderid(folder.getProjectleaderid())
                                                .flowId(flowInfo.get_id())
                                                .createtime(new Date())
                                                .theme(folder.getTheme())
                                                .day(day)
                                                .soc(soc)
                                                .tem(tem)
                                                .celltestcode(limsOrder.getCelltestcode()).build();

                                        this.updateById(updatePrimary);
                                        add = false;
                                    }
                                }
                                if (add) {
                                    //新增
                                    TLimsTestdataPrimary addPrimary = TLimsTestdataPrimary.builder()
                                            .folderid(order.getFolderid())
                                            .folderno(order.getFolderno())
                                            .ordtaskid(tLimsOrdtask.getId())
                                            .testcode(tLimsOrdtask.getTestcode())
                                            .testname(tLimsOrdtask.getTestname())
                                            .orderid(limsOrder.getId())
                                            .orderno(limsOrder.getOrderno())
                                            .equiptcode(finalStepInfo.getComputerName())
                                            .dataPath(finalStepInfo.getRemark())
                                            .queryparam(flowInfo.get_id())
                                            .startTime(StrUtil.isNotBlank(finalStepInfo.getStartTime())?DateUtil.parseLocalDateTime(finalStepInfo.getStartTime()):null)
                                            .endTime(StrUtil.isNotBlank(finalStepInfo.getEndTime())?DateUtil.parseLocalDateTime(finalStepInfo.getEndTime()):null)
                                            .endstatusflag(StrUtil.isNotBlank(finalStepInfo.getEndTime())?"1":"0")
                                            .eptfactoryname("新威")
                                            .channelno(finalStepInfo.getChlId())
                                            .alias(tLimsOrdtask.getAlias())
                                            .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                            .createdbyid(folder.getCreatedbyid())
                                            .createdbyorgid(folder.getCreatedbyorgid())
                                            .laboratoryid(folder.getLaboratoryid())
                                            .projectleaderid(folder.getProjectleaderid())
                                            .flowId(flowInfo.get_id())
                                            .createtime(new Date())
                                            .theme(folder.getTheme())
                                            .day(day)
                                            .celltestcode(limsOrder.getCelltestcode()).build();

                                    this.save(addPrimary);
                                }

                            }
                        }

                    }


                }


            }


        }
    }



    @Override
    public void syncTestPrimary() {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime previousHalfHour = currentTime.minus(30, ChronoUnit.MINUTES);
        //盛弘

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("StartTime").gte(previousHalfHour),
                Criteria.where("EndTime").gte(previousHalfHour)
        );
        List<FlowInfo> rows = shengHongMongoTemplate.find(new Query(criteria), FlowInfo.class);
        syncData(rows);

        //新威
        Criteria criteriaXinWei = new Criteria();
        criteriaXinWei.andOperator(
                Criteria.where("barcode").ne(""),
                Criteria.where("barcode").ne(null)
        );

        criteriaXinWei.orOperator(
                Criteria.where("start_time").gte(DateUtil.format(previousHalfHour,"yyyy-MM-dd HH:mm:ss")),
                Criteria.where("end_time").gte(DateUtil.format(previousHalfHour,"yyyy-MM-dd HH:mm:ss")),
                Criteria.where("upload_time").gte(DateUtil.format(previousHalfHour,"yyyy-MM-dd HH:mm:ss"))
        );

        //设置数字排序

        //管道查询数据  allowDiskUse(true) 开启磁盘缓存
        TypedAggregation aggregation = Aggregation.newAggregation(
                XinWeiStepInfo.class,
                Aggregation.group("test_id", "chl_id")
                        .last("barcode").as("barcode")
                        .max("upload_time").as("upload_time")
                        .min("start_time").as("start_time")
                        .max("end_time").as("end_time")
                        .first("remark").as("remark")
                        .first("computer_name").as("computer_name"),
                Aggregation.match(criteriaXinWei)
        )
                .withOptions(AggregationOptions.builder().
                        allowDiskUse(true).build());

        List<XinWeiStepInfo> xinWeiRows = xinWeiMongoTemplate.aggregate(aggregation, XinWeiStepInfo.class).getMappedResults();

        syncDataXinWei(xinWeiRows);


        //chroma
        Criteria criteriaChroma = new Criteria();
        criteriaChroma.andOperator(
                Criteria.where("identityNo").ne(""),
                Criteria.where("identityNo").ne(null)
        );
        criteriaChroma.orOperator(
                Criteria.where("beginTime").gte(DateUtil.format(previousHalfHour,"yyyy/M/d H:mm:ss")),
                Criteria.where("resultTime").gte(DateUtil.format(previousHalfHour,"yyyy/M/d H:mm:ss"))
        );
        List<ChromaTestData> chromaTestData = this.chromaMongoTemplate.find(new Query(criteriaChroma), ChromaTestData.class);

        //转换成盛弘后同步
        List<FlowInfo> flowInfoList = chromaService.chromaTestDataToShenghong(chromaTestData);
        syncData(flowInfoList);

        //蓝电
        Criteria criteriaLandian = new Criteria();
        criteriaLandian.andOperator(
                Criteria.where("BarCode").ne(""),
                Criteria.where("BarCode").ne(null)
        );
        criteriaLandian.orOperator(
                Criteria.where("StartTime").gte(DateUtil.format(previousHalfHour,"yyyy/MM/dd HH:mm:ss")),
                Criteria.where("EndTime").gte(DateUtil.format(previousHalfHour,"yyyy/MM/dd HH:mm:ss"))
        );
        List<LandianFlowInfo> landianFlowInfos = this.landianMongoTemplate.find(new Query(criteriaLandian), LandianFlowInfo.class);

        //转换成盛弘后同步
        List<FlowInfo> landianList = landianService.landianFlowInfoToShenghong(landianFlowInfos);
        syncData(landianList);

        //瑞能
        Criteria criteriaRuineng = new Criteria();
        criteriaRuineng.andOperator(
                Criteria.where("BarCode").ne(""),
                Criteria.where("BarCode").ne(null)
        );
        criteriaRuineng.orOperator(
                Criteria.where("StartTime").gte(DateUtil.format(previousHalfHour,"yyyy/MM/dd HH:mm:ss")),
                Criteria.where("EndTime").gte(DateUtil.format(previousHalfHour,"yyyy/MM/dd HH:mm:ss"))
        );
        List<RuinengFlowInfo> ruinengFlowInfos = this.ruinengMongoTemplate.find(new Query(criteriaRuineng), RuinengFlowInfo.class);

        //转换成盛弘后同步
        List<FlowInfo> ruinengList = ruinengService.ruinengFlowInfoToShenghong(ruinengFlowInfos);
        syncData(ruinengList);
    }

    @Override
    public void syncTestPrimaryOneDayData() {

        Calendar calendar = Calendar.getInstance();

        calendar.add(Calendar.DAY_OF_MONTH, -3); // 将当前日期减一天

        Date time = calendar.getTime();

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("StartTime").gte(time),
                Criteria.where("EndTime").gte(time)
        );

        List<FlowInfo> rows = shengHongMongoTemplate.find(new Query(criteria), FlowInfo.class);
        syncData(rows);

        //新威
        Criteria criteriaXinWei = new Criteria();
        criteriaXinWei.andOperator(
                Criteria.where("barcode").ne(""),
                Criteria.where("barcode").ne(null)
        );

        criteriaXinWei.orOperator(
                Criteria.where("start_time").gte(DateUtil.format(time,"yyyy-MM-dd HH:mm:ss")),
                Criteria.where("end_time").gte(DateUtil.format(time,"yyyy-MM-dd HH:mm:ss")),
                Criteria.where("upload_time").gte(DateUtil.format(time,"yyyy-MM-dd HH:mm:ss"))
        );


        //设置数字排序
        Collation collation = Collation.of(Locale.CHINESE)
                .numericOrdering(true);
        //管道查询数据  allowDiskUse(true) 开启磁盘缓存
        TypedAggregation aggregation = Aggregation.newAggregation(
                XinWeiStepInfo.class,
                Aggregation.group("test_id", "chl_id")
                        .last("barcode").as("barcode")
                        .max("upload_time").as("upload_time")
                        .min("start_time").as("start_time")
                        .max("end_time").as("end_time")
                        .first("remark").as("remark")
                        .first("computer_name").as("computer_name"),
                Aggregation.match(criteriaXinWei)
        )
                .withOptions(AggregationOptions.builder().
                        allowDiskUse(true).collation(collation).build());

        List<XinWeiStepInfo> xinWeiRows = xinWeiMongoTemplate.aggregate(aggregation, XinWeiStepInfo.class).getMappedResults();

        syncDataXinWei(xinWeiRows);

        //chroma
        Criteria criteriaChroma = new Criteria();
        criteriaChroma.andOperator(
                Criteria.where("identityNo").ne(""),
                Criteria.where("identityNo").ne(null)
        );
        criteriaChroma.orOperator(
                Criteria.where("beginTime").gte(DateUtil.format(time,"yyyy/M/d H:mm:ss")),
                Criteria.where("resultTime").gte(DateUtil.format(time,"yyyy/M/d H:mm:ss"))
        );
        List<ChromaTestData> chromaTestData = this.chromaMongoTemplate.find(new Query(criteriaChroma), ChromaTestData.class);

        //转换成盛弘后同步
        List<FlowInfo> flowInfoList = chromaService.chromaTestDataToShenghong(chromaTestData);
        syncData(flowInfoList);

        //蓝电
        Criteria criteriaLandian = new Criteria();
        criteriaLandian.andOperator(
                Criteria.where("BarCode").ne(""),
                Criteria.where("BarCode").ne(null)
        );
        criteriaLandian.orOperator(
                Criteria.where("StartTime").gte(DateUtil.format(time,"yyyy/MM/dd HH:mm:ss")),
                Criteria.where("EndTime").gte(DateUtil.format(time,"yyyy/MM/dd HH:mm:ss"))
        );
        List<LandianFlowInfo> landianFlowInfos = this.landianMongoTemplate.find(new Query(criteriaLandian), LandianFlowInfo.class);

        //转换成盛弘后同步
        List<FlowInfo> landianList = landianService.landianFlowInfoToShenghong(landianFlowInfos);
        syncData(landianList);

        //瑞能
        Criteria criteriaRuineng = new Criteria();
        criteriaRuineng.andOperator(
                Criteria.where("BarCode").ne(""),
                Criteria.where("BarCode").ne(null)
        );
        criteriaRuineng.orOperator(
                Criteria.where("StartTime").gte(DateUtil.format(time,"yyyy/MM/dd HH:mm:ss")),
                Criteria.where("EndTime").gte(DateUtil.format(time,"yyyy/MM/dd HH:mm:ss"))
        );
        List<RuinengFlowInfo> ruinengFlowInfos = this.ruinengMongoTemplate.find(new Query(criteriaRuineng), RuinengFlowInfo.class);

        //转换成盛弘后同步
        List<FlowInfo> ruinengList = ruinengService.ruinengFlowInfoToShenghong(ruinengFlowInfos);
        syncData(ruinengList);


    }

    @Override
    public void syncData(List<FlowInfo> rows){
        for (int j = 0; j < rows.size(); j++) {
            FlowInfo flowInfo = rows.get(j);

            String text = flowInfo.getDataPath();
            String day = null;
            if(StrUtil.isNotBlank(text)){

                // 正则表达式匹配"第"后面跟着任意字符（除了换行符），直到遇到"天"
                String regex = "第(.*?)天";  // 使用非贪婪模式匹配
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(text);

                if (matcher.find()) {
                    // 提取出匹配的内容
                    day = matcher.group(1);
                }
            }

            if (StrUtil.isBlank(flowInfo.getFlowId()) || StrUtil.isBlank(flowInfo.getBarCode()) || StrUtil.isBlank(flowInfo.getDataPath())) {
                continue;
            }
            //根据测试编码查询样品信息
            LambdaQueryWrapper<TLimsOrder> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderLambdaQueryWrapper.and(
                    wrapper -> {
                        wrapper.eq(TLimsOrder::getCelltestcode, flowInfo.getBarCode()).or()
                                .eq(TLimsOrder::getOrderno, flowInfo.getBarCode());
                    }
            );
            TLimsOrder order = orderService.getOne(orderLambdaQueryWrapper);
            if (order == null) {
                continue;
            }
            //查询委托单
            TLimsFolder folder = folderService.getById(order.getFolderid());
            //查询所有测试项
            LambdaQueryWrapper<TLimsOrdtask> ordtaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
            ordtaskLambdaQueryWrapper.eq(TLimsOrdtask::getFolderid, order.getFolderid());
            List<TLimsOrdtask> ordtaskList = ordtaskService.list(ordtaskLambdaQueryWrapper);

            for (int k = 0; k < ordtaskList.size(); k++) {
                TLimsOrdtask tLimsOrdtask = ordtaskList.get(k);
                //查询测试项目包含的所有测试样品
                LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
                testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, tLimsOrdtask.getId());
                List<TLimsTestmatrix> testmatrices = this.testmatrixService.list(testmatrixLambdaQueryWrapper);

                String tem = null;
                String soc = null;

                LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid,tLimsOrdtask.getId());
                conditionLambdaQueryWrapper.in(TLimsOrdtaskCondition::getConditionname,Arrays.asList("温度","储存SOC"));
                List<TLimsOrdtaskCondition> conditionList = ordtaskConditionService.list(conditionLambdaQueryWrapper);
                if(CollUtil.isNotEmpty(conditionList)){
                    Optional<TLimsOrdtaskCondition> first = conditionList.stream().filter(l -> "温度".equals(l.getConditionname())).findFirst();
                    Optional<TLimsOrdtaskCondition> second = conditionList.stream().filter(l -> "储存SOC".equals(l.getConditionname())).findFirst();
                    if(first.isPresent()){
                        tem = first.get().getConditionvalue();
                    }
                    if(second.isPresent()){
                        soc = second.get().getConditionvalue();
                    }
                }


                for (int l = 0; l < testmatrices.size(); l++) {
                    if ("1".equals(testmatrices.get(l).getCheckstatus())) {
                        //查询是否已经创建
                        TLimsOrder limsOrder = orderService.getById(testmatrices.get(l).getOrderid());
                        //匹配测试编号
                        if (limsOrder.getCelltestcode().equals(flowInfo.getBarCode()) || limsOrder.getOrderno().equals(flowInfo.getBarCode()) ) {

                            //判断电芯是否只有一个测试项目测
                            LambdaQueryWrapper<TLimsTestmatrix> checkOnlyOneOrdTaskQueryWrapper = new LambdaQueryWrapper<>();
                            checkOnlyOneOrdTaskQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
                            checkOnlyOneOrdTaskQueryWrapper.eq(TLimsTestmatrix::getOrderid, testmatrices.get(l).getOrderid());
                            if(testmatrixService.count(checkOnlyOneOrdTaskQueryWrapper) > 1){

                                if(!(StrUtil.isNotBlank(flowInfo.getDataPath()) && flowInfo.getDataPath().contains(tLimsOrdtask.getAlias()))){
                                    continue;
                                }
                            }

                            //将补充数据删除
                            LambdaQueryWrapper<TLimsTestdataPrimary> testdataDeletePrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getCelltestcode, limsOrder.getCelltestcode());
                            testdataDeletePrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getAlias, tLimsOrdtask.getAlias());
                            testdataDeletePrimaryLambdaQueryWrapper.isNull(TLimsTestdataPrimary::getQueryparam);
                            List<TLimsTestdataPrimary> deleteList = this.list(testdataDeletePrimaryLambdaQueryWrapper);
                            for (TLimsTestdataPrimary testdataPrimary: deleteList) {
                                this.removeById(testdataPrimary.getId());
                            }

                            LambdaQueryWrapper<TLimsTestdataPrimary> testdataPrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus, 0);
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getCelltestcode, limsOrder.getCelltestcode());
                            testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getAlias, tLimsOrdtask.getAlias());
                            List<TLimsTestdataPrimary> primaryList = this.list(testdataPrimaryLambdaQueryWrapper);
                            //新增
                            if (primaryList.size() == 0) {
                                Map<String, String> queryparam = new HashMap<>();
                                queryparam.put("FlowId", flowInfo.getFlowId());
                                TLimsTestdataPrimary addPrimary = TLimsTestdataPrimary.builder()
                                        .folderid(order.getFolderid())
                                        .folderno(order.getFolderno())
                                        .ordtaskid(tLimsOrdtask.getId())
                                        .testcode(tLimsOrdtask.getTestcode())
                                        .testname(tLimsOrdtask.getTestname())
                                        .orderid(limsOrder.getId())
                                        .orderno(limsOrder.getOrderno())
                                        .equiptcode(flowInfo.getUnitNum().toString())
                                        .dataPath(flowInfo.getDataPath())
                                        .queryparam(StrUtil.isNotBlank(flowInfo.getQueryParam())?flowInfo.getQueryParam():JSON.toJSONString(queryparam))
                                        .startTime(flowInfo.getStartTime())
                                        .endTime(flowInfo.getEndTime())
                                        .endstatusflag(flowInfo.getState())
                                        .eptfactoryname(StrUtil.isNotBlank(flowInfo.getEptFactory())?flowInfo.getEptFactory():"盛弘")
                                        .channelno(flowInfo.getChannelId().toString())
                                        .alias(tLimsOrdtask.getAlias())
                                        .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                        .createdbyid(folder.getCreatedbyid())
                                        .createdbyorgid(folder.getCreatedbyorgid())
                                        .laboratoryid(folder.getLaboratoryid())
                                        .projectleaderid(folder.getProjectleaderid())
                                        .flowId(flowInfo.getFlowId())
                                        .createtime(new Date())
                                        .theme(folder.getTheme())
                                        .day(day)
                                        .tem(tem)
                                        .soc(soc)
                                        .celltestcode(limsOrder.getCelltestcode()).build();
                                this.save(addPrimary);

                            } else {
                                //更新
                                /*//查询FlowInfo
                                String pattern = tLimsOrdtask.getAlias();
                                //转义特殊字符
                                pattern = escapeExprSpecialWord(pattern);
                                Criteria criteria = Criteria.where("DataPath").regex(pattern).and("BarCode").is(flowInfo.getBarCode());
                                List<FlowInfo> flowInfoList = shengHongMongoTemplate.find(new Query(criteria), FlowInfo.class);*/
                                Boolean add = true;
                                for (int m = 0; m < primaryList.size(); m++) {
                                    TLimsTestdataPrimary primary = primaryList.get(m);
                                    //已有数据更新
                                    if (StrUtil.isNotBlank(primary.getQueryparam()) && primary.getQueryparam().contains(flowInfo.getFlowId())) {
                                        TLimsTestdataPrimary updatePrimary = TLimsTestdataPrimary.builder()
                                                .id(primary.getId())
                                                .folderid(order.getFolderid())
                                                .folderno(order.getFolderno())
                                                .ordtaskid(tLimsOrdtask.getId())
                                                .testcode(tLimsOrdtask.getTestcode())
                                                .testname(tLimsOrdtask.getTestname())
                                                .orderid(limsOrder.getId())
                                                .orderno(limsOrder.getOrderno())
                                                .equiptcode(flowInfo.getUnitNum().toString())
                                                .dataPath(flowInfo.getDataPath())
                                                .startTime(flowInfo.getStartTime())
                                                .endTime(flowInfo.getEndTime())
                                                .endstatusflag(flowInfo.getState())
                                                .eptfactoryname(StrUtil.isNotBlank(flowInfo.getEptFactory())?flowInfo.getEptFactory():"盛弘")
                                                .channelno(flowInfo.getChannelId().toString())
                                                .alias(tLimsOrdtask.getAlias())
                                                .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                                .createdbyid(folder.getCreatedbyid())
                                                .createdbyorgid(folder.getCreatedbyorgid())
                                                .laboratoryid(folder.getLaboratoryid())
                                                .projectleaderid(folder.getProjectleaderid())
                                                .flowId(flowInfo.getFlowId())
                                                .updatetime(new Date())
                                                .theme(folder.getTheme())
                                                .day(day)
                                                .tem(tem)
                                                .soc(soc)
                                                .celltestcode(limsOrder.getCelltestcode()).build();

                                        this.updateById(updatePrimary);
                                        add = false;
                                    }
                                }
                                if (add) {
                                    //新增
                                    Map<String, String> queryparam = new HashMap<>();
                                    queryparam.put("FlowId", flowInfo.getFlowId());
                                    TLimsTestdataPrimary addPrimary = TLimsTestdataPrimary.builder()
                                            .folderid(order.getFolderid())
                                            .folderno(order.getFolderno())
                                            .ordtaskid(tLimsOrdtask.getId())
                                            .testcode(tLimsOrdtask.getTestcode())
                                            .testname(tLimsOrdtask.getTestname())
                                            .orderid(limsOrder.getId())
                                            .orderno(limsOrder.getOrderno())
                                            .equiptcode(flowInfo.getUnitNum().toString())
                                            .dataPath(flowInfo.getDataPath())
                                            .queryparam(StrUtil.isNotBlank(flowInfo.getQueryParam())?flowInfo.getQueryParam():JSON.toJSONString(queryparam))
                                            .startTime(flowInfo.getStartTime())
                                            .endTime(flowInfo.getEndTime())
                                            .endstatusflag(flowInfo.getState())
                                            .eptfactoryname((StrUtil.isNotBlank(flowInfo.getEptFactory())?flowInfo.getEptFactory():"盛弘"))
                                            .channelno(flowInfo.getChannelId().toString())
                                            .alias(tLimsOrdtask.getAlias())
                                            .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                            .createdbyid(folder.getCreatedbyid())
                                            .createdbyorgid(folder.getCreatedbyorgid())
                                            .laboratoryid(folder.getLaboratoryid())
                                            .projectleaderid(folder.getProjectleaderid())
                                            .flowId(flowInfo.getFlowId())
                                            .createtime(new Date())
                                            .theme(folder.getTheme())
                                            .day(day)
                                            .tem(tem)
                                            .soc(soc)
                                            .celltestcode(limsOrder.getCelltestcode()).build();

                                    this.save(addPrimary);
                                }

                            }
                        }

                    }


                }


            }
        }
    }

    //补充没有启动的电芯数据
    @Override
    public void fullDataNoMongo(){

        Calendar calendar = Calendar.getInstance();

        calendar.add(Calendar.MINUTE, -30); // 将当前日期减半小时

        Date time = calendar.getTime();

        LambdaQueryWrapper<TLimsTestdataPrimary> testdataPrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testdataPrimaryLambdaQueryWrapper.select(TLimsTestdataPrimary::getFolderno);
        testdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus,0);
        testdataPrimaryLambdaQueryWrapper.ge(TLimsTestdataPrimary::getCreatetime,time);
        testdataPrimaryLambdaQueryWrapper.groupBy(TLimsTestdataPrimary::getFolderno);
        List<TLimsTestdataPrimary> list = this.list(testdataPrimaryLambdaQueryWrapper);
        for (TLimsTestdataPrimary folderPrimary : list) {
            LambdaQueryWrapper<TLimsTestdataPrimary> oneFolderTestdataPrimaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            oneFolderTestdataPrimaryLambdaQueryWrapper.select(TLimsTestdataPrimary::getCelltestcode);

            oneFolderTestdataPrimaryLambdaQueryWrapper.groupBy(TLimsTestdataPrimary::getCelltestcode);
            oneFolderTestdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getFolderno,folderPrimary.getFolderno());
            oneFolderTestdataPrimaryLambdaQueryWrapper.eq(TLimsTestdataPrimary::getDeleteStatus,0);
            //已经测试中的数据
            List<TLimsTestdataPrimary> haveTestData = this.list(oneFolderTestdataPrimaryLambdaQueryWrapper);
            LambdaQueryWrapper<TLimsOrder> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderLambdaQueryWrapper.eq(TLimsOrder::getFolderno,folderPrimary.getFolderno());
            //测试单所有样品
            List<TLimsOrder> orderList = orderService.list(orderLambdaQueryWrapper);
            if(haveTestData.size() == orderList.size() ){
                continue;
            }else{

                if(orderList.size() > 0){
                    TLimsFolder folder = this.folderService.getById(orderList.get(0).getFolderid());
                    for (TLimsOrder order: orderList) {
                        long count = haveTestData.stream().filter(h -> order.getCelltestcode().equals(h.getCelltestcode())).count();
                        //需要补充数据
                        if(count < 1){


                            LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getFolderid, order.getFolderid());
                            testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, 1);
                            testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrderid, order.getId());
                            List<TLimsTestmatrix> testmatrices = this.testmatrixService.list(testmatrixLambdaQueryWrapper);

                            if(testmatrices.size() > 0){
                                TLimsTestmatrix tLimsTestmatrix = testmatrices.get(0);
                                TLimsOrdtask tLimsOrdtask = ordtaskService.getById(tLimsTestmatrix.getOrdtaskid());

                                TLimsTestdataPrimary addPrimary = TLimsTestdataPrimary.builder()
                                        .folderid(order.getFolderid())
                                        .folderno(order.getFolderno())
                                        .ordtaskid(tLimsOrdtask.getId())
                                        .testcode(tLimsOrdtask.getTestcode())
                                        .testname(tLimsOrdtask.getTestname())
                                        .orderid(order.getId())
                                        .orderno(order.getOrderno())
                                        //.deleteStatus(1)
                                        .eptfactoryname("补充数据")

                                        .alias(tLimsOrdtask.getAlias())
                                        .startcodeuuid(tLimsOrdtask.getStartcodeuuid())
                                        .createdbyid(folder.getCreatedbyid())
                                        .createdbyorgid(folder.getCreatedbyorgid())
                                        .laboratoryid(folder.getLaboratoryid())
                                        .projectleaderid(folder.getProjectleaderid())
                                        .createtime(new Date())
                                        .theme(folder.getTheme())
                                        .celltestcode(order.getCelltestcode()).build();

                                this.save(addPrimary);
                            }

                        }
                    }
                }


            }

        }


    }

    @Override
    public Boolean hideData(TLimsTestdataPrimary param){
        return this.updateById(TLimsTestdataPrimary.builder().id(param.getId()).isHide(1).build());
    }

    @Override
    public Boolean showData(TLimsTestdataPrimary param){
        LambdaUpdateWrapper<TLimsTestdataPrimary> tLimsTestdataPrimaryLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        tLimsTestdataPrimaryLambdaUpdateWrapper.eq(TLimsTestdataPrimary::getCelltestcode,param.getCelltestcode());
        tLimsTestdataPrimaryLambdaUpdateWrapper.set(TLimsTestdataPrimary::getIsHide,0);
        return this.update(tLimsTestdataPrimaryLambdaUpdateWrapper);
    }

    /**
     * 定时刷新创建人不符的数据（LIMS转办）
     */
    @Override
    public void checkCreateAccount() {
        LambdaQueryWrapper<TLimsTestdataPrimary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TLimsTestdataPrimary::getFlowId);
        queryWrapper.exists("select 1 from T_LIMS_FOLDER  where T_LIMS_FOLDER.ID= T_LIMS_TESTDATA_PRIMARY.FOLDERID and T_LIMS_FOLDER.CREATEDBYID != T_LIMS_TESTDATA_PRIMARY.CREATEDBYID ");
        List<TLimsTestdataPrimary> list = this.list(queryWrapper);
        list.forEach(p -> {
            TLimsFolder folder = folderService.getById(p.getFolderid());
            this.updateById(TLimsTestdataPrimary.builder().createdbyid(folder.getCreatedbyid()).id(p.getId()).build());
        });
    }

    /**
     * 同步第四实验室日历寿命历史数据（只同步一次）
     */
    @Override
    public void syncSafeData() {
        LambdaQueryWrapper<TLimsOrdtask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TLimsOrdtask::getFolderno,Arrays.asList("************",
                "************",
                "202408130100",
                "202408210071",
                "202408210095",
                "202408210095",
                "202408220063"));
        queryWrapper.in(TLimsOrdtask::getTestname,Arrays.asList("高温存储","高温高湿存储"));
        //第四实验室且状态不是取消或者新建
        queryWrapper.exists(
                "SELECT 1 FROM T_LIMS_FOLDER F WHERE F.ID = T_LIMS_ORDTASK.FOLDERID AND F.LABORATORY = '第四实验室' AND F.STATUS IN ('Testing','Done')"
        );
        List<TLimsOrdtask> ordtaskList = this.ordtaskService.list(queryWrapper);
        ordtaskList.forEach(ordtask -> {
            TLimsFolder folder = this.folderService.getById(ordtask.getFolderid());

            //样品信息
            LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
            testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, ordtask.getId());
            testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getCheckstatus, "1");
            List<TLimsTestmatrix> testmatrices = this.testmatrixService.list(testmatrixLambdaQueryWrapper);
            List<String> sampleCodes = new ArrayList<>();
            List<String> cellTestCodes = new ArrayList<>();
            for (int i = 0; i < testmatrices.size(); i++) {
                TLimsOrder order = orderService.getById(testmatrices.get(i).getOrderid());
                sampleCodes.add(order.getOrderno());
                cellTestCodes.add(order.getCelltestcode());
            }
            //组装中检数据
            TestProgress addParam = TestProgress.builder()
                    .testCode(folder.getFolderno())
                    .productName(folder.getProducttype())//产品类型
                    .sampleType(folder.getCylindercellflag())
                    .productSampleStage(folder.getTechnicalstatus())
                    .testType(folder.getTesttype())
                    .testAlias(ordtask.getAlias())
                    .applicant(folder.getCreatedbyname())
                    .applicantAccount(folder.getCreatedbyid())
                    .dept(folder.getCreatedbyorgname())
                    .testAddress("R3")
                    .quantity(Long.valueOf(sampleCodes.size()))
                    .sampleCodes(JSON.toJSONString(sampleCodes))
                    .cellTestCodes(JSON.toJSONString(cellTestCodes))
                    .testStatus("Done".equals(folder.getStatus())?"Done":"Ongoing")
                    .ordtaskid(ordtask.getId())
                    .testPurpose(folder.getTestpurpose())
                    .stageFlag(0)
                    .build();

            this.progressService.save(addParam);

            TestProgressDetail detail = TestProgressDetail.builder()
                    .day(0L)
                    .totalDay(0L)
                    .orderNumber(1)
                    .progressId(addParam.getId())
                    .build();

            //初始化一条中间明细
            this.progressDetailService.save(detail);



        });

    }

    @Override
    public void syncSafeDataOnlyTesting() {
        LambdaQueryWrapper<TestProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TestProgress::getTestAddress,"R3");
        List<TestProgress> list = this.progressService.list(queryWrapper);
        list.forEach(l -> {
            LambdaQueryWrapper<TLimsFolder> folderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            folderLambdaQueryWrapper.eq(TLimsFolder::getFolderno, l.getTestCode());
            TLimsFolder one = this.folderService.getOne(folderLambdaQueryWrapper);
            if(one != null && one.getStatus() != null && !Arrays.asList("Done","Testing").contains(one.getStatus()) ){
                this.progressService.removeById(l.getId());
            }
        });
    }


    public static void testApiServer(String inputText) throws IOException {
        long startTime = System.currentTimeMillis();
        System.err.println(inputText+"开始提问"+new Date());
        String apiUrl = "http://************:11434/v1/chat/completions";
        HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        JSONObject data = new JSONObject()
                .put("messages", new JSONArray()
                        .put(new JSONObject().put("role", "system").put("content", ""))
                        .put(new JSONObject().put("role", "user").put("content", "你觉得自己优秀吗？"))
                        .put(new JSONObject().put("role", "user").put("content", "我如何定义不重要"))

                )
                .put("temperature", 0.3)
                .put("top_p", 0.95)
                .put("max_tokens", 512)
                .put("model", "gemma2:27b")
                .put("stream", true)
                .put("n", 1)
                .put("best_of", 1)
                .put("presence_penalty", 1.2)
                .put("frequency_penalty", 0.2)
                .put("top_k", 50)
                .put("use_beam_search", false)
                .put("stop", new JSONArray())
                .put("ignore_eos", false)
                .put("logprobs", JSONObject.DEFAULT_CAPACITY);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(data.toString().getBytes(StandardCharsets.UTF_8));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            System.err.println(inputText+"开始回答"+new Date());
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = in.readLine()) != null) {
                    line = line.trim();
                    if (line.isEmpty() || line.equals("[DONE]") || !line.startsWith("data: ")) {
                        continue;
                    }
                    line = line.substring(6).trim(); // 去除 "data: "

                    try {
                        JSONObject result = new JSONObject(line);
                        JSONArray choices = result.getJSONArray("choices");
                        JSONObject delta = choices.getJSONObject(0).getJSONObject("delta");
                        String content = delta.getStr("content");

                        System.out.print(content);
                    } catch (JSONException ignored) {
                    }
                }
            }
        } else {
            System.out.println("HTTP error code : " + connection.getResponseCode());
        }

        long elapsedTime = System.currentTimeMillis() - startTime;
        System.out.println(inputText+"完成耗时: " + elapsedTime / 1000.0 + " 秒");
    }
}
