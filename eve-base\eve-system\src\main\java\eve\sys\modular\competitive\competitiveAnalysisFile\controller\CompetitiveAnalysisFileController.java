package eve.sys.modular.competitive.competitiveAnalysisFile.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.competitive.competitiveAnalysisFile.entity.CompetitiveAnalysisFile;
import eve.sys.modular.competitive.competitiveAnalysisFile.service.ICompetitiveAnalysisFileService;
import eve.sys.modular.competitive.competitiveAnalysisSample.entity.CompetitiveAnalysisSample;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <p>
 * 竞品分析数据包 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@RestController
@RequestMapping("/competitiveAnalysisFile")
public class CompetitiveAnalysisFileController {

    @Resource
    private ICompetitiveAnalysisFileService competitiveAnalysisFileService;

    @PostMapping("/add")
    @BusinessLog(title = "竞品分析数据包-文件上传", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData getByBatteryId(@RequestPart MultipartFile file, @ModelAttribute CompetitiveAnalysisFile param) throws Exception{
        return new SuccessResponseData(competitiveAnalysisFileService.add(file,param));
    }

    @PostMapping("/remove")
    @BusinessLog(title = "竞品分析数据包-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData remove(@RequestBody CompetitiveAnalysisFile param) {
        return new SuccessResponseData(competitiveAnalysisFileService.remove(param));
    }

    @PostMapping("/update")
    @BusinessLog(title = "竞品分析数据包-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody CompetitiveAnalysisFile param) {
        return new SuccessResponseData(competitiveAnalysisFileService.update(param));
    }

    @PostMapping("/list")
    @BusinessLog(title = "竞品分析数据包-列表查询", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData list(@RequestBody CompetitiveAnalysisFile param) {
        return new SuccessResponseData(competitiveAnalysisFileService.list(param));
    }

}

