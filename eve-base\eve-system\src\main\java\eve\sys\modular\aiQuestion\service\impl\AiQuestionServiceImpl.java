package eve.sys.modular.aiQuestion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.sys.modular.aiQuestion.entity.AiQuestion;
import eve.sys.modular.aiQuestion.mapper.AiQuestionMapper;
import eve.sys.modular.aiQuestion.service.IAiQuestionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class AiQuestionServiceImpl extends ServiceImpl<AiQuestionMapper, AiQuestion> implements IAiQuestionService {

    @Override
    public List<AiQuestion> list(AiQuestion param) {
        LambdaQueryWrapper<AiQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiQuestion::getQuestionId,param.getQuestionId());
        queryWrapper.isNotNull(AiQuestion::getQuestion);
        queryWrapper.orderByAsc(AiQuestion::getCreateTime);
        return this.list(queryWrapper);
    }
}
