package eve.sys.dorisModular.testData.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.sys.dorisModular.testData.entity.SyncDetail;
import eve.sys.dorisModular.testData.mapper.SyncDetailMapper;
import eve.sys.dorisModular.testData.service.ISyncDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class SyncDetailServiceImpl extends ServiceImpl<SyncDetailMapper, SyncDetail> implements ISyncDetailService {

}
