package eve.sys.modular.bom.params;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class TreeBomParam {

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "jira标志")
    private Long issueId;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "产品")
    private String productName;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "BOM编号")
    private String bomNo;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "PBI标志")
    private String id;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "物料代码")
    private String sapNumber;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "物料规格")
    private String partDescription;

    @HeadFontStyle(fontHeightInPoints = 8)
    @ContentFontStyle(fontHeightInPoints = 8)
    @ExcelProperty(value = "层级")
    private Long levels;
}
