package eve.sys.modular.competitive.competitiveAnalysis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.competitive.competitiveAnalysis.entity.CompetitiveAnalysis;

import java.util.Map;

/**
 * <p>
 * 竞品分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface ICompetitiveAnalysisService extends IService<CompetitiveAnalysis> {

    PageResult<CompetitiveAnalysis> pageList(CompetitiveAnalysis param);

    Map echartsData(String echartsType);

    Map treeData(String queryParam);

    Boolean remove(CompetitiveAnalysis param);

    Boolean update(CompetitiveAnalysis param);

    Boolean add(CompetitiveAnalysis param);
}
