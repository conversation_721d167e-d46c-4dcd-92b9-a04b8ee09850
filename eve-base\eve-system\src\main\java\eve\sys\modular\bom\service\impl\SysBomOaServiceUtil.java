package eve.sys.modular.bom.service.impl;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/* import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject; */

import cn.hutool.Hutool;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import eve.core.exception.ServiceException;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;
import eve.sys.modular.product.param.ProjectDetail;
import eve.sys.modular.product.param.request.DocParams;
import eve.sys.modular.product.service.ProductJiraService;
import eve.sys.modular.werkline.entity.SysWerkLine;
import eve.sys.modular.werkline.service.ISysWerkLineService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import eve.core.context.constant.ConstantContextHolder;
import eve.core.context.login.LoginContextHolder;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.OaAddOrEditParam;
import eve.sys.modular.bom.params.OaApiParam;
import eve.sys.modular.bom.params.TreeBom;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.aspose.pdf.internal.imaging.internal.bouncycastle.jcajce.provider.symmetric.ARC4.Base;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Component
@Slf4j
public class SysBomOaServiceUtil {


    public static SysBomOaServiceUtil sysBomOaServiceUtil;



    @PostConstruct
    public void init(){
        sysBomOaServiceUtil = this;
        sysBomOaServiceUtil.bomLineService = this.bomLineService;
        sysBomOaServiceUtil.werkLineService = this.werkLineService;
        sysBomOaServiceUtil.productJiraService = this.productJiraService;
    }

    @Autowired
    private ISysBomLineService bomLineService;

    @Autowired
    private ISysWerkLineService werkLineService;

    @Autowired
    private ProductJiraService productJiraService;


    public MultiValueMap<String, Object> loopTreeBom2(SysBom sysBom, List<TreeBom> treeBoms) {

        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }


        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-" + (sysBom.getBomType() == 1 ? "包装BOM-新增申请-" : "电芯BOM-新增申请-") + loginName;


        List<SysBomLine> sysBomLines = sysBomOaServiceUtil.bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());



        List<SysWerkLine> werkLines =  sysBomOaServiceUtil.werkLineService
                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));
        
 
        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<String> lineNos = werkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList());

        List<SysWerkLine> extendWerkLines = sysBom.getBomIfAdd() == 1 ? com.alibaba.fastjson.JSONObject.parseArray(sysBom.getBomAddWerks(), SysWerkLine.class): new ArrayList<>();

        List<String> extendLineNos = extendWerkLines.stream().map(SysWerkLine::getLineName).distinct()
        .collect(Collectors.toList());


        /* JSONArray objects = new JSONArray();

        werkNos.forEach(w -> objects.add(w)); */


        MultiValueMap<String, Object> form = new LinkedMultiValueMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        form.set("fdTemplateId", OaApiParam.AddFdTemplateId);
        form.set("docSubject", name);
        form.set("docStatus", OaApiParam.DocStatus);

        
        form.set("docCreator", new JSONObject().set("LoginName", summitor));

        JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        formValuesJson.set("fd_31d500eb363450", new JSONObject().set("LoginName", summitor));// 姓名
        formValuesJson.set("fd_3acf6a5d0947de", String.join(",", werkNos) );// 工厂
        formValuesJson.set("fd_3acf6a5d6d51b2", treeBoms.get(0).getSapNumber() +"-" + treeBoms.get(0).getPartName() + " "+treeBoms.get(0).getPartDescription());// 产品信息



        formValuesJson.set("fd_3af68b7e68b7c6", sysBom.getBomNo());// 文件编号
        //formValuesJson.put("fd_3af68bc2088bc8", sysBom.getBomVersion());// 版本号
        formValuesJson.set("fd_3af68bc3742e9c", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");// BOM类型



        formValuesJson.set("fd_32003df664d964", "1000");// 试制数量

        formValuesJson.set("fd_3acf6aed39146c",String.join(" ",lineNos));// 适用工厂
        formValuesJson.set("fd_beizhu",sysBom.getBomIfAdd() == 1 ? "增加工厂"+String.join(" ",extendLineNos) : "" );// 扩充工厂
        formValuesJson.set("fd_3acf6a50a7ad92", "1 - 亿纬生产");// BOM用途
        formValuesJson.set("fd_3acf6a510cfc94", sdf.format(new Date()));// 起始日期
        //formValuesJson.set("fd_3288027a5844dc", new JSONObject().set("LoginName", "071716"));// 直属上司
        //formValuesJson.put("fd_34260ac31dfa36", "标准化");// 是否标准化
        //formValuesJson.set("ekp_processId", sysBom.getBomJiraNo());// JIRA进程

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        sysBomServiceUtil.putLevel(treeBoms);

        Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);
        Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

        List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel)).collect(Collectors.toList());

        Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);


        for (int i = 0; i < collect.size(); i++) {
            map1.put(collect.get(i),treeBomListMap1.get(collect.get(i)));
        }

        JSONArray jArray = new JSONArray();

        for (TreeBom key : map1.keySet()) {
            List<TreeBom> treeBoms1 = map1.get(key);
            for (int i = 0; i < treeBoms1.size(); i++) {
                jArray.add(addParam2(treeBoms1.get(i), sysBom,key));
            }
        }

        formValuesJson.set("fd_31d5011b95fb78", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c74987e5ec330",BaseUrl+"project/materials?id="+sysBom.getId());

        form.set("formValues", formValuesJson);
        return form;
    }


    public MultiValueMap<String, Object> loopEndTreeBom(SysBomEnd endBomEnd, SysBom sysBom,String code,String version,List<String> werkNos, List<String> lineNos,List<TreeBom> treeBoms) {

        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }


        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-" + "成品BOM-新增申请-" + loginName;


        MultiValueMap<String, Object> form = new LinkedMultiValueMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        form.set("fdTemplateId", OaApiParam.AddFdTemplateId);
        form.set("docSubject", name);
        form.set("docStatus", OaApiParam.DocStatus);
        form.set("docCreator", new JSONObject().set("LoginName", summitor));

        JSONObject formValuesJson = new JSONObject();

        List<String> _lineNos = new ArrayList<>();

        if (endBomEnd.getBomIfAdd() == 1) {

            List<SysWerkLine> _werkLines = com.alibaba.fastjson.JSONObject.parseArray(endBomEnd.getBomAddWerks(), SysWerkLine.class);


            _lineNos = _werkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList());
            
        }
        
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        formValuesJson.set("fd_3acf6aed39146c", String.join(" ",lineNos));// 适用工厂
        formValuesJson.set("fd_beizhu", endBomEnd.getBomIfAdd() == 1 ? "增加工厂"+String.join(" ",_lineNos) : "" );// 扩充工厂

        formValuesJson.set("fd_31d500eb363450", new JSONObject().set("LoginName", summitor));// 姓名
        formValuesJson.set("fd_3acf6a5d0947de",  String.join(",", werkNos));// 工厂
        formValuesJson.set("fd_3af68b7e68b7c6", code);// 文件编号
        //formValuesJson.put("fd_3af68bc2088bc8", version);// 版本号
        formValuesJson.set("fd_3af68bc3742e9c", "成品BOM");// BOM类型
        formValuesJson.set("fd_3acf6a5d6d51b2", treeBoms.get(0).getSapNumber() +"-" + treeBoms.get(0).getPartName() + " "+treeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_32003df664d964", "1000");// 试制数量
        formValuesJson.set("fd_3acf6a50a7ad92", "1 - 亿纬生产");// BOM用途
        formValuesJson.set("fd_3acf6a510cfc94", sdf.format(new Date()));// 起始日期
        //formValuesJson.set("fd_3288027a5844dc", new JSONObject().set("LoginName", "071716"));// 直属上司
        //formValuesJson.put("fd_34260ac31dfa36", "标准化");// 是否标准化
        //formValuesJson.put("ekp_processId", sysBom.getBomJiraNo());// JIRA进程

        SysBomServiceUtil sysBomServiceUtil = new SysBomServiceUtil();

        //sysBomServiceUtil.putLevel(treeBoms);

        Map<TreeBom, List<TreeBom>> treeBomListMap = sysBomServiceUtil.loopTreeBom1(treeBoms);
        Map<TreeBom, List<TreeBom>> map1 = new LinkedHashMap<>();

        List<TreeBom> collect = treeBomListMap.keySet().stream().sorted(Comparator.comparing(TreeBom::getLevel)).collect(Collectors.toList());

        Map<TreeBom, List<TreeBom>> treeBomListMap1 = sysBomServiceUtil.loopTreeBom1(treeBoms);


        for (int i = 0; i < collect.size(); i++) {
            map1.put(collect.get(i),treeBomListMap1.get(collect.get(i)));
        }

        JSONArray jArray = new JSONArray();

        for (TreeBom key : map1.keySet()) {
            List<TreeBom> treeBoms1 = map1.get(key);
            for (int i = 0; i < treeBoms1.size(); i++) {
                jArray.add(addParam2(treeBoms1.get(i), sysBom,key));
            }
        }

        formValuesJson.set("fd_31d5011b95fb78", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c74987e5ec330",BaseUrl+"project/materials?id="+endBomEnd.getId());

        form.set("formValues", formValuesJson);
        return form;
    }

    public JSONObject addParam(TreeBom param, SysBom sysBom) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a16ae21ec", param.getSapNumber());
        jsonObject.set("fd_31d5011b95fb78.fd_31f92419d50d58", String.valueOf(param.getPartUse()));
        jsonObject.set("fd_31d5011b95fb78.fd_31f924262eb2f0", param.getPartUnit());
        jsonObject.set("fd_31d5011b95fb78.fd_3203495198d0bc", String.valueOf(param.getPartLoss()));
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a32c08524", sysBom.getBomStartdate());
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a333f0dba", "9999-12-31");
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a339afffc", param.getPartGroup());
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a33f8a188", param.getPartPriority());
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a3461b344", param.getPartMaybe());
        jsonObject.set("fd_31d5011b95fb78.fd_320646fa906a84", param.getDesc());
        return jsonObject;
    }
    public JSONObject addParam2(TreeBom param, SysBom sysBom,TreeBom parent) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("fd_31d5011b95fb78.fd_3b12095671d3ea", param.getPartName());
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a16ae21ec", param.getSapNumber() + " - " + param.getPartDescription());
        jsonObject.set("fd_31d5011b95fb78.fd_31f92419d50d58", String.valueOf(param.getPartUse()));//sysBomChangeUtil.division(param.getPartUse(), parent.getPartUse(), 3)
        jsonObject.set("fd_31d5011b95fb78.fd_31f924262eb2f0", param.getPartUnit());
        jsonObject.set("fd_31d5011b95fb78.fd_3203495198d0bc", String.valueOf(param.getPartLoss()));
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a32c08524", sdf.format(new Date()));
        jsonObject.set("fd_31d5011b95fb78.fd_3acf6a333f0dba", "9999-12-31");
        //jsonObject.set("fd_31d5011b95fb78.fd_3acf6a339afffc", param.getPartGroup());
        //jsonObject.set("fd_31d5011b95fb78.fd_3acf6a33f8a188", param.getPartPriority());
        //jsonObject.set("fd_31d5011b95fb78.fd_3acf6a3461b344", param.getPartMaybe());
        jsonObject.set("fd_31d5011b95fb78.fd_3ad59944b90a34", parent.getSapNumber());
        jsonObject.set("fd_31d5011b95fb78.fd_3ad599452e106a", parent.getPartDescription());
        jsonObject.set("fd_31d5011b95fb78.fd_320646fa906a84", param.getDesc());
        return jsonObject;
    }

    public MultiValueMap<String, Object> loopDelWerkTreeBoms(SysBom sysBom, List<TreeBom> treeBoms,List<Long> lineIds) {


        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }

        List<SysBomLine> sysBomLines = sysBomOaServiceUtil.bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());



        List<SysWerkLine> werkLines = sysBomOaServiceUtil.werkLineService
                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

        List<SysWerkLine> delLines = sysBomOaServiceUtil.werkLineService
                .getWerks(lineIds);


        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<String> lineNos = werkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList());

        String deleteLine = " ";
        for (int i = 0; i < delLines.size(); i++) {
            deleteLine += delLines.get(i).getWerkNo()+ "-"+delLines.get(i).getLineName() + " ";
        }


        /* JSONArray objects = new JSONArray();

        werkNos.forEach(w -> objects.add(w)); */



        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-" + (sysBom.getBomType() == 1 ? "包装BOM-变更申请-" : "电芯BOM-变更申请-") + loginName;

        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();

        form.add("fdTemplateId", OaApiParam.EditFdTemplateId);
        form.add("docSubject", name);
        form.add("docStatus", OaApiParam.DocStatus);
        form.set("docCreator", new JSONObject().set("LoginName", summitor));

        JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        formValuesJson.set("fd_3b1208c6645d9e", sysBom.getBomRemark());// 原因
        formValuesJson.set("fd_31fb720d9524f4", new JSONObject().set("LoginName", summitor));// 姓名
        formValuesJson.set("fd_3acf6b61633728", String.join(",",werkNos));// 工厂
        formValuesJson.set("fd_31fb722e86e742", treeBoms.get(0).getSapNumber() +"-" + treeBoms.get(0).getPartName() + " "+treeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", sysBom.getBomNo());// 试制数量
       // formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        //formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("fd_3af8e62162e92c", String.join(" ",lineNos));// 适用工厂
        formValuesJson.set("fd_3af8e631cb6cd6", "删除工厂" +deleteLine);// 产线变更内容

        formValuesJson.set("fd_3af8e586e6d502", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");// BOM类型
        JSONArray jArray = new JSONArray();

        SysBomChangeUtil bomChangeUtil = new SysBomChangeUtil();
        List<OaAddOrEditParam> oaAddOrEditParams = null;

        oaAddOrEditParams = bomChangeUtil.deleteBoms(sysBom, treeBoms);

        oaAddOrEditParams.forEach(e->{
            jArray.add(fillParam(e));
        });

        formValuesJson.set("fd_31fb6edf666208", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c7497fe332d34",BaseUrl+"project/materials?id="+sysBom.getId());

        form.add("formValues", formValuesJson.toString());

        return form;
    }


    public MultiValueMap<String, Object> loopEndDelWerkTreeBoms(SysBomEnd sysBom,SysBom bom,String code, List<TreeBom> treeBoms,List<SysWerkLine> sysWerkLines) {


        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }

        /* String bomLines = sysBom.getBomLines();

        List<SysWerkLine> werkLines = sysBomOaServiceUtil.werkLineService
                .getWerks(com.alibaba.fastjson.JSONArray.parseArray(bomLines,Long.class));*/

        List<String> werkNos = sysWerkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<String> lineNos = sysWerkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList()); 

        String deleteLine = " ";
        for (int i = 0; i < sysWerkLines.size(); i++) {
            deleteLine += sysWerkLines.get(i).getWerkNo()+ "-"+sysWerkLines.get(i).getLineName() + " ";
        }

        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-成品BOM-变更申请-" + loginName;

        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();

        form.add("fdTemplateId", OaApiParam.EditFdTemplateId);
        form.add("docSubject", name);
        form.add("docStatus", OaApiParam.DocStatus);
        form.set("docCreator", new JSONObject().set("LoginName", summitor));

        JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        //formValuesJson.set("fd_3b1208c6645d9e", sysBom.get());// 原因
        formValuesJson.set("fd_31fb720d9524f4", new JSONObject().set("LoginName", summitor));// 姓名
        formValuesJson.set("fd_3acf6b61633728", String.join(",",werkNos));// 工厂
        formValuesJson.set("fd_31fb722e86e742", treeBoms.get(0).getSapNumber() +"-" + treeBoms.get(0).getPartName() + " "+treeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", code);// 试制数量
       // formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        //formValuesJson.set("fd_38d0ba42f560de", new JSONObject().set("LoginName", "071716"));// 直属上司
        //formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("fd_3af8e62162e92c",  String.join(",",lineNos));// 适用工厂
        formValuesJson.set("fd_3af8e631cb6cd6", "删除工厂 " +deleteLine);// 产线变更内容

        formValuesJson.set("fd_3af8e586e6d502", "成品BOM");// BOM类型
        JSONArray jArray = new JSONArray();

        SysBomChangeUtil bomChangeUtil = new SysBomChangeUtil();
        List<OaAddOrEditParam> oaAddOrEditParams = null;

        oaAddOrEditParams = bomChangeUtil.deleteEndBoms(sysBom, treeBoms);

        oaAddOrEditParams.forEach(e->{
            jArray.add(fillParam(e));
        });

        formValuesJson.set("fd_31fb6edf666208", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c7497fe332d34",BaseUrl+"project/materials?id="+sysBom.getId());

        form.add("formValues", formValuesJson.toString());

        return form;
    }

    public MultiValueMap<String, Object> loopNewEndTreeBoms(SysBomEnd sysBom, List<TreeBom> newTreeBoms, List<TreeBom> oldTreeBoms) {


        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }


        /* List<SysBomLine> sysBomLines = sysBomOaServiceUtil.bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build()); */



        List<SysWerkLine> werkLines = sysBomOaServiceUtil.werkLineService
                .getWerks(JSON.parseArray(sysBom.getBomLines(), Long.class));

        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<String> lineNos = werkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList());





        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-成品BOM-变更申请-" + loginName;

        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        form.add("fdTemplateId", OaApiParam.EditFdTemplateId);
        form.add("docSubject", name);
        form.add("docStatus", OaApiParam.DocStatus);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("LoginName", summitor);
        form.set("docCreator", jsonObject);


        JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        formValuesJson.set("fd_3b1208c6645d9e", "成品BOM修订");// 原因
        formValuesJson.set("fd_31fb720d9524f4", jsonObject);// 姓名
        formValuesJson.set("fd_3acf6b61633728", String.join(",",werkNos));// 工厂
        formValuesJson.set("fd_31fb722e86e742", newTreeBoms.get(0).getSapNumber() +"-" + newTreeBoms.get(0).getPartName() + " "+newTreeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", sysBom.getBomPartName());// 试制数量
        // formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        //formValuesJson.set("fd_38d0ba42f560de", new JSONObject().set("LoginName", summitor));// 直属上司
        //formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("fd_3af8e62162e92c",  String.join(" ",lineNos));// 适用工厂
        formValuesJson.set("fd_3af8e631cb6cd6", " ");// 产线变更内容

        formValuesJson.set("fd_3af8e586e6d502", "成品BOM");// BOM类型


       /* JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_3acf6b61633728", "4260");// 工厂
        formValuesJson.set("fd_31fb722e86e742", newTreeBoms.get(0).getSapNumber() +"-" + newTreeBoms.get(0).getPartName() + " "+newTreeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", "1000");// 试制数量
        formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        formValuesJson.set("fd_38d0ba42f560de", new JSONObject().set("LoginName", "071716"));// 直属上司
        formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("ekp_processId", sysBom.getBomJiraNo());// JIRA进程*/
        JSONArray jArray = new JSONArray();

        SysBomEndChangeUtil sysBomChangeUtil = new SysBomEndChangeUtil();
        Map<String, TreeBom> flagTreeMap = new HashMap<>();
        Map<String, TreeBom> mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms,flagTreeMap);
        List<TreeBom> newBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, newTreeBoms, mapTreeBoms, newBoms,flagTreeMap);
        oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));

        oaAddOrEditParams = oaAddOrEditParams.stream().filter(e-> null != e).collect(Collectors.toList());

        oaAddOrEditParams.forEach(e->{
            jArray.add(fillParam(e));
        });

        formValuesJson.set("fd_31fb6edf666208", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c7497fe332d34",BaseUrl+"project/materials?id="+sysBom.getId());
        
        form.add("formValues", formValuesJson.toString());

        return form;
    }


    public MultiValueMap<String, Object> loopNewTreeBoms(SysBom sysBom, List<TreeBom> newTreeBoms, List<TreeBom> oldTreeBoms) {


        ProjectDetail detail = null;

        try {
            
            detail = sysBomOaServiceUtil.productJiraService.getProjectDetail(
                    DocParams.builder().issueId(sysBom.getBomIssueId()).build());
        } catch (Exception e) {
            throw new ServiceException(500, "服务重启，请重新登录");
        }


        List<SysBomLine> sysBomLines = sysBomOaServiceUtil.bomLineService
                .getList(SysBomLineParam.builder().bomId(sysBom.getId()).build());



        List<SysWerkLine> werkLines = sysBomOaServiceUtil.werkLineService
                .getWerks(sysBomLines.stream().map(SysBomLine::getLineId).collect(Collectors.toList()));

        List<String> werkNos = werkLines.stream().map(SysWerkLine::getWerkNo).distinct()
                .collect(Collectors.toList());

        List<String> lineNos = werkLines.stream().map(SysWerkLine::getLineName).distinct()
                .collect(Collectors.toList());





        //产品型号-电芯BOM/包装BOM-新增/变更申请-申请人

        String loginName = LoginContextHolder.me().getSysLoginUser().getName();
        String summitor = LoginContextHolder.me().getSysLoginUserAccount();

        String name = detail.getProductProjectName()+"-" + (sysBom.getBomType() == 1 ? "包装BOM-变更申请-" : "电芯BOM-变更申请-") + loginName;

        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        form.add("fdTemplateId", OaApiParam.EditFdTemplateId);
        form.add("docSubject", name);
        form.add("docStatus", OaApiParam.DocStatus);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("LoginName", summitor);
        form.set("docCreator", jsonObject);


        JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_xmdj", detail.getProjectLevelName());
        formValuesJson.set("fd_3b1208c6645d9e", sysBom.getBomRemark());// 原因
        formValuesJson.set("fd_31fb720d9524f4", jsonObject);// 姓名
        formValuesJson.set("fd_3acf6b61633728", String.join(",",werkNos));// 工厂
        formValuesJson.set("fd_31fb722e86e742", newTreeBoms.get(0).getSapNumber() +"-" + newTreeBoms.get(0).getPartName() + " "+newTreeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", sysBom.getBomNo());// 试制数量
        // formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        //formValuesJson.set("fd_38d0ba42f560de", new JSONObject().set("LoginName", summitor));// 直属上司
        //formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("fd_3af8e62162e92c",  String.join(" ",lineNos));// 适用工厂
        formValuesJson.set("fd_3af8e631cb6cd6", " ");// 产线变更内容

        formValuesJson.set("fd_3af8e586e6d502", sysBom.getBomType() == 1 ? "包装BOM" : "电芯BOM");// BOM类型


       /* JSONObject formValuesJson = new JSONObject();
        formValuesJson.set("fd_3acf6b61633728", "4260");// 工厂
        formValuesJson.set("fd_31fb722e86e742", newTreeBoms.get(0).getSapNumber() +"-" + newTreeBoms.get(0).getPartName() + " "+newTreeBoms.get(0).getPartDescription());// 产品信息
        formValuesJson.set("fd_3acf6b91bddbf2", "1000");// 试制数量
        formValuesJson.set("fd_31fda41c998222", "1 - 亿纬生产");// BOM适用
        formValuesJson.set("fd_38d0ba42f560de", new JSONObject().set("LoginName", "071716"));// 直属上司
        formValuesJson.set("fd_38d0bd05f1a3ea", "标准化");// 是否标准化
        formValuesJson.set("ekp_processId", sysBom.getBomJiraNo());// JIRA进程*/
        JSONArray jArray = new JSONArray();

        SysBomChangeUtil sysBomChangeUtil = new SysBomChangeUtil();
        Map<String, TreeBom> flagTreeMap = new HashMap<>();
        Map<String, TreeBom> mapTreeBoms = sysBomChangeUtil.loopOldTreeBoms(oldTreeBoms,flagTreeMap);
        List<TreeBom> newBoms = new ArrayList<>();
        List<OaAddOrEditParam> oaAddOrEditParams = sysBomChangeUtil.loopNewTreeBoms(sysBom, newTreeBoms, mapTreeBoms, newBoms,flagTreeMap);
        oaAddOrEditParams.addAll(sysBomChangeUtil.loopTreeBom(sysBom, newBoms, mapTreeBoms));

        Map<String,List<OaAddOrEditParam>> paramsMap = oaAddOrEditParams.stream()
        .filter(e -> null != e)
        .filter(e->e.getFlag().equals("新增") || e.getFlag().equals("删除"))
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(), 
                Collectors.toList()
            )
        )
        .entrySet().stream()
        .filter(e->e.getValue().size() > 1)
        .map(val->val.getValue())
        .flatMap(val->val.stream())
        .collect(
            Collectors.groupingBy(
                item->item.getBuildKey(),
                Collectors.mapping(item->OaAddOrEditParam.builder().id(item.getId()).flag(item.getFlag()).build(), Collectors.toList())
            )
        );

        List<String> ids = new ArrayList<>();

        for (Map.Entry<String,List<OaAddOrEditParam>> entry : paramsMap.entrySet()) {
            List<OaAddOrEditParam> delList = entry.getValue().stream().filter(e->e.getFlag().equals("删除")).collect(Collectors.toList());
            List<OaAddOrEditParam> addList = entry.getValue().stream().filter(e->e.getFlag().equals("新增")).collect(Collectors.toList());
            int minCount = delList.size() < addList.size() ? delList.size() : addList.size();
            for (int i = 0,j = minCount; i < j; i++) {
                ids.add(delList.get(i).getId());
                ids.add(addList.get(i).getId());
            }
        }

        oaAddOrEditParams = oaAddOrEditParams.stream().filter(e -> null != e).filter(e-> ids.indexOf(e.getId()) < 0).collect(Collectors.toList());

        oaAddOrEditParams.forEach(e->{
            jArray.add(fillParam(e));
        });

        formValuesJson.set("fd_31fb6edf666208", jArray);

        String BaseUrl =  ConstantContextHolder.getSysConfigWithDefault("service_url",String.class,"");

        formValuesJson.set("fd_3c7497fe332d34",BaseUrl+"project/materials?id="+sysBom.getId());
        
        form.add("formValues", formValuesJson.toString());

        return form;
    }

    private JSONObject fillParam(OaAddOrEditParam param){
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("fd_31fb6edf666208.fd_3b1208f0769866", param.getPartName());
        jsonObject.set("fd_31fb6edf666208.fd_3acf6bf0a7fa88", param.getFlag());
        jsonObject.set("fd_31fb6edf666208.fd_3acf6bf12820aa", param.getSapNumber()+ " - "+ param.getPartDescription());
        jsonObject.set("fd_31fb6edf666208.fd_38d0b8cfcdc3de", String.valueOf(param.getPrePartUse()));//String.valueOf(param.getPrePartUse())
        jsonObject.set("fd_31fb6edf666208.fd_31fb785811eb06", String.valueOf(param.getPartUse()));//param.getPartUse())
        jsonObject.set("fd_31fb6edf666208.fd_31fb86f297d2e8", param.getPartUnit());
        jsonObject.set("fd_31fb6edf666208.fd_32042c60dff0e2", String.valueOf(param.getPartLoss()));
        jsonObject.set("fd_31fb6edf666208.fd_3acf6c09884460", param.getBomStartdate());
        jsonObject.set("fd_31fb6edf666208.fd_31fb79a9e05b72", param.getDesc());
        jsonObject.set("fd_31fb6edf666208.fd_3af8e7f63b2ae4", param.getMSapNumber());//成品/半成品代码
        jsonObject.set("fd_31fb6edf666208.fd_3af8e7f72c8686", param.getMPartDescription());//成品/半成品名称
        return jsonObject;
    }

}
