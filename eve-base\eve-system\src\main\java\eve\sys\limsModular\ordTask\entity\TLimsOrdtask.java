package eve.sys.limsModular.ordTask.entity;

import cn.hutool.core.map.CaseInsensitiveLinkedMap;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("T_LIMS_ORDTASK")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLimsOrdtask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人ID
     */
    private String createdbyid;

    /**
     * 创建人名称
     */
    private String createdbyname;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdtime;

    /**
     * 创建人部门ID
     */
    private String createdbyorgid;

    /**
     * 创建人部门名称
     */
    private String createdbyorgname;

    /**
     * 序号
     */
    private Long sorter;

    /**
     * 委托单id
     */
    private Long folderid;

    /**
     * 试验项目方法ID
     */
    private Long testmethodid;

    /**
     * 测试状态
     */
    private String teststatus;

    /**
     * 测试依据ID
     */
    private Long basicid;

    /**
     * 是否委外
     */
    private String outsourceflag;

    /**
     * 最大电流
     */
    private String maxcurrent;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 预估金额
     */
    private String estimatedcost;

    /**
     * 测试预估工时
     */
    private String estimatedhour;

    /**
     * 状态
     */
    private String status;

    /**
     * 测试责任人ID
     */
    private String leadercode;

    /**
     * 测试责任人
     */
    private String leader;

    /**
     * 测试人
     */
    private String tester;

    /**
     * 测试人ID
     */
    private String testercode;

    /**
     * 参与人
     */
    private String participator;

    /**
     * 参与人ID
     */
    private String participatorcode;

    /**
     * 是否已排程
     */
    private String scheduleflag;

    /**
     * 标准成本单位
     */
    private String standardcostunit;

    /**
     * 试验用样品送样人
     */
    private String sender;

    /**
     * 所属试验室
     */
    private String dept;

    /**
     * 所属试验室名称
     */
    private String deptname;

    /**
     * 是否已生成报告
     */
    private String reported;

    /**
     * 测试单价
     */
    private Long price;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 检测结论
     */
    private String testconclusion;

    /**
     * 生成报告中的异常信息
     */
    private String reporterrorreason;

    /**
     * 准备时长
     */
    private String readyhour;

    /**
     * 分解员
     */
    private String resolver;

    /**
     * 分解员ID
     */
    private String resolverid;

    /**
     * 检测步骤
     */
    private String ordtaskteststep;

    /**
     * 综合报告ID
     */
    private Long reportcompositeid;

    /**
     * 拼单标记
     */
    private String shareflag;

    /**
     * 单号
     */
    private String taskno;

    /**
     * 测试目的
     */
    private String ordtasktestpurpose;

    /**
     * 拒绝理由
     */
    private String outsourcerejectreason;

    /**
     * 委外拒绝人
     */
    private String outsourcereject;

    /**
     * 拒绝时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outsourcerejecttime;

    /**
     * 委外拒绝人ID
     */
    private String outsourcerejectid;

    /**
     * 试验项目的附件
     */
    private Long ordtaskfileid;

    /**
     * 报告中样品图片一行数量
     */
    private Long reportorderimgnum;

    /**
     * 业务使用标识
     */
    private String businessflag;

    /**
     * 报告数据源
     */
    private String reportdatasource;

    /**
     * 试验项目成本
     */
    private String ordtaskcost;

    /**
     * 判定标准
     */
    private String judgestandard;

    /**
     * 检测记录
     */
    private String ordtasktestrecord;

    /**
     * 排程时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scheduletime;

    /**
     * 标准工时
     */
    private String standardhour;

    /**
     * 判定依据
     */
    private String judgebasis;

    /**
     * 费用类型
     */
    private String expensetype;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planstarttime;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planendtime;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realstarttime;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realendtime;

    /**
     * 标准编码
     */
    private String methodcode;

    /**
     * 标准名称
     */
    private String methodname;

    /**
     * 测试项目编码
     */
    private String testcode;

    /**
     * 测试项目名称
     */
    private String testname;

    /**
     * 委托单号
     */
    private String folderno;

    /**
     * 是否温变
     */
    private String tempchangeflag;

    /**
     * 附加时长
     */
    private String additionaltime;

    /**
     * 时间单位
     */
    private String timeunit;

    private String firstcode;

    private String firstcategory;

    private String secondcode;

    private String secondcategory;

    /**
     * 排程异常信息
     */
    private String errormsg;

    /**
     * 测试步骤
     */
    private String teststep;

    /**
     * 待处理人
     */
    private String todoperson;

    /**
     * 检测时长
     */
    private String testduration;

    /**
     * 检测内容
     */
    private String testcontent;

    /**
     * 是否委外
     */
    private String initialoutsourceflag;

    /**
     * 委外实验室
     */
    private String outsourcelabname;

    /**
     * 委外费用
     */
    private String outsourcecost;

    /**
     * 是否需要报告
     */
    private String ordtaskreportflag;

    /**
     * 意见
     */
    private String idea;

    /**
     * 解释
     */
    private String explain;

    /**
     * 测试项目别名
     */
    private String alias;

    /**
     * 测试机构
     */
    private String testorg;

    /**
     * 测试机构报价
     */
    private String testorgoffer;

    /**
     * 草本 or 正本
     */
    private String originalflag;

    /**
     * 认证类型
     */
    private String certifytype;

    /**
     * 认证开始时间
     */
    private String certifystarttime;

    /**
     * 认证结束时间
     */
    private String certifyendtime;

    /**
     * 结果修改标志
     */
    private String resultmodifyflag;

    /**
     * 出报告时间
     */
    private String reporttime;

    /**
     * 报告状态
     */
    private String reportstatus;

    /**
     * 超期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testoverduetime;

    /**
     * 报告编制人工号
     */
    private String reportercode;

    /**
     * 报告编制人
     */
    private String reporter;

    /**
     * 测试地点ID
     */
    private Long testlocationid;

    /**
     * 测试地点
     */
    private String testlocation;

    /**
     * 精密实验室新增字段:测试参数
     */
    private String testparameter;

    /**
     * 启动条码唯一ID
     */
    private String startcodeuuid;

    /**
     * 温度
     */
    @TableField(exist = false)
    private String tem;

    /**
     * soc
     */
    @TableField(exist = false)
    private String soc;

    /**
     * 存储时间
     */
    @TableField(exist = false)
    private String totalDay;

    /**
     * 项目测试数量
     */
    @TableField(exist = false)
    private String testnumber;

    /**
     * 下级属性：第四实验室任务分配树形数据的展示
     */
    @TableField(exist = false)
    private List<Map<String, Object>> children;

    /**
     * 是否是中间层父类：第四实验室任务分配树形数据的展示
     */
    @TableField(exist = false)
    private Boolean isMidParent;

    @TableField(exist = false)
    private Map<String, String> otherProperties = new CaseInsensitiveLinkedMap();;

    public Map<String, String> getOtherProperties() {
        return this.otherProperties;
    }

    public void setOtherProperties(Map<String, String> otherProperties) {
        this.otherProperties.putAll(otherProperties);
    }

    public void setOtherPropertiesItem(String key, String value) {
        this.otherProperties.put(key, value);
    }

    public String getOtherPropertiesItem(String key) {
        return (String)this.otherProperties.get(key);
    }
}
