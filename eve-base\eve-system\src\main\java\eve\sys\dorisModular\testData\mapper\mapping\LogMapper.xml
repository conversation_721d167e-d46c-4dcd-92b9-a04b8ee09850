<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.dorisModular.testData.mapper.LogMapper">

    <select id="selectLogPage" resultType="eve.sys.dorisModular.testData.entity.Log">
        SELECT r.*
        FROM sync_data.log r,
             ( SELECT record_id, flow_id FROM sync_data.log ${ew.customSqlSegment} ORDER BY absolute_time ASC LIMIT #{start}, #{pageSize} ) temp
        WHERE r.record_id = temp.record_id and r.flow_id = temp.flow_id
        ORDER BY r.absolute_time ASC;
    </select>


</mapper>
