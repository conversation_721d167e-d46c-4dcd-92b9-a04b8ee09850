package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sync_data.log")
public class Log implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据关联id
     */
//    @JsonProperty("flow_id")
    @ApiModelProperty("数据关联id")
    private String flowId;

    /**
     * 记录id
     */
//    @JsonProperty("record_id")
    @ApiModelProperty("记录id")
    private Long recordId;

    /**
     * 类型
     */
//    @JsonProperty("record_type")
    @ApiModelProperty("类型")
    private String recordType;

    /**
     * 描述
     */
//    @ApiModelProperty("描述")
    private String description;

    /**
     * 数据生成时间
     */
//    @JsonProperty("absolute_time")
//    @ApiModelProperty("数据生成时间")
    private String absoluteTime;

    /**
     * 数据生成时间
     */
//    @JsonProperty("create_time")
    @ApiModelProperty("数据上传时间")
    private String createTime;

    /**
     * 设备编号
     */
    @ApiModelProperty("设备编号")
//    @JsonProperty("unit_num")
    private String unitNum;

    /**
     * 通道编号
     */
    @ApiModelProperty("通道编号")
//    @JsonProperty("channel_id")
    private String channelId;

    /**
     * 条码
     */
    @ApiModelProperty("条码")
    private String barcode;






}
