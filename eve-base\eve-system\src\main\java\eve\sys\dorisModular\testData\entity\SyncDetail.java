package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sync_data.sync_detail")
public class SyncDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据关联id
     */
    private String flowId;

    /**
     * 设备名称 盛弘、新威等
     */
    private String equName;

    /**
     * 查询参数
     */
    private String queryParam;

    /**
     * 测试项目名称
     */
    private String testName;

    /**
     * 电芯编码
     */
    private String cellCode;


    /**
     * 数据同步状态 1 同步完成不需要再同步
     */
    private Integer syncStatus;

    /**
     * 详细数据导入状态 数据同步状态 1 同步完成不需要再同步
     */
    private Integer recordSyncStatus;

    /**
     * 同步时间
     */
    private Date syncDate;

    /**
     * 测试开始时间
     */
    private LocalDateTime startDate;

    /**
     * 测试结束时间/最新数据时间
     */
    private LocalDateTime endDate;

    /**
     * lims表id
     */
    private Long primaryId;


}
