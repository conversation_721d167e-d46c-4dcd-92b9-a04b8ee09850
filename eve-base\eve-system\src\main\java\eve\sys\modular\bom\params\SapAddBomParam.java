package eve.sys.modular.bom.params;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SapAddBomParam implements Serializable{

    @JSONField(name = "IV_CTYPE")
    private String IV_CTYPE;//是否试产

    @JSONField(name = "IV_UPMEN")
    private int IV_UPMEN;//试制数量上限

    @J<PERSON>NF<PERSON>(name = "IV_WERKS")
    private String IV_WERKS;//工厂

    @JSONField(name = "IV_STLAL")
    private String IV_STLAL;// 版本

    @JSONField(name="IV_STKTX")//可选文本
    private String IV_STKTX;

    @JSONField(name = "IT_DATA")
    private List<SapAddBomItOrEtParam> IT_DATA;//输入表
    
    public boolean noItData(){
        return IT_DATA == null || IT_DATA.isEmpty();
    }

    public SapAddBomParam newClone(){
        SapAddBomParam sapAddBomParam = new SapAddBomParam(
            this.IV_CTYPE,
            this.IV_UPMEN,
            this.IV_WERKS,
            this.IV_STLAL,
            this.IV_STKTX,
            new ArrayList<>()
        );
        for (SapAddBomItOrEtParam item : this.IT_DATA) {
            sapAddBomParam.getIT_DATA().add(item.newClone());
        }
        return sapAddBomParam;
    }

    public List<SapAddBomParam> flat2List(){
        List<SapAddBomParam> list = new ArrayList<>();
        for (SapAddBomItOrEtParam item : this.IT_DATA) {
            SapAddBomParam sapAddBomParam = new SapAddBomParam(
                this.IV_CTYPE,
                this.IV_UPMEN,
                this.IV_WERKS,
                this.IV_STLAL,
                this.IV_STKTX,
                new ArrayList<>()
            );
            sapAddBomParam.getIT_DATA().add(item);
            list.add(sapAddBomParam);
        }
        return list;
    }
}
