package eve.sys.modular.bom.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import eve.core.pojo.base.entity.BaseEntity;
import eve.sys.modular.part.entity.SysPart;
import eve.sys.modular.werkline.entity.SysWerkLine;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
@Data
@TableName("SYS_BOM")
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysBom extends BaseEntity {

    //@TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    private String bomName;

    private String bomVersion;

    private String bomTransport;

    private Long bomSourceId;

    private String bomRemark;

    private Long bomStatus;

    private String bomProductType;
    

    //private Long bomSourceId;

    //private String bomFactory;

    private String bomData;

    private String bomNo;

    private Long bomIssueId;

    private String bomUse;

    private String bomCtype;

    private Integer bomUpmen;

    private String bomStartdate;

    private Integer productState;

    private Integer bomType;

    private Long fileId;
    private Long childIssueId;
    private String bomJiraNo;
    private String oaId;

    private String manager;
    private String bomCode;
    private String bomPartName;
    private String productStage;//产品阶段
    private Long jiraId;
    //JIRA_ID使用状态  1：已使用  0 未使用
    private Integer jiraIdStatus;

    private Integer bomIfAdd;

    private String bomAddWerks;

    //增加工厂减少工厂原因
    private String lineRemark;
    //提交人
    private String summitor;

    @ApiModelProperty("审核人英文,隔开")
    private String reviewer;


    @ApiModelProperty("批准人")
    private String approver;


    @TableField(exist = false)
    private Long bomEndCount;

    @TableField(exist = false)
    private List<Long> lines;

    @TableField(exist = false)
    private List<SysWerkLine> werkLines;

    @TableField(exist = false)
    private Integer canImport;

    @TableField(exist = false)
    private List<SysPart> parts;

    @TableField(exist = false)
    private List<String> werks;

    @TableField(exist = false)
    private Boolean isEnd;

    private String productProjectName;

    /**
     * bom号  01  02 03  导入时可填写
     */
    private String num;


    private Integer adminComfirm;

    /* 变更分类 */
    private Integer alterType;

    @TableField(exist = false)
    private Long cellBomId;

    @TableField(exist = false)
    private Long packBomId;

    @TableField(exist = false)
    private Long count;

    @TableField(exist = false)
    private Integer lastProductState;

    @TableField(exist = false)
    private Boolean isCheck;

    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sapDate;

    @TableField(exist = false)
    private List<Long> packIds;
}
