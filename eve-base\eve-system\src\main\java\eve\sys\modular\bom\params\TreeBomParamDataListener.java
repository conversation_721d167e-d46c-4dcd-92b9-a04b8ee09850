package eve.sys.modular.bom.params;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import cn.hutool.core.util.ObjectUtil;
import eve.sys.modular.finance.bid.service.IDeviceBidService;
import eve.sys.modular.product.entity.ProductManager;

public class TreeBomParamDataListener extends AnalysisEventListener<TreeBomParam>{

    private Map<String,Long> mapById = new HashMap<>();


    public TreeBomParamDataListener() {
    }

    @Override
    public void invoke(TreeBomParam data, AnalysisContext context) {
        if (ObjectUtil.isNotEmpty(data.getLevels())) {
            mapById.put(data.getId(), data.getLevels());
        }
    }

    public Map<String,Long> getData(){
        return mapById;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        
    }
}
