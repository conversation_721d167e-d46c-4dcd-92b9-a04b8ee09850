package eve.sys.limsModular.temGradient.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.limsModular.temGradient.service.ITLimsTemGradientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 温度梯度 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@RestController
@RequestMapping("/tLimsTemGradient")
public class TLimsTemGradientController {
    @Autowired
    private ITLimsTemGradientService tLimsTemGradientService;

    @PostMapping("/getByFolderIdAndOrdtaskId/{folderId}/{ordtaskId}")
    @BusinessLog(title = "G26日历寿命报告建模_根据测试项目ID获取中检信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getByOrderIdAndOrdtaskId(@PathVariable Long folderId, @PathVariable Long ordtaskId) {
        return new SuccessResponseData(tLimsTemGradientService.getByOrderIdAndOrdtaskId(folderId, ordtaskId));
    }
}

