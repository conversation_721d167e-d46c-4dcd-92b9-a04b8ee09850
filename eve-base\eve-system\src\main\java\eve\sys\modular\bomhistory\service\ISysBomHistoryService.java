package eve.sys.modular.bomhistory.service;

import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBomVo;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.param.SysBomHistoryParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
public interface ISysBomHistoryService extends IService<SysBomHistory> {
    Long add(SysBomHistoryParam param);
    SysBomHistory getLastOne(Long bomId);
    SysBomHistory getCountByBomId(Long bomId);
    PageResult<SysBomHistory> page(Long bomId);

    List<SysBomVo> pageB(SysBomParam param);

    SysBomHistory getLastHistory(Long bomId);
    List<SysBomHistory> getHistoryGroupByBomId(List<Long> bomIds);
    List<SysBomHistory> getLastHistoryGroupByBomId();
}
