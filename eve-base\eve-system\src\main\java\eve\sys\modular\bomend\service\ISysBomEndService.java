package eve.sys.modular.bomend.service;

import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomhistory.entity.SysBomHistory;

import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.sys.modular.bom.params.OaAddOrEditParam;
import eve.sys.modular.werkline.entity.SysWerkLine;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
public interface ISysBomEndService extends IService<SysBomEnd> {
    Long saveBomEnd(SysBomEnd param);
    Long editBomEnd(SysBomEnd param);
    List<SysBomEnd> getList(SysBomEnd param);
    void relate(String token, SysBomEnd param) throws IOException;
    SysBomEnd getBomEndError(SysBomEnd param);
    //void updateForAddWerks(SysBomEnd param);
    PageResult<SysBomEnd> page(SysBomEnd param);
    PageResult<SysBom> bomPage(SysBomParam param);
    List<SysBomEnd> getBomPacks(List<Long> bomIds);

    void deleteWerk2BOM2Sap(SysBomEnd sysBom);

    void delBomEnd(SysBomEnd param);

    void addWerk2Bom2Sap(SysBomEnd end) throws ServiceException;

    void addBom2Sap(SysBomEnd end) throws ServiceException;
    void sapImport(SysBomEnd param) throws ServiceException;
    void addBomLines(SysBomEnd param);
    JSONObject getBomPackLineIds(SysBomEnd param);
    JSONObject getBomLineIds(SysBomEnd param);
    void delBomLines(SysBomEnd param);
    void addHistory(SysBomEnd end, List<OaAddOrEditParam> oaAddOrEditParams, List<SysWerkLine> _werkLines,Integer addwerk);
    /* List<Long> getListNotInIssueId(SysBomEnd param);
    List<Long> getBomIdsByPackId(SysBomEnd param); */
    void withDraw(SysBomParam param);
    void editBom2Sap(SysBomEnd sysBom, SysBomHistory sysBomHistory, List<String> werkNos,
            List<SysWerkLine> werkLines)
            throws ServiceException ;
}
