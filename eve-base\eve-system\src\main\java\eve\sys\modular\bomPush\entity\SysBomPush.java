package eve.sys.modular.bomPush.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

/**
 * <p>
 * bom推送状态
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@Getter
@Setter
@TableName("SYS_BOM_PUSH")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysBomPush extends BaseEntity {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 描述
     */
    private String describe;

    /**
     * 推送系统
     */
    private String sys;

    /**
     * 推送状态 0 提交审批  2 OA已审核 3 已提交SAP 4 JIRA已受控
     */
    private Integer pushStatus;

    /**
     * bomId
     */
    private Long bomId;

    /**
     * 状态（字典 0正常 1冻结 2删除）
     */
    private Integer status;


    /**
     * 样品阶段
     */
    private String stage;

    /**
     * 操作人姓名
     */
    private String operator;

    /**
     *
     */
    private String url;


    /**
     * 流程id
     */
    private Long processId;

     /**
     * 文件id
     */
    private Long fileId;

    /**
     * 提交人id
     */
    private Long submitterId;

    /**
     * 提交人
     */
    private String submitter;


    /**
     * 项目名称
     */
    private String name;


    /**
     * bom类型
     */
    private String bomType;


    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 9  -1 : BOM
     * 0: MI
     * 11: 零部件图纸
     * 12:包装图纸
     * 13:电芯图纸
     * 14:工序图纸
     * 2:特殊特性清单
     * 3：测试报告
     * 4：产品规格书
     * 5：产品规范
     * 6:工艺流程图
     * 7：包装标准和规范
     * 8：原材料技术要求
     **/
    private Integer type;

    //变更原因/新增原因
    private String remark;

    //阶段
    private Long productState;

    private String change;

    private String oaLink;


}
