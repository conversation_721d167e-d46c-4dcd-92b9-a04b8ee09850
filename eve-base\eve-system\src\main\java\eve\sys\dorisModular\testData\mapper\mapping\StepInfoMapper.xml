<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.dorisModular.testData.mapper.StepinfoMapper">

        <insert id="batchInsertStepInfos" parameterType="java.util.List">
            INSERT INTO sync_data.stepinfo ( step_id, step_name, flow_id, absolute_time, step_para, cutoff_condition, record_condition, unit_num, channel_id, barcode, sync_date, create_time ) VALUES
            <foreach collection="list" item="item" index="index" separator=",">
                (
                #{item.stepId},
                #{item.stepName},
                #{item.flowId},
                #{item.absoluteTime},
                #{item.stepPara},
                #{item.cutoffCondition},
                #{item.recordCondition},
                #{item.unitNum},
                #{item.channelId},
                #{item.barcode},
                #{item.syncDate},
                #{item.createTime}
                )
            </foreach>
        </insert>




</mapper>
