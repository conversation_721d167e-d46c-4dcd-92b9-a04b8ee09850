package eve.sys.modular.bomend.entity;

import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import eve.core.pojo.base.entity.BaseEntity;
import eve.sys.modular.bom.params.BomSapProof;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("SYS_BOM_END")
public class SysBomEnd extends BaseEntity {

    
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 电芯bom的id
     */
    private Long bomId;

    /**
     * 包装bom的id
     */
    private Long bomPackId;

    /**
     * 成品代号
     */
    private String bomCode;

    /**
     * 成品名称
     */
    private String bomPartName;

    /**
     * sap返回的bom版本
     */
    private String bomSapVersion;

    /**
     * 关联的产线
     */
    private String bomLines;

    //0未关联 1 审核中 2 关联成功 3 关联失败 4 新增工厂 7 驳回
    private int bomRelateStatus;

    private String bomData;

    private String addFails;

    private Long bomIssueId;

    private String adds;

    private String erros;

    private String oaId;

    private Integer bomIfAdd;

    private String bomAddWerks;

    private String edits;

    private String editFails;

    private String bomChange;

    @TableField(exist = false)
    private String bomVersion;

    @TableField(exist = false)
    private String sapVersion;

    /**
     * 最新变更的电芯bom的id
     */
    @TableField(exist = false)
    private Long lastBomId;

    /**
     * 最新变更的包装bom的id
     */
    @TableField(exist = false)
    private Long lastBomPackId;

    @TableField(exist = false)
    private Boolean isCheck;

    @TableField(exist = false)
    private String cellBomCode;

    @TableField(exist = false)
    private String packBomCode;

    @TableField(exist = false)
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sapDate;

    @TableField(exist = false)
    private List<BomSapProof> proofs;

}
