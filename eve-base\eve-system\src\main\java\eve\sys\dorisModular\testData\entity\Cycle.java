package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sync_data.cycle")
public class Cycle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 循环id
     */
    private Long cycleId;

    /**
     * 数据关联id
     */
    private String flowId;

    /**
     * 数据绝对时间
     */
    private String absoluteTime;

    /**
     * 充电容量
     */
    private BigDecimal chargeCapacity;

    /**
     * 放电容量
     */
    private BigDecimal dischargeCapacity;

    /**
     * 充电能量
     */
    private BigDecimal chargeEnergy;

    /**
     * 放电能量
     */
    private BigDecimal dischargeEnergy;

    /**
     * 充放电效率
     */
    private BigDecimal chargeCapacityRatio;

    /**
     * 最大温度
     */
    private BigDecimal maxTemp;

    /**
     * 最小温度
     */
    private BigDecimal minTemp;

    /**
     * 充电时间
     */
    private String chargeTime;

    /**
     * 放电时间
     */
    private String dischargeTime;

    /**
     * 设备编号
     */
    private String unitNum;

    /**
     * 通道编号
     */
    private String channelId;

    /**
     * 测试编码
     */
    private String barcode;

    /**
     * 同步到doris的时间
     */
    private Date syncDate;

    /**
     * 数据上传时间
     */
    private String createTime;


}
