package eve.sys.modular.aiQuestion.controller;


import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.aiQuestion.entity.AiQuestion;
import eve.sys.modular.aiQuestion.service.IAiQuestionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@RequestMapping("/open/aiQuestion")
public class AiQuestionController {

    @Resource
    private IAiQuestionService questionService;

    @PostMapping("/add")
    public ResponseData edit(@RequestBody AiQuestion param) {
        questionService.save(param);
        return new SuccessResponseData(param.getId());
    }

    @PostMapping("/list")
    public ResponseData list(@RequestBody AiQuestion param) {

        return new SuccessResponseData(questionService.list(param));
    }

}

