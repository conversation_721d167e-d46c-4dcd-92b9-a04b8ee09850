package eve.sys.modular.competitive.competitiveAnalysisFile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import eve.core.pojo.base.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 竞品分析数据包
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Getter
@Setter
@TableName("COMPETITIVE_ANALYSIS_FILE")
@AllArgsConstructor
@NoArgsConstructor
public class CompetitiveAnalysisFile extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 竞品分析id
     */
    private Long competitiveId;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小信息，计算后的
     */
    private String fileSizeInfo;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件类型 disassemble 拆解 electrical 电性能 safe 安全
     */
    private String fileType;


}
