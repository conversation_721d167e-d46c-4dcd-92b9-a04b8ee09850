package eve.sys.modular.aiQuestion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@Setter
@TableName("AI_QUESTION")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiQuestion extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private String question;

    private String result;

    private Long questionId;




}
