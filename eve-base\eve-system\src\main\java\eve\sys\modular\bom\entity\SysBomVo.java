package eve.sys.modular.bom.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SysBomVo   extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long lineId;

    private String lineName;

    private Long bomId;

    private Long productState;

    private String bomData;

    private Date createTime;

    private String remark;

    private String bomNo;

    private String bomVersion;

    private String bomName;

    private String bomChange;

    private Long fileId;

    private Integer type;

    //提交人
    private String summitor;

    private Integer isEnd;//是否成品

    private Integer isAddWerk; // 1 新增  2 删除

    /* 变更分类 */
    private Integer alterType;

    private String bomLines;

    private String bomTransport;

    private Long cellBomId;

    private Long packBomId;

    @TableField(exist = false)
    private List<Long> innData;

    @TableField(exist = false)
    private String key;
}
