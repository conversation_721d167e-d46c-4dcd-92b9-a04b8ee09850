package eve.sys.dorisModular.testData.service.impl;

import cn.hutool.core.util.PageUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.testData.entity.Log;
import eve.sys.dorisModular.testData.mapper.LogMapper;
import eve.sys.dorisModular.testData.service.ILogService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.mongoDbModular.shenghong.bean.HisLogData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class LogServiceImpl extends ServiceImpl<LogMapper, Log> implements ILogService {

    @Resource
    private ITLimsTestdataPrimaryService testdataPrimaryService;

    @Override
    public PageResult<Log> logListPage(HisLogData param) {
        TLimsTestdataPrimary testdataPrimary = testdataPrimaryService.getById(param.get_id());
        Map<String,Object> map = JSON.parseObject(testdataPrimary.getQueryparam(), Map.class);
        List<String> values = new ArrayList<>();

        for (String key : map.keySet()) {

            values.add(map.get(key).toString());
        }
        String flowId = String.join("-", values);
        LambdaQueryWrapper<Log> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Log::getFlowId,flowId);

        //翻页处理
        if (param.getPageNo() > 1) {
            Integer count = this.count(queryWrapper);
            List<Log> records = this.baseMapper.selectLogPage(queryWrapper, (param.getPageNo() - 1) * param.getPageSize(), param.getPageSize());
            PageResult pageResult = new PageResult();
            pageResult.setPageNo(param.getPageNo());
            pageResult.setPageSize(param.getPageSize());
            pageResult.setRows(records);
            pageResult.setTotalRows(count);
            pageResult.setTotalPage(PageUtil.totalPage(count,
                    param.getPageSize()));
            return pageResult;
        }

        queryWrapper.orderByAsc(Log::getAbsoluteTime);

        return new PageResult(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));

    }
}

