
package eve.sys.core.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.db.Db;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import cn.hutool.log.Log;
import org.springframework.boot.context.event.ApplicationContextInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import eve.core.consts.CommonConstant;
import eve.core.context.constant.ConstantContext;
import eve.core.enums.CommonStatusEnum;
import eve.core.exception.ServiceException;
import eve.core.pojo.base.param.BaseParam.add;
import eve.sys.modular.consts.enums.SysConfigExceptionEnum;
import eve.sys.modular.dict.entity.SysDictData;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化常量的监听器
 * <p>
 * 当spring装配好配置后，就去数据库读constants
 *
 * <AUTHOR>
 * @date 2020/6/6 23:39
 */
public class ConstantsInitListener implements ApplicationListener<ApplicationContextInitializedEvent>, Ordered {

    private static final Log log = Log.get();

    private static final String CONFIG_LIST_SQL = "select code,value from sys_config where status = ?";


    private static final String ORG_LIST_SQL = "select id,name from sys_org where status = 0";

    private static final String CAPITAL_CODE = "CODE";

    private static final String CODE = "code";

    private static final String CAPITAL_VALUE = "VALUE";

    private static final String VALUE = "value";

    private static final String TYPE_ID = "type_id";

    private static final String CAPITAL_TYPE_ID = "TYPE_ID";

    private static final String Dict_LIST_SQL = "select type_id,code,value from SYS_DICT_DATA where status = ?";

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    public void onApplicationEvent(ApplicationContextInitializedEvent applicationContextInitializedEvent) {
        ConfigurableEnvironment environment = applicationContextInitializedEvent.getApplicationContext()
                .getEnvironment();

        // 获取数据库连接配置
        String dataSourceUrl = environment.getProperty("spring.datasource.dynamic.datasource.b1.url");
        String dataSourceUsername = environment.getProperty("spring.datasource.dynamic.datasource.b1.username");
        String dataSourcePassword = environment.getProperty("spring.datasource.dynamic.datasource.b1.password");

        // 缓存中放入datasource链接，代码生成时候使用
        ConstantContext.putConstant(CommonConstant.DATABASE_URL_NAME, dataSourceUrl);
        ConstantContext.putConstant(CommonConstant.DATABASE_DRIVER_NAME,
                environment.getProperty("spring.datasource.dynamic.datasource.b1.driver-class-name"));
        ConstantContext.putConstant(CommonConstant.DATABASE_USER_NAME, dataSourceUsername);

        // 如果有为空的配置，终止执行
        if (ObjectUtil.hasEmpty(dataSourceUrl, dataSourceUsername)) {
            throw new ServiceException(SysConfigExceptionEnum.DATA_SOURCE_NOT_EXIST);
        }

        Connection conn = null;
        try {
            Class.forName(environment.getProperty("spring.datasource.dynamic.datasource.b1.driver-class-name"));
            assert dataSourceUrl != null;
            conn = DriverManager.getConnection(dataSourceUrl, dataSourceUsername, dataSourcePassword);

            // 获取sys_config表的数据
            List<Entity> entityList = SqlExecutor.query(conn, CONFIG_LIST_SQL, new EntityListHandler(),
                    CommonStatusEnum.ENABLE.getCode());
            // 获取部门表的数据
            List<Entity> orgList = SqlExecutor.query(conn, ORG_LIST_SQL, new EntityListHandler());

            // 将查询到的参数配置添加到缓存
            if (ObjectUtil.isNotEmpty(entityList)) {
                entityList.forEach(sysConfig -> ConstantContext.putConstant(
                        sysConfig.getStr(CODE) == null ? sysConfig.getStr(CAPITAL_CODE) : sysConfig.getStr(CODE),
                        sysConfig.getStr(VALUE) == null ? sysConfig.getStr(CAPITAL_VALUE) : sysConfig.getStr(VALUE)));
            }

            List<Long> ids = new ArrayList<Long>() {
                {
                    add(1560434068226170881L);
                    add(1560437758399995905L);
                    add(1560434894621499393L);
                    add(1562679026567942146L);
                    add(1560434328189132801L);
                    add(1559814815567413249L);
                    add(1562679710289825794L);
                    add(1559814815567413249L);
                    add(1566679535195217921L);
                    add(1566956840194916354L);
                    add(1566972857184927745L);
                    add(1566696028750438401L);
                    add(1566695664525467649L);
                    add(1566694874033381378L);
                    add(1564519857656750081L);
                    add(1564519241618350082L);
                    add(1564499028462067713L);
                    add(1560437927107485697L);
                    add(1560435197487996930L);
                    add(1560435494587326465L);
                    add(1560434894621499393L);
                    add(1560434328189132801L);
                    add(1560434068226170881L);
                    add(1560433448459034626L);
                    add(1560433122704220162L);
                    add(1559758936990117890L);
                    add(1559816551900217346L);
                    add(1522118152715505666L);
                    add(1522452210922381314L);

                    add(1719985128790818817L);
                    add(1719984364848676866L);
                    add(1722127017061179394L);
                    add(1721405225036275714L);
                    add(1719599013693083649L);//年度费用分类
                    add(1719599174167154689L);//项目费用分类

                    add(1722840378346426370L);
                    add(1722840788192841729L);
                    add(1722841372597800962L);
                    add(1722841668648554497L);

                    add(1723983502646751233L);
                    add(1723983748705595393L);
                    add(1723984179225735170L);
                    add(1723984580234752001L);
                    add(1724588722567561218L);//ZCO标签

                    add(1732307572432121858L);//zco阶段标签
                    add(1734824055062237186L);

                    add(1727132440508067842L);//工厂标签
                    add(1727131505677398018L);//ZCO事务部标签
                    add(1727133112326516738L);//ZCO业务事项
                    add(1732282004345008130L);//年度费用其他费用二级

                    add(1735178476329648130L);//部门

                    add(1735470421649952769L);//转入/转出
                    add(1732282364409229314L);//年度(强控)费用
                    add(1737750600145453057L);

                    add(1740564810365693954L);//十大平台
                }
            };
            
            List<Entity> dictList = SqlExecutor.query(conn, Dict_LIST_SQL, new EntityListHandler(), CommonStatusEnum.ENABLE.getCode());
            List<SysDictData> dictDatas = new ArrayList<>();

            dictList.forEach(e->{
                if (ids.indexOf(e.getLong(CAPITAL_TYPE_ID)) > -1) {
                    SysDictData item = new SysDictData();
                    item.setTypeId(e.getLong(CAPITAL_TYPE_ID));
                    item.setCode(e.getStr(CAPITAL_CODE));
                    item.setValue(e.getStr(CAPITAL_VALUE));
                    dictDatas.add(item);
                }
                
            });
            
            Map<Long, List<SysDictData>> dictMap = dictDatas.stream().collect(Collectors.groupingBy(node ->node.getTypeId()));

            ConstantContext.putConstant("dict_map",dictMap);
            ConstantContext.putConstant("org_List",orgList);

        } catch (SQLException | ClassNotFoundException e) {
            log.error(">>> 读取数据库constants配置信息出错：");
            e.printStackTrace();
            throw new ServiceException(SysConfigExceptionEnum.DATA_SOURCE_NOT_EXIST);
        } finally {
            DbUtil.close(conn);
        }

    }
}
