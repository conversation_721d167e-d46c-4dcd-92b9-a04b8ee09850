package eve.sys.limsModular.testMatrix.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 试验矩阵
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@TableName("T_LIMS_TESTMATRIX")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLimsTestmatrix implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人ID
     */
    private String createdbyid;

    /**
     * 创建人名称
     */
    private String createdbyname;

    /**
     * 创建时间
     */
    private Date createdtime;

    /**
     * 创建人部门ID
     */
    private String createdbyorgid;

    /**
     * 创建人部门名称
     */
    private String createdbyorgname;

    /**
     * 样品ID
     */
    private Long orderid;

    /**
     * 委托单id
     */
    private Long folderid;

    /**
     * 试验项目ID
     */
    private Long ordtaskid;

    /**
     * 选中状态
     */
    private String checkstatus;

    /**
     * 实际开始时间
     */
    private Date actualstarttime;

    /**
     * 实际结束时间
     */
    private Date actualendtime;

    /**
     * 状态
     */
    private String status;

    /**
     * 手工排程标志
     */
    private String handscheduleflag;

    /**
     * 自动排程标志
     */
    private String autoscheduleflag;

    /**
     * 恢复时间
     */
    private Date recoverytime;

    /**
     * 暂停原因
     */
    private String passreason;

    /**
     * 暂停时间
     */
    private Date passtime;

    /**
     * 移交试验室
     */
    private String handoverdept;

    /**
     * 移交试验室
     */
    private String handoverdeptname;

    /**
     * 移交时间
     */
    private Date handovertime;

    /**
     * 移交人
     */
    private String handover;

    /**
     * 领样时间
     */
    private Date collarusetime;

    /**
     * 处理容量衰减率的定时任务是否在进行中
     */
    private String doingvolumedecay;

    /**
     * 样品是否移交试验室标识
     */
    private String handoverflag;

    /**
     * 检测结果
     */
    private String testresult;

    /**
     * 业务编号
     */
    //private String servicecode;

    /**
     * 设备ID
     */
    //private Long equiptid;

    /**
     * 设备编号
     */
//    private String equiptcode;

    /**
     * 设备名称
     */
//    private String equiptname;



    /**
     * 计划开始时间(第四实验室使用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planstarttime;

    /**
     * 计划结束时间(第四实验室使用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planendtime;

    /**
     * 动态任务分配,查询数据状态表已提交的不能查询 1代表已提交，0/null代表未提交
     */
    private String assignstatus;
}
