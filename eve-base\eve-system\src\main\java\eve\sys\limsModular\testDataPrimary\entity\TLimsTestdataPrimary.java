package eve.sys.limsModular.testDataPrimary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 测试数据关联mongodb主表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Getter
@Setter
@TableName("T_LIMS_TESTDATA_PRIMARY")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLimsTestdataPrimary implements Serializable {

    
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 委托单主键
     */
    private Long folderid;

    /**
     * 委托单号
     */
    private String folderno;
    /**
     * 测试项主键
     */
    private Long ordtaskid;

    /**
     * 测试项编码
     */
    private String testcode;

    /**
     * 测试项名称
     */
    private String testname;

    /**
     * 样品主键
     */
    private Long orderid;

    /**
     * 样品编号
     */
    private String orderno;


    /**
     * 测试设备号
     */
    private String equiptcode;


    /**
     * 测试内容
     */
    private String dataPath;

    /**
     * 查询参数
     */
    private String queryparam;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 结束标识
     */
    private String endstatusflag;

    /**
     * 厂商名称
     */
    private String eptfactoryname;


    /**
     * 通道编号
     */
    private String channelno;

    /**
     * 测试编码
     */
    private String celltestcode;

    /**
     * 测试项别名
     */
    private String alias;

    /**
     * 启动条码唯一ID
     */
    private String startcodeuuid;

    /**
     * 委托单创建人ID
     */
    private String createdbyid;

    /**
     * 委托单项目负责人ID
     */
    private String projectleaderid;


    /**
     * 委托单创建人部门ID
     */
    private String createdbyorgid;

    private String flowId;

    private String theme;

    @TableField(exist = false)
    private Long uuid;

    @TableField(exist = false)
    private List<Long> ids;

    @TableField(exist = false)
    private int isShowAll;

    /**
     * 委托单检测实验室编码
     */
    private String laboratoryid;

    private Date createtime;

    private Date updatetime;

    private Integer deleteStatus;

    private Integer isHide;

    /**
     * 存储第几天
     */
    private String day;

    /**
     * 温度
     */
    private String tem;

    /**
     * soc
     */
    private String soc;

    /**
     * 需要取消隐藏的按钮
     */
    @TableField(exist = false)
    private Boolean showHide = false;

    @TableField(exist = false)
    private List<TLimsTestdataPrimary> children;

    @TableField(exist = false)
    private Boolean isChild = false;

    @TableField(exist = false)
    private Integer pageNo = 1;

    @TableField(exist = false)
    private Integer pageSize = 20;

    /**
     * G26报告 Comment
     */
    @TableField(exist = false)
    private String comment ;


    /**
     * sorDcr报告工步1
     */
    @TableField(exist = false)
    private Integer socDcrStepId1 ;
    /**
     * sorDcr报告工步2
     */
    @TableField(exist = false)
    private Integer socDcrStepId2 ;

    /**
     * G26循环报告使用 land 类型
     */
    @TableField(exist = false)
    private String landType ;

    /**
     * G26循环报告使用 K值
     */
    @TableField(exist = false)
    private String kvalue ;





}
