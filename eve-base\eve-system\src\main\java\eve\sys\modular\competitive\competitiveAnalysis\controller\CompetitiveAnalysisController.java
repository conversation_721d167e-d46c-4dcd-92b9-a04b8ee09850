package eve.sys.modular.competitive.competitiveAnalysis.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.baseParam.IdParam;
import eve.sys.modular.competitive.competitiveAnalysis.entity.CompetitiveAnalysis;
import eve.sys.modular.competitive.competitiveAnalysis.service.ICompetitiveAnalysisService;
import eve.sys.modular.competitive.competitiveAnalysisSample.service.ICompetitiveAnalysisSampleService;
import eve.sys.modular.test.testAbnormal.entity.TestAbnormal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 竞品分析 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@RestController
@RequestMapping("/competitiveAnalysis")
public class CompetitiveAnalysisController {

    @Resource
    private ICompetitiveAnalysisService competitiveAnalysisService;

    @PostMapping("/pageList")
    @BusinessLog(title = "竞品分析报告-分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData listPage(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.pageList(param));
    }

    @PostMapping("/add")
    @BusinessLog(title = "竞品分析报告-新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.add(param));
    }

    @PostMapping("/remove")
    @BusinessLog(title = "竞品分析报告-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData remove(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.remove(param));
    }

    @PostMapping("/update")
    @BusinessLog(title = "竞品分析报告-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.update(param));
    }

    @PostMapping("/echartsData")
    @BusinessLog(title = "竞品分析报告-echarts图数据", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData echartsData(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.echartsData(param.getCompetitiveType()));
    }

    @PostMapping("/treeData")
    @BusinessLog(title = "竞品分析报告-数据包查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData treeData(@RequestBody CompetitiveAnalysis param) {
        return new SuccessResponseData(competitiveAnalysisService.treeData(param.getCode()));
    }
}

