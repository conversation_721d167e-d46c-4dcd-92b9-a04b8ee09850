package eve.sys.modular.bomend.controller;


import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.exception.ServiceException;
import eve.core.pojo.response.ErrorResponseData;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bomend.entity.SysBomEnd;
import eve.sys.modular.bomend.service.ISysBomEndService;

import java.io.IOException;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@RestController
public class SysBomEndController {
    @Resource
    private ISysBomEndService sysBomEndService;

    @BusinessLog(title = "成品BOM关联", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBomEnd/save")
    public ResponseData save(@RequestBody SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.saveBomEnd(param));
    }

    

    @BusinessLog(title = "成品BOM编辑", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBomEnd/edit")
    public ResponseData editBomEnd(@RequestBody SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.editBomEnd(param));
    }

    @BusinessLog(title = "成品BOM设置工厂", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBomEnd/setbomlines")
    public ResponseData addBomLines(@RequestBody SysBomEnd param) {
        sysBomEndService.addBomLines(param);
        return new SuccessResponseData();
    }
    @BusinessLog(title = "成品BOM删除工厂", opType = LogAnnotionOpTypeEnum.DELETE)
    @PostMapping("/sysBomEnd/delBomLines")
    public ResponseData delBomLines(@RequestBody SysBomEnd param) {
        sysBomEndService.delBomLines(param);
        return new SuccessResponseData();
    }

    @PostMapping("/sysBomEnd/page")
    public ResponseData page(@RequestBody SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.page(param));
    }

    @PostMapping("/sysBomEnd/bompage")
    public ResponseData bomPage(@RequestBody SysBomParam param) {
        return new SuccessResponseData(sysBomEndService.bomPage(param));
    }

    //获取bom列表
    @GetMapping("/sysBomEnd/list")
    public ResponseData getList(SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.getList(param));
    }

    @GetMapping("/sysBomEnd/get")
    public ResponseData getBomEnd(SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.getBomEndError(param));
    }

    //关联和重试
    @BusinessLog(title = "成品BOM提交", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBomEnd/relate")
    public ResponseData upgrade(@RequestHeader("JiraAuth") String token,@RequestBody SysBomEnd param) throws IOException {
        sysBomEndService.relate(token,param);
        if (param.getProofs().size() > 0) {
            return new ErrorResponseData(501,"BOM扩充校验失败",param.getProofs());
        }
        return new SuccessResponseData();
    }

    //获取包装bom的产线
    @GetMapping("/sysBomEnd/packLineIds")
    public ResponseData getBomPackLineIds(SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.getBomPackLineIds(param));
    }

    //获取成品bom的产线
    @GetMapping("/sysBomEnd/bomLineIds")
    public ResponseData getBomLineIds(SysBomEnd param) {
        return new SuccessResponseData(sysBomEndService.getBomLineIds(param));
    }

    //删除成品BOM
    @BusinessLog(title = "成品BOM删除", opType = LogAnnotionOpTypeEnum.DELETE)
    @PostMapping("/sysBomEnd/del")
    public ResponseData del(@RequestBody SysBomEnd param) throws IOException {
        sysBomEndService.delBomEnd(param);
        return new SuccessResponseData();
    }

    @BusinessLog(title = "成品BOM导入", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBomEnd/sapimport")
    public ResponseData sapImport(@RequestBody SysBomEnd param) throws ServiceException {
        sysBomEndService.sapImport(param);
        return new SuccessResponseData();
    }

    @BusinessLog(title = "成品BOM撤回", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBomEnd/widthDraw")
    public ResponseData widthDraw(@RequestBody SysBomParam sysBomParam) {
        sysBomEndService.withDraw(sysBomParam);
        return new SuccessResponseData();
    }
    
}

