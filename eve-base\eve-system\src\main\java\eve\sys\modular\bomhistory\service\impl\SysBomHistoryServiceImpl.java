package eve.sys.modular.bomhistory.service.impl;

import eve.core.factory.PageFactory;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBomVo;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.mapper.SysBomHistoryMapper;
import eve.sys.modular.bomhistory.param.SysBomHistoryParam;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.bean.BeanUtil;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@Service
public class SysBomHistoryServiceImpl extends ServiceImpl<SysBomHistoryMapper, SysBomHistory> implements ISysBomHistoryService {

    @Override
    public Long add(SysBomHistoryParam param) {

        SysBomHistory sysBomHistory = new SysBomHistory();
        BeanUtil.copyProperties(param,sysBomHistory);
        this.save(sysBomHistory);

        return sysBomHistory.getId();
    }

    @Override
    public SysBomHistory getLastOne(Long bomId) {
        return this.baseMapper.getLastOne(bomId);
    }

    @Override
    public SysBomHistory getCountByBomId(Long bomId) {
        return this.baseMapper.getCountByBomId(bomId);
    }

    @Override
    public SysBomHistory getLastHistory(Long bomId) {
        return this.baseMapper.getLastHistory(bomId);
    }

    @Override
    public PageResult<SysBomHistory> page(Long bomId) {
        LambdaQueryWrapper<SysBomHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(SysBomHistory::getCreateTime);
        queryWrapper.eq(SysBomHistory::getBomId, bomId);
        //queryWrapper.gt(SysBomHistory::getIsAddWerk, 0);
        PageResult<SysBomHistory> res = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        res.getRows().forEach(e->{
            e.setInnData(new ArrayList<>());
        });
        return res;
    }

    @Override
    public List<SysBomVo> pageB(SysBomParam param) {
        String[] split = param.getBomIds().split(",");
        if(param.getBomIds().isEmpty() || ObjectUtils.isEmpty(split)){
            return new ArrayList<>();
        }

        List<Long> ids = Arrays.asList(split).stream().map(Long::parseLong).collect(Collectors.toList()); //前端用get传数组

        List<SysBomVo> sysBomVo = this.baseMapper.getBomHis(ids);
        //过滤最新
        List<SysBomVo> collect = sysBomVo.stream()
                .collect(Collectors.groupingBy(
                        SysBomVo::getBomId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(
                                        (r1, r2) -> r1.getCreateTime().compareTo(r2.getCreateTime())
                                ),
                                // Optional::get 可能会抛出 NoSuchElementException
                                Optional -> Optional.orElseThrow(IllegalStateException::new)
                        )
                ))
                .values().stream()
                .collect(Collectors.toList());

        return collect;
    }

    @Override
    public List<SysBomHistory> getHistoryGroupByBomId(List<Long> bomIds){
        return this.baseMapper.getHistoryGroupByBomId(bomIds);
    }

    @Override
    public List<SysBomHistory> getLastHistoryGroupByBomId() {
        return this.baseMapper.getLastHistoryGroupByBomId();
    }

}
