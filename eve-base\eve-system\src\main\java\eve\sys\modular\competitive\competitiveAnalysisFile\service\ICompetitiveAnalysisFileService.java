package eve.sys.modular.competitive.competitiveAnalysisFile.service;

import eve.sys.modular.competitive.competitiveAnalysisFile.entity.CompetitiveAnalysisFile;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 竞品分析数据包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface ICompetitiveAnalysisFileService extends IService<CompetitiveAnalysisFile> {

    Boolean add(MultipartFile file, CompetitiveAnalysisFile param) throws Exception;

    Boolean remove(CompetitiveAnalysisFile param);

    Boolean update(CompetitiveAnalysisFile param);

    List<CompetitiveAnalysisFile> list(CompetitiveAnalysisFile param);
}
