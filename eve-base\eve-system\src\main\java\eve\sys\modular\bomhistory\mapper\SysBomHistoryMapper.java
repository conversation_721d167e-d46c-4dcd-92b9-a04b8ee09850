package eve.sys.modular.bomhistory.mapper;

import eve.sys.modular.bom.entity.SysBomVo;
import eve.sys.modular.bomhistory.entity.SysBomHistory;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
public interface SysBomHistoryMapper extends BaseMapper<SysBomHistory> {
    SysBomHistory getLastOne(Long bomId);

    SysBomHistory getCountByBomId(Long bomId);

    SysBomHistory getLastHistory(Long bomId);

    List<SysBomHistory> getHistoryGroupByBomId(@Param("bomIds")List<Long> bomIds);

    List<SysBomHistory> getLastHistoryGroupByBomId();

    List<SysBomVo> getBomHis(List<Long> bomIds);
}
