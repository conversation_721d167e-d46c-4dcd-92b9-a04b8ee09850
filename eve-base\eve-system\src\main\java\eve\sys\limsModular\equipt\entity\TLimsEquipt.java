package eve.sys.limsModular.equipt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Getter
@Setter
@TableName("T_LIMS_EQUIPT")
public class TLimsEquipt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 设备编码
     */
    private String equiptcode;

    /**
     * 设备名称
     */
    private String equiptname;

    /**
     * 设备状态
     */
    private String status;

    /**
     * 设备类型ID
     */
    private Long equiptcategoryid;

    /**
     * 所属部门ID
     */
    private String orgid;

    /**
     * 所属部门
     */
    private String orgname;

    /**
     * 所属实验室ID
     */
    private Long laboratoryid;

    /**
     * 所属实验室
     */
    private String laboratory;

    /**
     * 所属试验室组别ID
     */
    private String groupcategoryid;

    /**
     * 所属试验室组别
     */
    private String groupcategory;

    /**
     * 设备管理员
     */
    private String manageuser;

    /**
     * 设备管理员ID
     */
    private String manageuserid;

    /**
     * 固定资产编号
     */
    private String fixassetcode;

    /**
     * 计量编号
     */
    private String measurecode;

    /**
     * 设备使用类型
     */
    private String usetype;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 设备尺寸
     */
    private String dimension;

    /**
     * 生产厂家
     */
    private String manufacture;

    /**
     * 供应商
     */
    private Long vendorid;

    /**
     * 出厂编号
     */
    private String manufacturecode;

    /**
     * 出厂日期
     */
    private Date manufacturedate;

    /**
     * 购进日期
     */
    private Date purchasedate;

    /**
     * 使用年限
     */
    private Long usefullife;

    /**
     * 存放地点ID
     */
    private Long locationid;

    /**
     * 通道数量
     */
    private Long channels;

    /**
     * 单元数量
     */
    private Long unitnum;

    /**
     * 测量范围
     */
    private String measurescope;

    /**
     * 测量精度
     */
    private String accuracy;

    /**
     * 工作环境温度
     */
    private Long temperature;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 采样频率
     */
    private String quency;

    /**
     * 设备资料
     */
    private String equiptinfo;

    /**
     * 设备功率
     */
    private String power;

    /**
     * 作业指导书
     */
    private String sop;

    /**
     * 检定有效期
     */
    private Date verificationdate;

    /**
     * 校准有效期
     */
    private Date calibrationdate;

    /**
     * 期间核查有效期
     */
    private Date validitydate;

    /**
     * 应出勤时间
     */
    private String attenddate;

    /**
     * 出勤开始
     */
    private String attendstartdate;

    /**
     * 出勤结束时间
     */
    private String attendenddate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 设备图片
     */
    private String pic;

    /**
     * 创建人ID
     */
    private String createdbyid;

    /**
     * 创建人
     */
    private String createdbyname;

    /**
     * 创建时间
     */
    private Date createdtime;

    /**
     * 创建人部门ID
     */
    private String createdbyorgid;

    /**
     * 创建人部门
     */
    private String createdbyorgname;

    /**
     * 湿度
     */
    private Long humidity;

    /**
     * 公共辅助设备的标记
     */
    private String publicassistflag;

    /**
     * 设备数据源ID
     */
    private Long eptdatasourceid;

    /**
     * 设备新分类型
     */
    private String equipttype;

    /**
     * 内部编号
     */
    private String innercode;

    /**
     * EAM编号
     */
    private String eamcode;

    /**
     * 用途
     */
    private String usage;

    /**
     * 是否固资
     */
    private String fixedflag;

    /**
     * SAP编码
     */
    private String assetcode;

    /**
     * 立卡人
     */
    private String audituser;

    /**
     * 公司代码
     */
    private String firdeptcode;

    /**
     * 所属公司
     */
    private String firdeptname;

    /**
     * 产品线（OA一级部门）
     */
    private String linename;

    /**
     * 设备类别（一级）
     */
    private String firsttype;

    /**
     * 设备类别（二级）
     */
    private String secondtype;

    /**
     * ABC分类
     */
    private String abctype;

    /**
     * 设备机型
     */
    private String bomname;

    /**
     * SN码
     */
    private String serialno;

    /**
     * SAP资产状态
     */
    private String sapstatus;

    /**
     * 成本中心代码
     */
    private String costcode;

    /**
     * 成本中心
     */
    private String costname;

    /**
     * 使用部门id
     */
    private String deptid;

    /**
     * 使用部门
     */
    private String deptname;

    /**
     * 地理区域编码
     */
    private String sitecode;

    /**
     * 地理区域描述（拼接）
     */
    private String sitename;

    /**
     * 存放地点
     */
    private String installsite;

    /**
     * 专项属性
     */
    private String specialtype;

    /**
     * 资产管理员
     */
    private String liableuser;

    /**
     * 启用日期
     */
    private Date revdate;

    /**
     * 资本化日期
     */
    private Date assetdate;

    /**
     * 供应商代码
     */
    private String providercode;

    /**
     * 供应商名称
     */
    private String providername;

    /**
     * 校准方式（内/外）
     */
    private String calibway;

    /**
     * 校准日期
     */
    private String caliblastdate;

    /**
     * 校准周期（月）
     */
    private String calibcheckcyc;

    /**
     * 次校日期
     */
    private String calibnextdate;

    /**
     * 是否温箱
     */
    private String wxflag;

    /**
     * 测试型号
     */
    private String testproducttype;

    /**
     * 业务编号
     */
    private String servicecode;

    /**
     * 量程
     */
    private String range;

    /**
     * 温度
     */
    private String testtemperature;

    /**
     * 设备校准计划ID
     */
    private Long calibrateplanid;

    private String sealstatus;

    /**
     * 电脑ip
     */
    private String pcip;

    /**
     * 所属实验室ID
     */
    private Long testlabid;

    /**
     * 所属实验室
     */
    private String testlabname;

    /**
     * 设备状态
     */
    private String dcxtstatus;

    /**
     * 上次校准日期
     */
    private Date dcxtlastcaldate;

    /**
     * 最后使用时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsedTime;

    /**
     * 最后使用委托单
     */
    @TableField(exist = false)
    private String lastUsedFolderNo;
}
