package eve.sys.dorisModular.testData.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import eve.sys.dorisModular.testData.entity.Log;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@DS("b4")
public interface LogMapper extends BaseMapper<Log> {


    List<Log> selectLogPage(@Param("ew") LambdaQueryWrapper queryWrapper, @Param("start")Integer start, @Param("pageSize")Integer pageSize);

}
