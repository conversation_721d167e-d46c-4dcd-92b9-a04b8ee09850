 
package eve.sys.modular.auth.controller;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import eve.core.annotion.BusinessLog;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.context.login.LoginContextHolder;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.exception.AuthException;
import eve.core.exception.enums.AuthExceptionEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.auth.service.AuthService;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Map;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @date 2020/3/11 12:20
 */
@RestController
public class SysLoginController {

    @Resource
    private AuthService authService;

    @Lazy
    @Resource
    private CaptchaService captchaService;

    /**
     * 获取是否开启租户的标识
     *
     * <AUTHOR>
     * @date 2020/9/4
     */
    @GetMapping("/getTenantOpen")
    public ResponseData getTenantOpen() {
        return new SuccessResponseData(ConstantContextHolder.getTenantOpenFlag());
    }

    /**
     * 账号密码登录
     *
     * <AUTHOR>
     * @date 2020/3/11 15:52
     */
    @PostMapping("/login")
//    @BusinessLog(title = "用户-登录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData login(@RequestBody Dict dict) {
        String account = dict.getStr("account");
        String password = dict.getStr("password");
        //String tenantCode = dict.getStr("tenantCode");

        //检测是否开启验证码
        /* if (ConstantContextHolder.getCaptchaOpenFlag()) {
            verificationCode(dict.getStr("code"));
        } */

        //如果系统开启了多租户开关，则添加租户的临时缓存
        /* if (ConstantContextHolder.getTenantOpenFlag()) {
            authService.cacheTenantInfo(tenantCode);
        } */

        String token = authService.login(account, password);
        return new SuccessResponseData(JSONObject.parse(token));
    }

    @PostMapping("/login2")
//    @BusinessLog(title = "用户-登录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData login2(@RequestBody Dict dict) {
        String account = dict.getStr("account");

        String token = authService.login1(account);
        return new SuccessResponseData(JSONObject.parse(token));
    }

    @GetMapping("/login1")
//    @BusinessLog(title = "用户-登录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseEntity<Object> login1(@RequestParam String account) {

        String s = this.authService.login1(account);
        Map map = JSONObject.parseObject(s, Map.class);
        return ResponseEntity.status(HttpStatus.FOUND)
                .header("Authorization", "Bearer "+map.get("jwtToken"))
                .location(URI.create("http://10.5.101.134:88/product_test"))
                .build();
    }

    /**
     * 退出登录
     *
     * <AUTHOR>
     * @date 2020/3/16 15:02
     */
    @GetMapping("/logout")
    @BusinessLog(title = "用户-注销登录", opType = LogAnnotionOpTypeEnum.QUERY)
    public void logout() {
        authService.logout();
    }

    /**
     * 获取当前登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/23 17:57
     */
    @GetMapping("/getLoginUser")
    @BusinessLog(title = "用户-获取用户信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getLoginUser() {
        return new SuccessResponseData(LoginContextHolder.me().getSysLoginUserUpToDate());
    }

    /**
     * 获取验证码开关
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    @GetMapping("/getCaptchaOpen")
    @Deprecated
    public ResponseData getCaptchaOpen() {
        return new SuccessResponseData(ConstantContextHolder.getCaptchaOpenFlag());
    }

    /**
     * 校验验证码
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    private boolean verificationCode(String code) {
        CaptchaVO vo = new CaptchaVO();
        vo.setCaptchaVerification(code);
        if (!captchaService.verification(vo).isSuccess()) {
            throw new AuthException(AuthExceptionEnum.CONSTANT_EMPTY_ERROR);
        }
        return true;
    }

}
