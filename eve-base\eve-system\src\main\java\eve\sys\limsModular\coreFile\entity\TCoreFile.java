package eve.sys.limsModular.coreFile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 文件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Getter
@Setter
@TableName("T_CORE_FILE")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TCoreFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 对应业务主键
     */
    private String targetid;

    /**
     * MD5加密的文件名称（主键+$+文件名称后加密）
     */
    private String md5name;

    /**
     * 文件扩展名（如：jpg,doc,gif等）
     */
    private String fileext;

    /**
     * 访问范围（如：secure-登录才能访问 open-开放 temp-临时文件 import-数据导入）
     */
    private String scope;

    /**
     * 下载次数
     */
    private Integer times;

    /**
     * 生效开始日期
     */
    private Date validtimefrom;

    /**
     * 生效截止日期
     */
    private Date validtimeto;

    /**
     * 额外信息
     */
    private String ext;

    /**
     * 制单日期
     */
    private Date createdtime;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 制单人编码
     */
    private String createdbyid;

    /**
     * 制单人名称
     */
    private String createdbyname;

    /**
     * 长期生效（0-否 1-是）
     */
    private String permanent;

    /**
     * 是否允许下载（no-否 yes-是）
     */
    private String downloadable;

    private String bizcategory;

    private String remark;

    /**
     * 制单单位编码
     */
    private String createdbyorgid;

    /**
     * 制单单位名称
     */
    private String createdbyorgname;

    @TableField(select = false)
    private BigDecimal size;

    /**
     * 版本
     */
    private BigDecimal version;

    /**
     * 样品分组
     */
    private String samplegroup;

    /**
     * 是否报告中展示
     */
    private String reportshowflag;

    /**
     * 排序
     */
    private BigDecimal orderno;


}
