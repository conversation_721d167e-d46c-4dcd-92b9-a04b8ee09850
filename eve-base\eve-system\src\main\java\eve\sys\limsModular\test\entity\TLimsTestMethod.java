package eve.sys.limsModular.test.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 试验项目方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@TableName("T_LIMS_TEST_METHOD")
public class TLimsTestMethod implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人ID
     */
    private String createdbyid;

    /**
     * 创建人名称
     */
    private String createdbyname;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdtime;

    /**
     * 创建人部门ID
     */
    private String createdbyorgid;

    /**
     * 创建人部门名称
     */
    private String createdbyorgname;

    /**
     * 试验项目ID
     */
    private Long testid;

    /**
     * 试验方法ID
     */
    private Long methodid;

    /**
     * 测试目的
     */
    private String testpurpose;

    /**
     * 标准成本单位
     */
    private String standardcostunit;

    /**
     * 标准工时
     */
    private String standardhour;

    /**
     * 测试单价
     */
    private Long price;

    /**
     * 试验准备时长
     */
    private String testduration;

    /**
     * 备注
     */
    private String remark;

    /**
     * 拼单标记
     */
    private String shareflag;

    /**
     * 判定标准
     */
    private String judgestandard;

    /**
     * 检测记录
     */
    private String testrecord;

    /**
     * 判定依据
     */
    private String judgebasis;

    /**
     * 费用类型
     */
    private String expensetype;

    /**
     * 启用标志
     */
    private String enableflag;

    /**
     * 是否排程
     */
    private String scheduleflag;

    /**
     * 是否温变
     */
    private String tempchangeflag;

    /**
     * 附加时长
     */
    private String additionaltime;

    /**
     * 时间单位
     */
    private String timeunit;

    /**
     * 检测时长公式
     */
    private String testdurationformula;

    /**
     * 检测内容
     */
    private String testcontent;

    private String teststep;

    /**
     * 是否委外
     */
    private String outsourceflag;

    /**
     * 业务类型
     */
    private String businesstype;

    /**
     * ELN模板ID
     */
    private Long elntmplid;

    /**
     * ELN模板
     */
    private String elntemplate;

    /**
     * 是否需要报告
     */
    private String reportflag;

    /**
     * 意见
     */
    private String idea;

    /**
     * 解释
     */
    private String explain;

    /**
     * 测试周期
     */
    private String testcycle;

    /**
     * 测试方法描述
     */
    private String description;


}
