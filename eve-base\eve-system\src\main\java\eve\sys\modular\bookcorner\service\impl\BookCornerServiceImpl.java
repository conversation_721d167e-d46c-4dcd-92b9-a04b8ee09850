package eve.sys.modular.bookcorner.service.impl;

import eve.core.factory.PageFactory;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bookcorner.entity.BookCorner;
import eve.sys.modular.bookcorner.mapper.BookCornerMapper;
import eve.sys.modular.bookcorner.service.IBookCornerService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.util.ObjectUtil;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 图书角 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class BookCornerServiceImpl extends ServiceImpl<BookCornerMapper, BookCorner> implements IBookCornerService {

    @Override
    public List<BookCorner> getList(BookCorner param) {
        LambdaQueryWrapper<BookCorner> queryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotNull(param)){
            if (ObjectUtil.isNotEmpty(param.getId())) {
                queryWrapper.ne(BookCorner::getId, param.getId());
            }
            if (ObjectUtil.isNotEmpty(param.getBookCateId())) {
                queryWrapper.eq(BookCorner::getBookCateId, param.getBookCateId());
            }

            if (ObjectUtil.isNotEmpty(param.getBookName())) {
                queryWrapper.like(BookCorner::getBookName, param.getBookName());
            }
            
        }

        return this.list(queryWrapper).stream().limit(Optional.of(param).map(BookCorner::getNums).orElse(8L)).collect(Collectors.toList());
    }

    @Override
    public PageResult<BookCorner> page(BookCorner param) {

        LambdaQueryWrapper<BookCorner> queryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotNull(param)){
            if (ObjectUtil.isNotEmpty(param.getBookCateIds())) {
                queryWrapper.in(BookCorner::getBookCateId, param.getBookCateIds());
            }
            
            if (ObjectUtil.isNotEmpty(param.getBookName())) {
                queryWrapper.like(BookCorner::getBookName, param.getBookName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public BookCorner getById(BookCorner param) {
        return this.getById(param.getId());
    }

    @Override
    public void add(BookCorner param) {
        this.save(param);
    }

    @Override
    public void edit(BookCorner param) {
        this.updateById(param);
    }

    @Override
    public void delete(BookCorner param) {
        this.removeById(param.getId());
    }

}
