package eve.sys.jiraModular.productManager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Getter
@Setter
@TableName("product_manager_test")
//@TableName("product_manager")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductManager implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long issueId;

    private Date createDate;

    private Date updateDate;

    private Date dueDate;

    private String resolution;

    private String summary;

    private String deptName;

    private String parentDept;

    private String parentDeptName;

    private String childDept;

    private String childDeptName;

    private String productCate;

    private String productParentCate;

    private String productChildCate;

    private Date projectStartDate;

    private String productName;

    private String projectName;

    private String customer;

    private Long projectLevel;

    private String projectLevelName;

    private Long productState;

    private String productStateName;

    private String productStageName;

    private String productRpmName;

    private String productRpm;

    private String productManagerName;

    private String productManager;

    private String directorName;

    private String director;

    private String issueKey;

    private String issueTypeName;

    private String issuestatusName;

    private String projectName1;

    private String priorityName;

    private String projectLeader;

    private String projectLeaderId;

    private String creatorId;

    private String creatorName;

    private String assigneeId;

    private String assigneeName;

    private String structureType;

    /**
     * 应用场景
     */
    private String scenario;

    private Long productOrProject;

    private Long productMatrixCateParent;

}
