package eve.sys.limsModular.test.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 试验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@TableName("T_LIMS_TEST")
public class TLimsTest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人ID
     */
    private String createdbyid;

    /**
     * 创建人名称
     */
    private String createdbyname;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdtime;

    /**
     * 创建人部门ID
     */
    private String createdbyorgid;

    /**
     * 创建人部门名称
     */
    private String createdbyorgname;

    /**
     * 启用人ID
     */
    private String activatedbyid;

    /**
     * 启用禁用
     */
    private String activatedflag;

    /**
     * 启用人
     */
    private String activatedbyname;

    /**
     * 启用时间
     */
    private Date activatedtime;

    /**
     * 所属试验室ID
     */
    private String orgid;

    /**
     * 所属试验室
     */
    private String orgname;

    /**
     * 备注
     */
    private String remark;

    /**
     * 检测库
     */
    private Long testprojectlibid;

    /**
     * ELN模板ID
     */
    private Long elntmplid;

    /**
     * 试验项目的分解员
     */
    private String resolver;

    /**
     * 试验项目的分解员ID
     */
    private String resolverid;

    /**
     * 告警容量衰减率
     */
    private String volumedecayrate;

    /**
     * 报告生成方式
     */
    private String doreportflag;

    /**
     * 测试项目编码
     */
    private String testcode;

    /**
     * 测试项目名称
     */
    private String testname;

    /**
     * 地点
     */
    private String place;

    private Long testcategoryid;


}
