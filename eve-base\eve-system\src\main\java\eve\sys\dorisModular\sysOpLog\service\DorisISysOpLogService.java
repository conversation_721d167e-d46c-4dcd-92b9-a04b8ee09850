package eve.sys.dorisModular.sysOpLog.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.sysOpLog.entity.DorisSysOpLog;
import eve.sys.modular.log.param.SysOpLogParam;

import java.io.UnsupportedEncodingException;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@DS("b4")
public interface DorisISysOpLogService extends IService<DorisSysOpLog> {

    /**
     * 查询系统操作日志
     *
     * @param sysOpLogParam 查询参数
     * @return 查询分页结果
     * <AUTHOR>
     * @date 2020/3/30 10:32
     */
    PageResult<DorisSysOpLog> page(SysOpLogParam sysOpLogParam) throws UnsupportedEncodingException;

    DorisSysOpLog get(SysOpLogParam sysOpLogParam) throws UnsupportedEncodingException;
}
