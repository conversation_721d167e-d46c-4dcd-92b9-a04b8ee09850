<template>
  <div class="container">
    <!--    <div class="topic_wid">
          <a-breadcrumb class="breadcrumb" separator="/">
            <a-breadcrumb-item>当前位置：<router-link to="/">首页</router-link></a-breadcrumb-item>
            <a-breadcrumb-item><router-link to="/lab-brief">实验平台</router-link></a-breadcrumb-item>
            <a-breadcrumb-item><router-link to="/labs">测试验证实验室</router-link></a-breadcrumb-item>
            <a-breadcrumb-item>测试失效管理</a-breadcrumb-item>
            <a-breadcrumb-item>测试失效分析报告管理</a-breadcrumb-item>
          </a-breadcrumb>
        </div>-->
    <a-spin :spinning="isDownloading" tip="文件下载中...">

    <pbiTitle title="测试失效分析报告管理"></pbiTitle>
    <div class="content" :style="{marginTop: '8px'}">
      <div class="left mr10">
        <div class="left-top">
          <div class="flex-sb-center-row">

            <div style="font-weight: bolder">数据包:{{treeData.total || '0'}}</div>
            <div class="btn-wrap mt5">
              <span>完成:{{treeData.finished || '0' }}</span>
              <a-divider type="vertical" style="color: #333;" />
              <span>进行中:{{treeData.ongoing || '0' }}</span>
            </div>
          </div>
          <div style="display: flex;margin-top: 10px">
            <div style="align-content: center;width: 25%;font-size: 12px;">文件名称：</div>
            <a-input  v-model="fileName" @keyup.enter="getTreeData" style="width: 55%;margin-right: 12px"></a-input>
            <a-button type="primary"  @click='getTreeData' class="btn-wrap mt5" style="padding: 0 5px;width: 20%"><a-icon type="search"></a-icon>搜索</a-button>
          </div>

        </div>

        <div class="left-bottom">

          <a-directory-tree @select="preview">
            <a-icon slot="switcherIcon" type="down" />
            <a-tree-node :key="item.id" :title="item.code" v-for="item in treeData.tree">
              <template slot="icon">
                <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="22" height="22"><path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path><path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path><path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path></svg>
              </template>

              <a-tree-node :key="item.id+cellFile.code" :title="cellFile.code" v-for="cellFile in item.cellFileList" >
                <template slot="icon">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="22" height="22"><path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path><path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path><path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path></svg>
                </template>
                <a-tree-node :key="file.id" :title="file.fileName" v-for="file in cellFile.data" is-leaf :on="{click : preview}" :value="file" @select="preview">
                  <template slot="icon">
                    <svg t="1733187029740" v-if="file.fileSuffix == 'pdf' || file.fileSuffix == 'PDF'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15201" width="18" height="24"><path d="M870.4 1024h-716.8A51.2 51.2 0 0 1 102.4 972.8v-921.6A51.2 51.2 0 0 1 153.6 0h569.685333a42.666667 42.666667 0 0 1 30.037334 12.288l155.989333 155.989333a42.666667 42.666667 0 0 1 12.288 30.037334V972.8a51.2 51.2 0 0 1-51.2 51.2zM153.6 34.133333a17.066667 17.066667 0 0 0-17.066667 17.066667v921.6a17.066667 17.066667 0 0 0 17.066667 17.066667h716.8a17.066667 17.066667 0 0 0 17.066667-17.066667V198.314667a7.168 7.168 0 0 0-2.389334-5.802667l-155.989333-155.989333a7.168 7.168 0 0 0-5.802667-2.389334z" fill="#4D4D4D" p-id="15202"></path><path d="M904.533333 204.8h-170.666666a17.066667 17.066667 0 0 1-17.066667-17.066667v-170.666666h34.133333V170.666667h153.6z" fill="#4D4D4D" p-id="15203"></path><path d="M204.8 170.666667h443.733333v34.133333H204.8zM204.8 307.2h614.4v34.133333H204.8zM204.8 443.733333h614.4v34.133334H204.8zM204.8 580.266667h614.4v34.133333H204.8zM204.8 853.333333h614.4v34.133334H204.8zM204.8 716.8h614.4v34.133333H204.8z" fill="#B3B3B3" p-id="15204"></path><path d="M51.2 460.8m17.066667 0l887.466666 0q17.066667 0 17.066667 17.066667l0 273.066666q0 17.066667-17.066667 17.066667l-887.466666 0q-17.066667 0-17.066667-17.066667l0-273.066666q0-17.066667 17.066667-17.066667Z" fill="#F33958" p-id="15205"></path><path d="M955.733333 477.866667v273.066666H68.266667v-273.066666h887.466666m0-34.133334H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133334v273.066666a34.133333 34.133333 0 0 0 34.133334 34.133334h887.466666a34.133333 34.133333 0 0 0 34.133334-34.133334v-273.066666a34.133333 34.133333 0 0 0-34.133334-34.133334z" fill="#C42E47" p-id="15206"></path><path d="M348.16 530.090667a55.978667 55.978667 0 1 1 0 111.616H307.2v57.002666h-24.917333v-168.618666zM307.2 618.837333h34.133333c22.528 0 35.84-11.605333 35.84-32.426666s-12.970667-34.133333-35.84-34.133334H307.2zM509.952 530.090667A74.410667 74.410667 0 0 1 589.141333 614.4a75.093333 75.093333 0 0 1-79.189333 84.992h-60.757333v-169.301333z m-34.133333 144.725333h31.744A52.906667 52.906667 0 0 0 562.858667 614.4a53.248 53.248 0 0 0-55.637334-60.074667h-31.744zM636.586667 698.709333v-168.618666h105.130666v23.893333h-78.848v51.882667h72.021334v22.869333h-72.021334v68.266667z" fill="#FFFFFF" p-id="15207"></path></svg>
                    <!--                    <a-icon type="file-pdf" v-if="file.fileSuffix == 'pdf'"/>-->
                    <svg t="1733187630326" v-else-if="file.fileSuffix == 'xlsx' || file.fileSuffix == 'xls'"  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18213" width="18" height="24"><path d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z" fill="#21A366" p-id="18214"></path><path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="18215"></path><path d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z" fill="#33C481" p-id="18216"></path><path d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z" fill="#185C37" p-id="18217"></path><path d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z" opacity=".5" p-id="18218"></path><path d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z" fill="#107C41" p-id="18219"></path><path d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z" fill="#FFFFFF" p-id="18220"></path><path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="18221"></path></svg>
                    <!--                    <a-icon type="file-excel" v-else-if="file.fileSuffix == 'xlxs'"/>-->
                    <svg t="1733187718798" v-else-if="file.fileSuffix == 'pptx'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19328" width="18" height="24"><path d="M538.731891 0h65.98683v107.168391c124.387582 0.722484 248.895579-1.324553 373.28316 0a40.699906 40.699906 0 0 1 45.034808 46.118533c2.047037 222.404516 0 444.929445 1.204139 667.454374-1.204139 24.082785 2.287865 50.694262-11.198495 72.248354-16.978363 12.041392-39.014111 10.957667-59.002822 12.041392-116.319849-0.60207-232.639699 0-349.200376 0V1023.518344h-72.248354C355.100659 990.886171 177.490122 960.662277 0 928.752587V95.488241C179.537159 63.698965 359.074318 31.30762 538.731891 0z" fill="#D24625" p-id="19329"></path><path d="M604.718721 142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532 14.570085 98.619003 14.208843 139.800564-14.088429 44.553151-27.093133 67.793039-78.630292 71.646284-130.047036H663.119473c0-51.777987 0.60207-103.555974-0.963311-155.213547-19.145814 3.732832-38.171214 7.826905-57.196614 12.041392z" fill="#FFFFFF" p-id="19330"></path><path d="M686.35936 224.69238a165.689558 165.689558 0 0 1 153.16651 156.5381c-51.055503 0.60207-102.111007 0-153.286924 0 0.120414-52.380056 0.120414-104.278457 0.120414-156.5381z" fill="#D24625" p-id="19331"></path><path d="M186.64158 314.521167c63.21731 3.130762 139.680151-25.527752 192.662277 22.878645 50.092192 62.374412 36.84666 176.888053-37.44873 214.095955-26.370649 13.847601-56.714958 12.041392-85.373471 10.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z" fill="#FFFFFF" p-id="19332"></path><path d="M255.759172 378.942615c22.878645-0.963311 51.296331-5.298213 66.709313 16.737536a87.902164 87.902164 0 0 1 1.565381 78.148635c-13.245532 24.082785-43.228598 22.035748-66.468485 24.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z" fill="#D24625" p-id="19333"></path></svg>
                    <svg t="1733187804936" class="icon" v-else-if="file.fileSuffix == 'doc' || file.fileSuffix == 'docx'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20412" width="18" height="24"><path d="M950.272 843.776H527.36c-16.384 0-29.696-13.312-29.696-29.696V210.944c0-16.384 13.312-29.696 29.696-29.696h422.912c16.384 0 29.696 13.312 29.696 29.696v603.136c0 16.384-13.312 29.696-29.696 29.696z" fill="#E8E8E8" p-id="20413"></path><path d="M829.44 361.472H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696 0 15.36-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 119.808H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z" fill="#B2B2B2" p-id="20414"></path><path d="M607.232 995.328l-563.2-107.52V135.168l563.2-107.52v967.68z" fill="#0D47A1" p-id="20415"></path><path d="M447.488 696.32h-71.68l-47.104-236.544c-3.072-13.312-4.096-27.648-4.096-40.96h-1.024c-1.024 16.384-3.072 30.72-5.12 40.96L269.312 696.32H194.56l-74.752-368.64h70.656l39.936 245.76c2.048 10.24 3.072 24.576 4.096 41.984h1.024c0-13.312 3.072-27.648 6.144-43.008l51.2-244.736h68.608l47.104 247.808c2.048 9.216 3.072 22.528 4.096 39.936h1.024c1.024-13.312 2.048-26.624 4.096-40.96l39.936-245.76H522.24L447.488 696.32z" fill="#FFFFFF" p-id="20416"></path></svg>
                    <svg t="1733188117407" class="icon"  v-else-if="file.fileSuffix == 'jpg' || file.fileSuffix == 'JPG' || file.fileSuffix == 'jepg' || file.fileSuffix == 'png'|| file.fileSuffix == 'PNG'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39515" width="18" height="14"><path d="M917.09952 84.6848H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856v772.96128c0 32.83456 26.624 59.45344 59.45856 59.45344h802.688c32.83456 0 59.45344-26.61888 59.45344-59.45344V144.13824c0-32.82944-26.61888-59.45344-59.45344-59.45344z" fill="" p-id="39516"></path><path d="M917.09952 54.95296H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856V887.35232c0 32.8448 26.624 59.46368 59.45856 59.46368h802.688c32.83456 0 59.45344-26.61888 59.45344-59.46368V114.41152c0-32.83456-26.61888-59.45856-59.45344-59.45856z" fill="#ECEAE0" p-id="39517"></path><path d="M872.50432 114.41152H159.00672a44.5952 44.5952 0 0 0-44.5952 44.5952V590.07488h802.688V159.00672a44.5952 44.5952 0 0 0-44.5952-44.5952z" fill="#98DCF0" p-id="39518"></path><path d="M613.63712 411.55584l-154.94144 178.51904h309.86752z" fill="#699B54" p-id="39519"></path><path d="M586.82368 590.07488l-206.53568-237.9776-206.5408 237.9776H114.41152V694.12352a44.5952 44.5952 0 0 0 44.5952 44.5952h713.4976a44.5952 44.5952 0 0 0 44.5952-44.5952v-104.05376h-330.27584z" fill="#80BB67" p-id="39520"></path><path d="M768.44544 263.05536m-59.45856 0a59.45856 59.45856 0 1 0 118.91712 0 59.45856 59.45856 0 1 0-118.91712 0Z" fill="#FFE68E" p-id="39521"></path></svg>
                    <svg t="1733188265050" class="icon" v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41415" width="18" height="24"><path d="M847.488 1008.896H61.504V117.056h491.968l294.016 229.952z" fill="#F0A221" p-id="41416"></path><path d="M956.48 901.888H170.496V10.048h556.032l230.016 226.176v665.664zM729.984 102.016v144h144l-144-144z m-104.96 81.984H286.016v57.984h339.008v-57.984z m216.96 164.992H284.992v57.984h556.992v-57.984z m0 169.984H284.992v57.984h556.992v-57.984z m0 165.056H284.992v57.984h556.992v-57.984z" fill="#F1C84C" p-id="41417"></path></svg>
                    <!--                    <a-icon type="file-ppt" v-else-if="file.fileSuffix == 'pptx'"/>-->
                    <!--                    <a-icon type="file-word" v-else-if="file.fileSuffix == 'doc'"/>-->
                  </template>
                </a-tree-node>
              </a-tree-node>


              <a-tree-node :key="item.fileId" :title="item.fileName" is-leaf :on="{click : preview}" :value="item" @select="preview">
                <template slot="icon">
                  <svg t="1733187029740" v-if="item.fileSuffix == 'pdf' || item.fileSuffix == 'PDF'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15201" width="18" height="24"><path d="M870.4 1024h-716.8A51.2 51.2 0 0 1 102.4 972.8v-921.6A51.2 51.2 0 0 1 153.6 0h569.685333a42.666667 42.666667 0 0 1 30.037334 12.288l155.989333 155.989333a42.666667 42.666667 0 0 1 12.288 30.037334V972.8a51.2 51.2 0 0 1-51.2 51.2zM153.6 34.133333a17.066667 17.066667 0 0 0-17.066667 17.066667v921.6a17.066667 17.066667 0 0 0 17.066667 17.066667h716.8a17.066667 17.066667 0 0 0 17.066667-17.066667V198.314667a7.168 7.168 0 0 0-2.389334-5.802667l-155.989333-155.989333a7.168 7.168 0 0 0-5.802667-2.389334z" fill="#4D4D4D" p-id="15202"></path><path d="M904.533333 204.8h-170.666666a17.066667 17.066667 0 0 1-17.066667-17.066667v-170.666666h34.133333V170.666667h153.6z" fill="#4D4D4D" p-id="15203"></path><path d="M204.8 170.666667h443.733333v34.133333H204.8zM204.8 307.2h614.4v34.133333H204.8zM204.8 443.733333h614.4v34.133334H204.8zM204.8 580.266667h614.4v34.133333H204.8zM204.8 853.333333h614.4v34.133334H204.8zM204.8 716.8h614.4v34.133333H204.8z" fill="#B3B3B3" p-id="15204"></path><path d="M51.2 460.8m17.066667 0l887.466666 0q17.066667 0 17.066667 17.066667l0 273.066666q0 17.066667-17.066667 17.066667l-887.466666 0q-17.066667 0-17.066667-17.066667l0-273.066666q0-17.066667 17.066667-17.066667Z" fill="#F33958" p-id="15205"></path><path d="M955.733333 477.866667v273.066666H68.266667v-273.066666h887.466666m0-34.133334H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133334v273.066666a34.133333 34.133333 0 0 0 34.133334 34.133334h887.466666a34.133333 34.133333 0 0 0 34.133334-34.133334v-273.066666a34.133333 34.133333 0 0 0-34.133334-34.133334z" fill="#C42E47" p-id="15206"></path><path d="M348.16 530.090667a55.978667 55.978667 0 1 1 0 111.616H307.2v57.002666h-24.917333v-168.618666zM307.2 618.837333h34.133333c22.528 0 35.84-11.605333 35.84-32.426666s-12.970667-34.133333-35.84-34.133334H307.2zM509.952 530.090667A74.410667 74.410667 0 0 1 589.141333 614.4a75.093333 75.093333 0 0 1-79.189333 84.992h-60.757333v-169.301333z m-34.133333 144.725333h31.744A52.906667 52.906667 0 0 0 562.858667 614.4a53.248 53.248 0 0 0-55.637334-60.074667h-31.744zM636.586667 698.709333v-168.618666h105.130666v23.893333h-78.848v51.882667h72.021334v22.869333h-72.021334v68.266667z" fill="#FFFFFF" p-id="15207"></path></svg>
                  <!--                    <a-icon type="file-pdf" v-if="file.fileSuffix == 'pdf'"/>-->
                  <svg t="1733187630326" v-else-if="item.fileSuffix == 'xlsx' || item.fileSuffix == 'xls'"  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18213" width="18" height="24"><path d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z" fill="#21A366" p-id="18214"></path><path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="18215"></path><path d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z" fill="#33C481" p-id="18216"></path><path d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z" fill="#185C37" p-id="18217"></path><path d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z" opacity=".5" p-id="18218"></path><path d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z" fill="#107C41" p-id="18219"></path><path d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z" fill="#FFFFFF" p-id="18220"></path><path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="18221"></path></svg>
                  <!--                    <a-icon type="file-excel" v-else-if="file.fileSuffix == 'xlxs'"/>-->
                  <svg t="1733187718798" v-else-if="item.fileSuffix == 'pptx'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19328" width="18" height="24"><path d="M538.731891 0h65.98683v107.168391c124.387582 0.722484 248.895579-1.324553 373.28316 0a40.699906 40.699906 0 0 1 45.034808 46.118533c2.047037 222.404516 0 444.929445 1.204139 667.454374-1.204139 24.082785 2.287865 50.694262-11.198495 72.248354-16.978363 12.041392-39.014111 10.957667-59.002822 12.041392-116.319849-0.60207-232.639699 0-349.200376 0V1023.518344h-72.248354C355.100659 990.886171 177.490122 960.662277 0 928.752587V95.488241C179.537159 63.698965 359.074318 31.30762 538.731891 0z" fill="#D24625" p-id="19329"></path><path d="M604.718721 142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532 14.570085 98.619003 14.208843 139.800564-14.088429 44.553151-27.093133 67.793039-78.630292 71.646284-130.047036H663.119473c0-51.777987 0.60207-103.555974-0.963311-155.213547-19.145814 3.732832-38.171214 7.826905-57.196614 12.041392z" fill="#FFFFFF" p-id="19330"></path><path d="M686.35936 224.69238a165.689558 165.689558 0 0 1 153.16651 156.5381c-51.055503 0.60207-102.111007 0-153.286924 0 0.120414-52.380056 0.120414-104.278457 0.120414-156.5381z" fill="#D24625" p-id="19331"></path><path d="M186.64158 314.521167c63.21731 3.130762 139.680151-25.527752 192.662277 22.878645 50.092192 62.374412 36.84666 176.888053-37.44873 214.095955-26.370649 13.847601-56.714958 12.041392-85.373471 10.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z" fill="#FFFFFF" p-id="19332"></path><path d="M255.759172 378.942615c22.878645-0.963311 51.296331-5.298213 66.709313 16.737536a87.902164 87.902164 0 0 1 1.565381 78.148635c-13.245532 24.082785-43.228598 22.035748-66.468485 24.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z" fill="#D24625" p-id="19333"></path></svg>
                  <svg t="1733187804936" class="icon" v-else-if="item.fileSuffix == 'doc' || item.fileSuffix == 'docx'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20412" width="18" height="24"><path d="M950.272 843.776H527.36c-16.384 0-29.696-13.312-29.696-29.696V210.944c0-16.384 13.312-29.696 29.696-29.696h422.912c16.384 0 29.696 13.312 29.696 29.696v603.136c0 16.384-13.312 29.696-29.696 29.696z" fill="#E8E8E8" p-id="20413"></path><path d="M829.44 361.472H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696 0 15.36-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 119.808H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z" fill="#B2B2B2" p-id="20414"></path><path d="M607.232 995.328l-563.2-107.52V135.168l563.2-107.52v967.68z" fill="#0D47A1" p-id="20415"></path><path d="M447.488 696.32h-71.68l-47.104-236.544c-3.072-13.312-4.096-27.648-4.096-40.96h-1.024c-1.024 16.384-3.072 30.72-5.12 40.96L269.312 696.32H194.56l-74.752-368.64h70.656l39.936 245.76c2.048 10.24 3.072 24.576 4.096 41.984h1.024c0-13.312 3.072-27.648 6.144-43.008l51.2-244.736h68.608l47.104 247.808c2.048 9.216 3.072 22.528 4.096 39.936h1.024c1.024-13.312 2.048-26.624 4.096-40.96l39.936-245.76H522.24L447.488 696.32z" fill="#FFFFFF" p-id="20416"></path></svg>
                  <svg t="1733188117407" class="icon"  v-else-if="item.fileSuffix == 'jpg' || item.fileSuffix == 'JPG' || item.fileSuffix == 'jepg' || item.fileSuffix == 'png'|| item.fileSuffix == 'PNG'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39515" width="18" height="14"><path d="M917.09952 84.6848H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856v772.96128c0 32.83456 26.624 59.45344 59.45856 59.45344h802.688c32.83456 0 59.45344-26.61888 59.45344-59.45344V144.13824c0-32.82944-26.61888-59.45344-59.45344-59.45344z" fill="" p-id="39516"></path><path d="M917.09952 54.95296H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856V887.35232c0 32.8448 26.624 59.46368 59.45856 59.46368h802.688c32.83456 0 59.45344-26.61888 59.45344-59.46368V114.41152c0-32.83456-26.61888-59.45856-59.45344-59.45856z" fill="#ECEAE0" p-id="39517"></path><path d="M872.50432 114.41152H159.00672a44.5952 44.5952 0 0 0-44.5952 44.5952V590.07488h802.688V159.00672a44.5952 44.5952 0 0 0-44.5952-44.5952z" fill="#98DCF0" p-id="39518"></path><path d="M613.63712 411.55584l-154.94144 178.51904h309.86752z" fill="#699B54" p-id="39519"></path><path d="M586.82368 590.07488l-206.53568-237.9776-206.5408 237.9776H114.41152V694.12352a44.5952 44.5952 0 0 0 44.5952 44.5952h713.4976a44.5952 44.5952 0 0 0 44.5952-44.5952v-104.05376h-330.27584z" fill="#80BB67" p-id="39520"></path><path d="M768.44544 263.05536m-59.45856 0a59.45856 59.45856 0 1 0 118.91712 0 59.45856 59.45856 0 1 0-118.91712 0Z" fill="#FFE68E" p-id="39521"></path></svg>
                  <svg t="1733188265050" class="icon" v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41415" width="18" height="24"><path d="M847.488 1008.896H61.504V117.056h491.968l294.016 229.952z" fill="#F0A221" p-id="41416"></path><path d="M956.48 901.888H170.496V10.048h556.032l230.016 226.176v665.664zM729.984 102.016v144h144l-144-144z m-104.96 81.984H286.016v57.984h339.008v-57.984z m216.96 164.992H284.992v57.984h556.992v-57.984z m0 169.984H284.992v57.984h556.992v-57.984z m0 165.056H284.992v57.984h556.992v-57.984z" fill="#F1C84C" p-id="41417"></path></svg>
                  <!--                    <a-icon type="file-ppt" v-else-if="file.fileSuffix == 'pptx'"/>-->
                  <!--                    <a-icon type="file-word" v-else-if="file.fileSuffix == 'doc'"/>-->
                </template>
              </a-tree-node>

            </a-tree-node>
          </a-directory-tree>

        </div>
      </div>
      <div class="right">
        <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="handleTabsChange"></pbiTabs>


        <!-- 标题 end -->
        <!-- 筛选 start -->
        <div style="background: white">
          <pbiSearchContainer style="padding: 8px 8px 0 0">
            <pbiSearchItem label='产品名称'  :span="6" >
              <a-input size='small' class="filter-input" @keyup.enter="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
            </pbiSearchItem>
            <pbiSearchItem label='概要'  :span="6" >
              <a-input size='small' class="filter-input" @keyup.enter="$refs.table.refresh()" v-model="queryParam.summary" placeholder="请输入概要" />
            </pbiSearchItem>
            <pbiSearchItem label='发起人' :span="6" >
              <a-input size='small' class="filter-input" @keyup.enter="$refs.table.refresh()" v-model="queryParam.initiatorName" placeholder="请输入发起人" />
            </pbiSearchItem>
            <pbiSearchItem label='FA责任人' :span="6" v-if='isShowAllSearch'>
              <a-input size='small' class="filter-input" @keyup.enter="$refs.table.refresh()" v-model="queryParam.faChargeName" placeholder="请输入FA责任人" />
            </pbiSearchItem>
            <pbiSearchItem label='FA状态' :span="6" v-if='isShowAllSearch'>
              <a-select mode="multiple" :maxTagCount="1" @change="$refs.table.refresh()" v-model="queryParam.faStatusList" placeholder="请选择FA状态" allow-clear >
                <a-select-option v-for="(item,index) in faStatusList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='测试大类' :span="6" v-if='isShowAllSearch'>
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试大类" allow-clear >
                <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='失效类别' :span="6" v-if='isShowAllSearch'>
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
                <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>


            <pbiSearchItem type='btn' :span="isShowAllSearch ? 6 : 6">
              <div class="secondary-btn">
                <a-button class="mr10" @click="$refs.table.refresh()" type="primary">查询</a-button>
              </div>
              <div class="secondary-btn">
                <a-button class="mr10" @click="resetSearch">重置</a-button>
              </div>
              <div class='toggle-btn'>
                <a-button size='small' type='link' @click='handleChangeSearch'>
                  {{ isShowAllSearch ? '收起' : '展开' }}
                  <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
                  <span v-else>
										<a-icon type='double-right'/>
									</span>
                </a-button>
              </div>
            </pbiSearchItem>
          </pbiSearchContainer>

          <div class="table-wrapper double-table-head" style="padding: 0 10px 10px 10px; background-color: white;">
            <s-table :columns="tableColumns"
                     ref="table"
                     :data="loadData"
                     :loading="searchLoading"
                     :rowKey="(record) => record.id"

                     bordered
                     size="middle">
        <span slot="simpleText" slot-scope="text,record">
<!--          <clamp :text="text" :sourceText="text?text:'-'" :isCenter="true"></clamp>-->
          {{text?text:'-'}}
        </span>

              <span slot="clampText" slot-scope="text,record">
          <clamp :text="text" :expend="true" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true"></clamp>
        </span>

              <span slot="productDepartment" slot-scope="text,record">
          {{text?(productDepartmentList.filter(e=>e.id==text).length>0?productDepartmentList.filter(e=>e.id==text)[0].customvalue:text):'-'}}
        </span>

              <span slot="testCate" slot-scope="text,record">
          {{text?(testCateList.filter(e=>e.code==text).length>0?testCateList.filter(e=>e.code==text)[0].value:text):'-'}}
        </span>

              <span slot="failureCate" slot-scope="text,record">
          {{text?failureCateList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

              <span slot="fileCode" slot-scope="text,record">
          <span v-if="record.fileName == null">{{text}}</span>
          <a v-else-if="record.fileName.includes('pdf') || record.fileName.includes('PDF') || record.fileName.includes('png') || record.fileName.includes('jpg') || record.fileName.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>
              <span slot="fileName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>

              <span slot="faBreakReportName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.faBreakReportId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.faBreakReportId)">{{text}}</a>
        </span>

              <span slot="faAnalysisReportName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.faAnalysisReportId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.faAnalysisReportId)">{{text}}</a>
        </span>

              <span slot="reportProcess" slot-scope="text,record">
                <span v-if="!Array.isArray(record.reportProcessList)">分析报告流程</span>
                <div v-else>
                  <div v-for="(reportProcessObj, index) in record.reportProcessList" :style="{ borderBottom : index === record.reportProcessList.length - 1 ? 'none' : '1px solid #e8e8e8' }">
                    <a @click="handleClickProcess(record, reportProcessObj)">分析报告流程{{ record.reportProcessList.length > 1 ? index + 1 : '' }}</a>
                  </div>
                </div>
              </span>
              <span slot="assignProcess" slot-scope="text,record">
                <span v-if="record.faResponsibleAssignIssueKey==null">查看责任人</span>
                <a v-else @click="handleToJira(record.faResponsibleAssignIssueKey)">查看责任人</a>
              </span>

              <template slot="overallFaStatusTitle">
                整体FA状态
                <a-tooltip :overlayStyle="{ maxWidth: '616px' }">
                  <template slot="title"><img src="./picture/overallFaStatus.png" alt="" /></template>
                  <a-icon type="question-circle" style="margin-left: 5px; color: #1890ff;"/>
                </a-tooltip>
              </template>

              <template slot="analysisContent" slot-scope="text, record, index, columns">
                <clamp :text="text" :expend="true" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true"></clamp>
<!--                <div v-if="Array.isArray(record.reportProcessList) && record.reportProcessList.length > 1" style="margin-top: 2px; border-top: 1px solid #e8e8e8;">-->
                  <div style="margin-top: 2px; border-top: 1px solid #e8e8e8;">
                  <a-tooltip title="历史内容">
                    <a @click="handleShowFaHistory(record, columns.dataIndex,columns.title[0].elm.textContent,'text')"><a-icon type="read"/></a>
                  </a-tooltip>
                </div>
              </template>
              <template slot="analysisFile" slot-scope="text, record, index, columns">
                <a v-if="text && record.latestProcessObj[`${columns.dataIndex}Id`]"
                   @click="handleFileNameClick(text, record.latestProcessObj[`${columns.dataIndex}Id`])">
                  {{ text }}
                </a>
                <span v-else>{{ text }}</span>
<!--                v-if="Array.isArray(record.reportProcessList) && record.reportProcessList.length > 1"-->
                <div  style="margin-top: 2px; border-top: 1px solid #e8e8e8;">
                  <a-tooltip title="历史文件">
                    <a @click="handleShowFaHistory(record, columns.dataIndex,columns.title[0].elm.textContent,'file')"><a-icon type="read"/></a>
                  </a-tooltip>
                </div>
              </template>

            </s-table>
          </div>
        </div>

      </div>
    </div>
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="80%"
      :destroyOnClose="true"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>

    <a-modal title="查看历史文件" :width="700" :visible="isShowModal" @cancel="isShowModal = false">
      <a-list size="small" :bordered="true" :dataSource="fileList">
        <a-list-item slot="renderItem" slot-scope="item, index">
          <div style="cursor: pointer;" :style="{ color: item.fileName && item.fileId ? '#1890ff' : '#333' }"
                @click="handleFileNameClick(item.fileName, item.fileId, 'isShowModal')">
            {{ item.fileName }}
          </div>
        </a-list-item>
      </a-list>
    </a-modal>
      <a-modal title="查看历史内容" :width="700" :visible="isShowContentModal" @cancel="isShowContentModal = false">
        <a-list size="small" :bordered="true" :dataSource="dataList">
          <a-list-item slot="renderItem" slot-scope="item, index">
            {{ item }}
          </a-list-item>
        </a-list>
      </a-modal>

    </a-spin>

    <faFieldFill ref="faFieldFill" @ok="$refs.table.refresh()"></faFieldFill>
    <fa-history ref="faHistory" @handleFileNameClick="previewFile"></fa-history>
  </div>
</template>

<script>
import {competitiveAnalysisPageList,competitiveAnalysisEchartsData,competitiveAnalysisTreeData} from "@/api/modular/system/competitveAnalysisManager"
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Vue from "vue";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
import {clamp, STable} from "@/components";
import {getDpvTestFailureList, getDpvTestFailureListPage,getDpvTestFailureTreeData} from "@/api/modular/system/testFailure";
import {getUserLists} from "@/api/modular/system/userManage";
import {getMinioDownloadUrl, sysFileInfoDownload} from "@/api/modular/system/fileManage";
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
import {downloadMinioFile} from "@/utils/util"
import jsonBigint from "json-bigint";
import {getDepartmentOptionList} from "@/api/modular/system/qualityManage";
import faFieldFill from './faFieldFill'
import faHistory from './faHistory';
const jsonBigintStr = jsonBigint({storeAsString: true})

export default {
  components: {STable, clamp, pbiTabs, faFieldFill, faHistory},
  props: {
    listType: {
      type: Number,
      default: 0
    },
    issueId: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      isDownloading: false,
      isShowModal: false,
      fileList: [],
      isShowContentModal: false,
      dataList: [],
      loadData: parameter => {
        // this.queryParam.productDepartment = this.productDepartment
        this.queryParam.reviewStatus = 2//FA显示审核过后的数据
        this.queryParam.laboratoryId = this.laboratoryId

        return getDpvTestFailureListPage(Object.assign(parameter, this.queryParam)).then((res) => {
          this.tableListLength = res.data.rows.length

          // 解析列表
          res.data.rows.forEach(record => {
            record.latestProcessObj = {}
            const reportProcessList = jsonBigintStr.parse(record.faAnalyseReportIssueKey)
            if (Array.isArray(reportProcessList) && reportProcessList.length > 0) {
              record.reportProcessList = reportProcessList
              record.latestProcessObj = reportProcessList[reportProcessList.length - 1]
            }
          })

          return res.data
        })
      },
      tableListLength: 0,
      laboratoryList:[{value:'',label:"全部"},{value:"HZ_YJ_DL_AQ",label:"第四实验室"},{value:"HZ_YJ_DL_JM",label:"精密实验室"},{value:"HZ_YJ_DL_CS",label:"第六实验室(HZ)"}],
      laboratoryId:'',
      productDepartment:null,
      fileUrl:'',
      previewBaseUrl:'/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',
      addVisible: false,
      addLoading: false,
      addForm: this.$form.createForm(this, { name: 'addForm' }),

      //{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}
      productDepartmentList:[],
      orderTypeList:[{code:'G圆柱',value:'G圆柱'},{code:'C圆柱',value:'C圆柱'},{code:'方型',value:'方型'},{code:'软包',value:'软包'},{code:'V圆柱',value:'V圆柱'}],
      projectLevelList:[{code:'S',value:'S'},{code:'A',value:'A'},{code:'B',value:'B'},{code:'C',value:'C'}],
      researchStageList:[{code:'A样',value:'A样'},{code:'B样',value:'B样'},{code:'C样',value:'C样'}],
      faStatusList:[{code:"beStart",value:"待启动"},{code:"onGoing",value:"进行中"},{code:"finish",value:"完成"}],
      testCateList:[{code:"电性能",value:"电性能"},{code:"循环寿命",value:"循环寿命"},{code:"日历寿命",value:"日历寿命"},{code:"安全",value:"安全"},{code:"精密",value:"精密"}],
      failureCateList:[{code:'1',value:"不满足指标"},{code:'2',value:"起火"},{code:'3',value:"漏液"},{code:'4',value:"壳体开裂"},{code:'5',value:"其它"}],
      reviewResultList:['请选择', '通过', '驳回'],
      faReportFieldMap:{
        causeAnalysis: '原因分析',
        tempMeasures: '临时措施',
        longTermMeasures: '长期措施',
        resultVerification: '结果验证',
        faBreakReport: '拆解报告',
        faAnalysisReportName: '分析报告',
      },
      selectUserLoading: false,
      selectUserColumns: [{
        title: '账号',
        dataIndex: 'account'
      }, {
        title: '姓名',
        dataIndex: 'name'
      }],
      //提出人
      presenterVisible: false,
      userQueryParam: {},
      presenterName: '',
      userLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.userQueryParam)).then((res) => {
          return res.data
        })
      },

      //待办相关
      showTodoPushBtn: this.hasPerm("oaTodo:productProblem"),
      todoPushConfirmLoading: false,
      todoPushVisible: false,
      form: this.$form.createForm(this),
      dropdownvisible: false,
      userNameDisplay: "",


      queryParam: {
        productName: '',
        projectName: '',
        faChargeName: '',
        summary: '',
        faStatusList: [],
        testCateList: [],
        failureCateList: [],
        // keyWord: '',
        queryType: 'noChildren',
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 6}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 18}
      },
      searchLoading: false,
      searchDataTimer: 0,
      problemDimensionList: [],
      problemStatusList: [],
      problemCateList: [],
      projectStageList: [],
      productList: [],
      tableColumns: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          align: 'center',
          width: 60,
          customRender: (text, record, index) => record.parentId == 1?`${index+1}`:'',
        },
        {
          title: '流程',
          dataIndex: 'reportProcess',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'reportProcess' }
        },
        {
          title: 'FA相关信息',
          children: [
            {
              title: '产品名称',
              dataIndex: 'productName',
              align: 'center',
              width: 100,
            },
            {
              title: '发起人',
              dataIndex: 'initiatorName',
              align: 'center',
              width: 100,
              key: 'initiator',
            },
            {
              title: 'FA责任人',
              dataIndex: 'faChargeName',
              align: 'center',
              width: 100,
            },
            {
              title: 'FA期限',
              dataIndex: 'faDeadline',
              align: 'center',
              width: 100,
            },
            {
              dataIndex: 'overallFaStatus',
              align: 'center',
              width: 100,
              customRender: (text, record, index) => {
                if(text == 'finish'){
                  return '完成'
                }
                if(text == 'onGoing'){
                  return '进行中'
                }
                return '待启动'
              },
              scopedSlots: { title: 'overallFaStatusTitle' }
            },
            {
              title: '原因分析',
              dataIndex: 'causeAnalysis',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'analysisContent' }
            },
            {
              title: '临时措施',
              dataIndex: 'tempMeasures',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'analysisContent' }
            },
            {
              title: '长期措施',
              dataIndex: 'longTermMeasures',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'analysisContent' }
            },
            {
              title: '结果验证',
              dataIndex: 'resultVerification',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'analysisContent' }
            },
            {
              title: '拆解报告',
              dataIndex: 'faBreakReport',
              align: 'center',
              width: 130,
              scopedSlots: { customRender: 'analysisFile' }
            },
            {
              title: '分析报告',
              dataIndex: 'faAnalysisReport',
              align: 'center',
              width: 130,
              scopedSlots: { customRender: 'analysisFile' }
            },

          ],
        },
        {
          title: '电芯基本信息',
          children: [
            {
              title: '产品所属研究所',
              dataIndex: 'productDepartment',
              align: 'center',
              width: 150,
              ellipsis:true,
              scopedSlots: { customRender: 'productDepartment' }
            },
            {
              title: '样品类型',
              dataIndex: 'orderType',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'orderType' }
            },
            {
              title: '项目等级',
              dataIndex: 'projectLevel',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'projectLevel' }
            },
            {
              title: '研制阶段',
              dataIndex: 'researchStage',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'researchStage' }
            },
            {
              title: '测试样品阶段',
              dataIndex: 'testSampleStage',
              align: 'center',
              width: 100,
              // scopedSlots: { customRender: 'testSampleStage' }
            },
            {
              title: '样品数量',
              dataIndex: 'testSampleNum',
              align: 'center',
              width: 100,
              /*
              customRender: (text, record, index)  => {
                if (record.parentId != 1) {//1为父级，显示数量，否则不显示
                  return '';
                }
                if (record.children == null|| record.children.length == 0) {
                  return 1;
                }
                return record.children.length + 1;
              }
               */
            },
            {
              title: '概要',
              dataIndex: 'summary',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
          ],
        },
        {
          title: '电芯测试信息',
          children: [
            {
              title: '委托单号',
              dataIndex: 'folderNo',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试项目名称',
              dataIndex: 'testProjectName',
              align: 'center',
              width: 125,
              scopedSlots: { customRender: 'clampText' }
            },
            {
              title: '电芯数据',
              dataIndex: 'orderNos',
              align: 'center',
              width: 100,
              ellipsis:true,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '电芯批次',
              dataIndex: 'cellBatch',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试大类',
              dataIndex: 'testCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'testCate' }
            },
            {
              title: '失效类别',
              dataIndex: 'failureCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'failureCate' }
            },
            {
              title: '测试失效描述',
              dataIndex: 'testFailureDescription',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'clampText' }
            },
          ],
        },
        {
          title: '测试失效告知书',
          children: [
            {
              title: '发起人',
              dataIndex: 'initiatorName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '发起时间',
              dataIndex: 'initiationTime',
              align: 'center',
              width: 100,
            },
            {
              title: '文件编号',
              dataIndex: 'fileCode',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'fileCode' }
            },
            // {
            //   title: '附件',
            //   dataIndex: 'fileName',
            //   align: 'center',
            //   width: 100,
            //   scopedSlots: { customRender: 'fileName' }
            // },
          ],
        },
        {
          title: '流程查看',
          dataIndex: 'assignProcess',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'assignProcess' }
        },
      ],
      tableData: [],
      initData: {},
      iframeUrl:'',
      filePreviewVisible:false,
      fileName:null,
      pageNo: 1,
      pageSize: 20,
      tableTotal:0,
      echartsData:{
        allCount: 0,
        allDisassembleCount:0,
        allSavingCount:0,
        allTestingCount:0
      },
      defaultColDef: {
        flex: 1,
        minWidth: 110,
        filter: false,
        floatingFilter: false,
        editable: false,
      },

      isShowAllSearch: false,
      templateHeight: document.documentElement.clientHeight - 40 - 2*10 - 22 - 8 - 37,
      tableHeight: 0,
      loading: true,
      columns: [
        {
          headerName: '序号',
          field: 'no',
          minWidth: 50,
          maxWidth: 60,
          initialWidth: 50,
          width: 50,
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          },
        },
        {
          headerName: '竞品管理单号',
          field: 'code',
          minWidth: 110,
          initialWidth: 110,
          width: 110,
        },
        {
          headerName: '状态',
          field: 'status',
          minWidth: 100,
          initialWidth: 100,
          width: 100,
          cellRenderer: function (params) {
            return params.value == 'finished'?'完成':params.value == 'ongoing'?'进行中':params.value
          },
        },{
          headerName: '竞品分析报告',
          field: 'reportName',
          minWidth: 100,
          initialWidth: 100,
          width: 100,
        },{
          headerName: '厂商',
          field: 'factory',
          minWidth: 100,
          initialWidth: 100,
          width: 100,
        },{
          headerName: '型号',
          field: 'model',
          minWidth: 50,
          initialWidth: 50,
          width: 50,
        },{
          headerName: '尺寸',
          field: 'competitiveSize',
          minWidth: 50,
          initialWidth: 50,
          width: 50,
        },{
          headerName: '化学体系',
          field: 'chemicalSystem',
          minWidth: 50,
          initialWidth: 50,
          width: 50,
        },{
          headerName: '容量',
          field: 'capacity',
          minWidth: 50,
          initialWidth: 50,
          width: 50,
        },{
          headerName: '应用领域',
          field: 'applicationArea',
          minWidth: 50,
          initialWidth: 50,
          width: 50,
        }

      ],
      pageData: [],
      allResultData: [],
      treeData:{},
      competitiveType:'chemicalSystem'
    }
  },
  watch: {
    tableListLength(newVal, oldVal) {

      // document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      if (newVal === 0 || oldVal === 0) {
        this.tableHeight = this.templateHeight - 48 - ( this.isShowAllSearch ? 40 : 0 ) - ( newVal === 0 ? 0 : 25 + 8 ) - 10
        document.documentElement.style.setProperty(`--height`, `${this.tableHeight}px`)
      }

    },
    isShowAllSearch(newVal, oldVal) {

      // document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      this.tableHeight = this.templateHeight - 48 - ( this.isShowAllSearch ? 40 : 0 ) - ( newVal === 0 ? 0 : 25 + 8 ) - 10
      document.documentElement.style.setProperty(`--height`, `${this.tableHeight}px`)
    }
  },
  methods: {
    handleClickProcess(record, reportProcessObj) {
      const list = ['onGoing', 'reviewRejected']
      if (list.includes(reportProcessObj.faStatus)) {
        this.$refs.faFieldFill.handleInit(record, reportProcessObj)
      } else {
        this.handleToJira(reportProcessObj.faAnalyseReportIssueKey)
      }
    },
    handleShowFileList(reportProcessList, dataIndex) {
      let key = dataIndex
      const splits = dataIndex.split('.')
      if (Array.isArray(splits) && splits.length > 0) {
        key = splits[splits.length - 1]
      }
      this.fileList = reportProcessList.map(item => {
        return {fileId: item[`${key.slice(0, -4)}Id`], fileName: item[key] ?? '-'}
      })
      this.isShowModal = true
    },
    handleShowFaHistory(record,dataIndex,title,type){
      console.log("title", title);
      this.$refs.faHistory.view(record, dataIndex,title,type)
    },
    handleShowContentList(reportProcessList, dataIndex) {
      let key = dataIndex
      const splits = dataIndex.split('.')
      if (Array.isArray(splits) && splits.length > 0) {
        key = splits[splits.length - 1]
      }
      this.dataList = reportProcessList.map(item => item[key] ?? '-')
      this.isShowContentModal = true
    },
    handleFileNameClick(fileName, fileId, target = null) {
      // console.log("fileName, fileId, target: ", fileName, fileId, target)
      if (fileName && fileId) {
        if (/pdf|png|jpg|jpeg/i.test(fileName)) {
          this.previewFile(fileId)
        } else {
          this.callFileInfoDownload(fileId)
        }

        // 关闭弹窗
        if (target) {
          this[target] = !this[target]
        }
      }
    },
    handleOk() {
      this.getData(this.queryParam);
    },
    filePreviewOnClose(){
      this.filePreviewVisible = false;
    },
    handleToJira(issueKey) {
      console.log(issueKey);
      if (issueKey == null) {
        return;
      }
      let $url = `http://jira.evebattery.com/browse/` + issueKey + `?auth=` + Vue.ls.get("jtoken");
      console.log($url);
      // let $url = `http://jira.evebattery.com/browse/` + record.issueKey;
      window.open($url, "_blank");
    },
    previewFile(fileId) {
      this.iframeUrl = this.previewBaseUrl + fileId;
      this.filePreviewVisible = true;
    },
    callFileInfoDownload (fileId) {
      this.isDownloading = true
      sysFileInfoDownload({ id: fileId }).then((res) => {
        this.downloadfile(res)
      }).catch((err) => {
        this.$message.error('下载错误：获取文件流错误')
      }).finally(() => {
        this.isDownloading = false
      })
    },
    handleChangeSearch() {
      this.isShowAllSearch = !this.isShowAllSearch;
      // this.tableHeight = this.isShowAllSearch ? this.templateHeight - 80 : this.templateHeight - 40
    },
    handleTabsChange(value) {
      this.laboratoryId = value
      // this.filterData = ''
      // this.getTodoTaskList()
      this.$refs.table.refresh()
    },
    downloadfile (res) {
      var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
      var contentDisposition = res.headers['content-disposition']
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var result = patt.exec(contentDisposition)
      var filename = result[1]
      var downloadElement = document.createElement('a')
      var href = window.URL.createObjectURL(blob) // 创建下载的链接
      var reg = /^["](.*)["]$/g
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    },

    resetSearch() {
      this.queryParam.fileCode = null
      this.queryParam.productName = null
      this.queryParam.cellCode = null
      this.queryParam.faChargeName = null
      this.queryParam.summary = null
      this.queryParam.faStatusList = []
      this.queryParam.testCateList = []
      this.queryParam.failureCateList = []
      this.$refs.table.refresh()
    },

    getData(queryParam) {
      this.searchLoading = true
      getDpvTestFailureList(queryParam).then(res => {
        this.tableData = res.data
      }).finally(res => {
        this.searchLoading = false
      })
    },
    getJiraOptionList(){
      getJiraOptionList({fieldName:'department'}).then(res => {
        var list=['18846','22101','22105','18863','18711','22269'];
        this.productDepartmentList = []
        for (let i = 0; i < list.length; i++) {
          this.productDepartmentList.push(res.data.find(e => list[i] == e.id))
        }

        // this.productDepartmentList = res.data.filter(e => list.includes(e.id));
        console.log(this.productDepartmentList);
        // this.productDepartmentList = res.data;
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    reset(){
      this.queryParam = {}
      this.getPageData()
    },
    preview(data,data2){
      if(data2.node.value){
        // console.log("data, data2, data2.node.value: ", data, data2, data2.node.value)
        if (/pdf|png|jpg|jpeg/i.test(data2.node.value.fileName)) {
          this.previewFile(data2.node.value.fileId)
        } else {
          this.callFileInfoDownload(data2.node.value.fileId)
        }
      }
    },

    tableFocus() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      this.$el.style.setProperty('--scroll-display', 'unset');
      this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      this.$el.style.setProperty('--scroll-display', 'none');
      this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      //数据分页
      this.getPageData()

    },

    initChart() {
      let chart = this.echarts.init(this.$refs.gainOverview)

      chart.clear()
      let series = []
      for (let i = 0; i < this.echartsData.series.length; i++) {
        let serie =  this.echartsData.series[i]
        series.push({
          name: serie.name,
          type: serie.type,
          showBackground: true,
          backgroundStyle: {
            color: '#f2f2f2',
            borderRadius: 10,
          },
          yAxisIndex: serie.yAxisIndex,
          barWidth: '20%',
          tooltip: {
            valueFormatter: (value) => serie.yAxisIndex < 1? value : value + '%'
          },
          itemStyle: {
            color:this.getColor(i,this.echartsData.series.length),
            borderRadius: 10
          },
          data: serie.data
        })
      }

      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          right:'0%',
          bottom:'10%',
          orient:'vertical'
        },
        grid: {
          left: '3%',
          top:'12%',
          right: '15%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.echartsData.xData,
            axisLine: { show: false },
            axisTick: { show: false },
          }
        ],
        yAxis: [
          {
            type: 'value',
            show:false
          },
          {
            type: 'value',
            show:false
          }

        ],

        series: series
      };
      chart.setOption(options)
      chart.resize()
    },
    getTreeData(){
      getDpvTestFailureTreeData({fileName:this.fileName}).then(res => {
        this.treeData = res.data
      })
    },
    getEchartsData(){
      competitiveAnalysisEchartsData({competitiveType:this.competitiveType}).then(res => {
        this.echartsData = res.data
      }).then(() => {
        this.initChart()
      })
    },
    getColor(i,length){
      if (i == length -1){
        return '#00f8e1'
      }else if (i == 0){

        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#188df0' // 0% 处的颜色
          }, {
            offset: 1, color: '#5faff3' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }else if (i == 1){
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#9bcfff' // 0% 处的颜色
          }, {
            offset: 1, color: '#d1e9ff' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }else {
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#d1e9ff' // 0% 处的颜色
          }, {
            offset: 1, color: '#eaf4fd' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    },
    getPageData(){
      this.loading = true
      competitiveAnalysisPageList({
        ...{
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.pageData = res.data.rows
          this.tableTotal = res.data.totalRows

        }
      }).finally(() => {
        if(this.pageData.length == 0 && this.pageNo > 1){
          // this.pageNo -= 1
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.getPageData()
        }
        this.loading = false
      })
    },
    async initGain() {
      // await this.getPageData()
      // await this.getEchartsData()
      this.getTreeData()

      /*await this.callGetTree()
      await this.callGetDeptTree()
      await this.callAffiliatedPlatformTree()
      await this.callGetResultsByParam()
      await this.getAllPassTopic()*/
      // await this.getAllResults()
    }
  },
  created() {
    this.tableHeight = this.templateHeight - 48 - ( this.isShowAllSearch ? 40 : 0 ) - ( this.tableListLength === 0 ? 0 : 24 + 8 ) - 10

    getDepartmentOptionList({fieldName:'PBI_ProductDepartment'}).then(res => {
      this.productDepartmentList = res.data;
    })
  },
  mounted() {
    this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
    this.$el.style.setProperty('--scroll-display', 'none');
    this.$el.style.setProperty('--scroll-border-bottom', 'none');
    this.initGain()
  }
}
</script>


<style lang="less" scoped="">
//@import './topic.less';
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
  --scroll-border-bottom: none;
  --scroll-border-bottom-fixed: none;
  --height: 600px;
}
/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}



/deep/ .ant-breadcrumb{
  font-size: 12px;
}

/deep/.left .ant-input,
/deep/.left .ant-btn{
  height: 28px;
  font-size: 12px;
  border-radius: 4px;
}

/deep/.ant-btn > .anticon + span,
/deep/.ant-btn > span + .anticon{
  margin-left: 4px;
}

/deep/.ant-radio-group{
  font-size: 12px;
  color: #999;
}

/deep/.ant-radio-button-wrapper{
  border: 1px solid #1890ff;
  height: 24px;
  line-height: 23px;
  padding: 0 12px;
}

/deep/.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled){
  color: #fff;
  background-color: #1890ff;
}

/deep/.ant-radio-button-wrapper:first-child{
  border-radius: 4px 0 0 4px;
}

/deep/.ant-radio-button-wrapper:last-child{
  border-radius: 0 4px 4px 0;
}


.page-container {
  height: auto;
  border-radius: 4px;
  margin: 0 !important;
  padding: 12px;
}

/deep/ .ant-modal-footer {
  padding-top: 0;
}

.container {
  height: calc(100vh - 40px);
  background: #f4f5fc;
  color: #333;
}

.content {
  height: calc(100% - 27px);
  display: flex;
}

/* 主标题 */

.head-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.head-headerName::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; /* 填充空格 */

  color: #5aaef4;
}

.head-content {
  padding: 0 0 10px;
}

.left {
  width: 300px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 12px;
}

.left-top {
  width: 100%;
  margin-bottom: 12px;
}

.right-top{
  background: #fff;
  border-radius: 100px;
  height: calc(30%);
  margin-bottom: 12px;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
}

.left-bottom {
  width: 100%;
  height: calc(100% - 50px);
  position: relative;
  overflow: auto;
}

.right {
  width: calc(100% - 300px);
  border-radius: 10px;
}

.top {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 50px;
}

.center {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc((65% - 10px) * 0.4);
}

.empty-block {
  height: calc(100% - 64px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-block-left {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-25%, -75%);
}

.bottom {
  height: calc(100% - 60px);
}

.block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #fff;
  border-radius: 10px;
  padding: 10px;
}

.block span,
.overview-block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #FFF;
  border-radius: 4px;
  padding: 12px;
}

.left-top span {
  font-size: 14px;
  /* font-weight: 600; */
}

.dept {
  display: flex;
  justify-content: space-between;
}

.dept span {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.chart_table {
  height: calc(100% - 20px);
}

.chart_bar_table {
  height: calc(100% - 24px);
  margin-top: calc(24px);
}

.table-wrap {
  height: calc(100% - 30px);
  overflow: auto;
}

.table-wrap button {
  z-index: 88;
}

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
}

.mt10 {
  margin: 10px;
}

.mr10 {
  margin-right: 10px;
}


/deep/ .ant-pagination-options-size-changer.ant-select {
  display: inline-block;
}
/deep/.search-container .vue-treeselect__multi-value-label{
  white-space: nowrap;
  max-width: 41px;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/.search-container .vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value .vue-treeselect__input-container{
  display: none;
}
/deep/.search-container .vue-treeselect__limit-tip-text{
  margin: 0;
  margin-top: 4px;
  font-weight: initial;
  text-indent: -44px;
  overflow: hidden;
  display: none;
}
/deep/.ant-tree li{
  width:100px
}

/deep/.ant-select {
  width: 100%;
}
/deep/.ant-tree-title{
  font-size: 12px;
}

/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
  font-size: 13px;
  color: rgba(0, 0, 0, .85);
  font-weight: 500;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td{
  padding: 4px;
  color: #333;
  font-size: 12px;
  font-weight: 400;
}
/deep/.ant-select-sm .ant-select-selection--single {
  height: 24px;
}
/deep/.ant-select-sm .ant-select-selection__rendered {
  line-height: 24px;
}
/deep/.ant-table-placeholder{
  display: none;
}
/deep/.ant-select-selection--multiple {
  min-height: 28px;
}
/deep/.ant-select-selection__rendered {
  position: unset!important;
}

/deep/ .s-table-tool {
  padding-bottom: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
  margin: 8px 0 0 0;
}
</style>