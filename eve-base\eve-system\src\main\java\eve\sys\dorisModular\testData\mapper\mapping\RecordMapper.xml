<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eve.sys.dorisModular.testData.mapper.RecordMapper">



    <insert id="batchInsertRecords" parameterType="java.util.List">
        INSERT INTO sync_data.record (
        record_id, step_num, step_id, cycle_id, flow_id, step_name,
        absolute_time, record_time, step_time, voltage, current1,
        capacity, energy, power, accumulate_charge_capacity,
        accumulate_discharge_capacity, accumulate_charge_energy,
        accumulate_discharge_energy, total_capacity, aux_tem1,
        aux_tem2, aux_tem3, aux_tem4, unit_num, channel_id,
        barcode, sync_date, create_time
        ) VALUES
        <foreach collection="list" item="record" index="index" separator=",">
            (
            #{record.recordId},
            #{record.stepNum},
            #{record.stepId},
            #{record.cycleId},
            #{record.flowId},
            #{record.stepName},
            #{record.absoluteTime},
            #{record.recordTime},
            #{record.stepTime},
            #{record.voltage},
            #{record.current1},
            #{record.capacity},
            #{record.energy},
            #{record.power},
            #{record.accumulateChargeCapacity},
            #{record.accumulateDischargeCapacity},
            #{record.accumulateChargeEnergy},
            #{record.accumulateDischargeEnergy},
            #{record.totalCapacity},
            #{record.auxTem1},
            #{record.auxTem2},
            #{record.auxTem3},
            #{record.auxTem4},
            #{record.unitNum},
            #{record.channelId},
            #{record.barcode},
            #{record.syncDate},
            #{record.createTime}
            )
        </foreach>
    </insert>

    <select id="selectRecordId" resultType="int">
        select record_id from sync_data.record
        ${ew.customSqlSegment}
        ORDER BY record_id ASC
        limit #{num},1

    </select>

    <select id="selectRecordPage" resultType="eve.sys.dorisModular.testData.entity.Record">
        SELECT r.*
        FROM sync_data.record r,
             ( SELECT record_id, flow_id FROM sync_data.record ${ew.customSqlSegment} ORDER BY record_id ASC LIMIT #{start}, #{pageSize} ) temp
        WHERE r.record_id = temp.record_id and r.flow_id = temp.flow_id
        ORDER BY r.record_id;
    </select>


</mapper>
