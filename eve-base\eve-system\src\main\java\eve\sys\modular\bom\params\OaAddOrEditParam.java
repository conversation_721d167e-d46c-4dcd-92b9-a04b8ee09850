package eve.sys.modular.bom.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OaAddOrEditParam {
    private String flag;//标志
    private String mPartName;//父物料名
    private String mPartDescription;// 父物料描述
    private String partName;//物料名
    private String partDescription;// 物料描述
    private String mSapNumber;// sap父编号
    private String sapNumber;// sap编号
    private String id;// 自定义行号
    private String prePartUnit;// 物料单位
    private String partUnit;// 物料单位
    private String bomStartdate; // 生效日期
    private String version; // 版本
    private String bomNo; // 版本编号
    private double prePartUse; // 变更前用量
    private double partUse; // 用量
    private double sapPrePartUse; // 变更前sap用量
    private double sapPartUse; // sap用量
    private double prePartLoss; // 变更前损耗率
    private double partLoss; // 损耗率
    private String desc;

    public String getBuildKey(){
        return this.getMSapNumber()+this.getSapNumber()+ String.valueOf(this.getFlag().equals("删除") ? this.getSapPrePartUse() : this.getSapPartUse());
    }
}
