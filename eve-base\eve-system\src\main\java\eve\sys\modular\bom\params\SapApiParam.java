package eve.sys.modular.bom.params;

import eve.core.context.constant.ConstantContextHolder;

public interface SapApiParam {
    String BaseUrl = ConstantContextHolder.getSysConfigWithDefault("SAP_URL",String.class,"");//"http://*********:8080/gateway/dev/sap/";
    String FactoryApi = "v_werks";//"v_plm_werks";
    String Appkey = "61dd2c64ee02641cb1558a5e";

    String BomAddApi = "ZFM_OA_PP_001";
    String BomEditApi = "ZFM_PLM_PP_002";
    String BomEditDcn = "ZFM_PLM_PP_001";
    String BomScan = "ZFM_FR_PP_001";
    String BomGetVersion = "ZFM_JIRA_PP_001"; 

    String BomExtendApi = "v_fr_marc";
}
