package eve.sys.jiraModular.qualityManager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.context.login.LoginContextHolder;
import eve.core.exception.ServiceException;
import eve.core.pojo.login.SysLoginUser;
import eve.sys.jiraModular.qualityManager.entity.QualityProblem;
import eve.sys.jiraModular.qualityManager.mapper.QualityProblemMapper;
import eve.sys.jiraModular.qualityManager.service.IQualityProblemService;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@Service
@DS("b2")
@Slf4j
public class QualityProblemServiceImpl extends ServiceImpl<QualityProblemMapper, QualityProblem> implements IQualityProblemService {
    @Override
    public List<QualityProblem> list(QualityProblem param) throws ParseException {

        LambdaQueryWrapper<QualityProblem> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(param.getReporterId())) {//报告人
            queryWrapper.eq(QualityProblem::getReporterId, param.getReporterId().toLowerCase());
        }
        if (ObjectUtil.isNotEmpty(param.getStatusLamp())) {//状态灯
            queryWrapper.eq(QualityProblem::getStatusLamp, param.getStatusLamp());
        }
        if (ObjectUtil.isNotEmpty(param.getStatusLampList())) {//状态灯
            queryWrapper.in(QualityProblem::getStatusLamp, param.getStatusLampList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemStatus())) {//问题状态 0 close 1 open
            queryWrapper.eq(QualityProblem::getProblemStatus, param.getProblemStatus());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemStatusList())) {//问题状态 0 close 1 open
            queryWrapper.in(QualityProblem::getProblemStatus, param.getProblemStatusList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemLevel())) {//问题等级
            queryWrapper.eq(QualityProblem::getProblemLevel, param.getProblemLevel());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemLevelList())) {//问题等级
            queryWrapper.in(QualityProblem::getProblemLevel, param.getProblemLevelList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemCate())) {//问题分类
            queryWrapper.eq(QualityProblem::getProblemCate, param.getProblemCate());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemCateList())) {//问题分类
            queryWrapper.in(QualityProblem::getProblemCate, param.getProblemCateList());
        }
        if (ObjectUtil.isNotEmpty(param.getProductName())) {//产品名称
            queryWrapper.like(QualityProblem::getProductName, param.getProductName());
        }
        if (ObjectUtil.isNotEmpty(param.getProjectDepartmentList())) {//问题分类
            queryWrapper.in(QualityProblem::getProjectDepartment, param.getProjectDepartmentList());
        }
        if (ObjectUtil.isNotEmpty(param.getInstituteList())) {//研究院
            queryWrapper.in(QualityProblem::getInstitute, param.getInstituteList());
        }
        if (ObjectUtil.isNotEmpty(param.getResolution())) {//解决结果ID
            queryWrapper.in(QualityProblem::getResolution, param.getResolution());
        }
        if (ObjectUtil.isNotEmpty(param.getKeyWord())) {//产品名称、项目名称、提出人、责任人 模糊搜索
            queryWrapper.and(qw -> qw
                    .like(QualityProblem::getProductName, param.getKeyWord()).or()
                    .like(QualityProblem::getProjectName, param.getKeyWord()).or()
                    .like(QualityProblem::getPresenterName, param.getKeyWord()).or()
                    .like(QualityProblem::getResponsiblePersonName, param.getKeyWord()));
        }
        queryWrapper.orderByAsc(QualityProblem::getProblemLevel).orderByDesc(QualityProblem::getFindDate);
        List<QualityProblem> qualityProblemList = this.list(queryWrapper);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        for (QualityProblem qualityProblem : qualityProblemList) {
            if (qualityProblem.getActualCompleteDate() != null) {
                continue;
            }
            String planCompleteDateStr = qualityProblem.getPlanCompleteDate();
            if (planCompleteDateStr == null) {
                qualityProblem.setDelayDay(0L);
            } else {
                Date date = new Date();
                Date planCompleteDate = format.parse(planCompleteDateStr);
                long delayDay = (date.getTime() - planCompleteDate.getTime()) / (1000 * 3600 * 24);
                qualityProblem.setDelayDay(delayDay < 0 ? 0L : delayDay);
            }
        }
        return qualityProblemList;
    }

    @Override
    public Boolean create(QualityProblem param, String token) {

//        try {
            Map<String, Object> mapParam = BeanUtil.beanToMap(param,false,true);
            Map<String, Object> params = new HashMap<String, Object>(4) {
                {
                    put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                    put("map",mapParam);
                }
            };

            log.info("创建质量问题参数"+ JSON.toJSONString(params));

            JSONObject resp = Utils.doPostAndToken(JiraApiParams.ProductQualityProblem, token, params);

            log.info("创建质量问题返回结果"+JSON.toJSONString(resp));

            if(resp.getBoolean("result")){
                return true;
            }else{
                throw new ServiceException(500,resp.getString("message"));
            }
//        } catch (Exception e) {
//            log.error("创建质量问题异常",e);
//            throw e;
//        }
//        return false;
    }

    @Override
    public List<QualityProblem> listByJiraPermit(String token, QualityProblem param) throws ParseException {
//        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        JSONObject jiraResp = Utils.doGet(JiraApiParams.ProductQualityProblemListByPermit, token, null);
        if (!jiraResp.getBoolean("result")) {
            return new ArrayList<>();
        }
        List<Long> ids = JSONObject.parseObject(jiraResp.getString("value"),new TypeReference<List<Long>>(){});

        if (ObjectUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<QualityProblem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(QualityProblem::getIssueId, ids);
        if (ObjectUtil.isNotEmpty(param.getReporterId())) {//报告人
            queryWrapper.eq(QualityProblem::getReporterId, param.getReporterId().toLowerCase());
        }
        if (ObjectUtil.isNotEmpty(param.getStatusLamp())) {//状态灯
            queryWrapper.eq(QualityProblem::getStatusLamp, param.getStatusLamp());
        }
        if (ObjectUtil.isNotEmpty(param.getStatusLampList())) {//状态灯
            queryWrapper.in(QualityProblem::getStatusLamp, param.getStatusLampList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemStatus())) {//问题状态 0 close 1 open
            queryWrapper.eq(QualityProblem::getProblemStatus, param.getProblemStatus());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemStatusList())) {//问题状态 0 close 1 open
            queryWrapper.in(QualityProblem::getProblemStatus, param.getProblemStatusList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemLevel())) {//问题等级
            queryWrapper.eq(QualityProblem::getProblemLevel, param.getProblemLevel());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemLevelList())) {//问题等级
            queryWrapper.in(QualityProblem::getProblemLevel, param.getProblemLevelList());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemCate())) {//问题分类
            queryWrapper.eq(QualityProblem::getProblemCate, param.getProblemCate());
        }
        if (ObjectUtil.isNotEmpty(param.getProblemCateList())) {//问题分类
            queryWrapper.in(QualityProblem::getProblemCate, param.getProblemCateList());
        }
        if (ObjectUtil.isNotEmpty(param.getProductName())) {//产品名称
            queryWrapper.like(QualityProblem::getProductName, param.getProductName());
        }
        if (ObjectUtil.isNotEmpty(param.getProjectDepartmentList())) {//问题分类
            queryWrapper.in(QualityProblem::getProjectDepartment, param.getProjectDepartmentList());
        }
        if (ObjectUtil.isNotEmpty(param.getInstituteList())) {//研究院
            queryWrapper.in(QualityProblem::getInstitute, param.getInstituteList());
        }
        if (ObjectUtil.isNotEmpty(param.getResolution())) {//解决结果ID
            queryWrapper.in(QualityProblem::getResolution, param.getResolution());
        }
        if (ObjectUtil.isNotEmpty(param.getKeyWord())) {//产品名称、项目名称、提出人、责任人 模糊搜索
            queryWrapper.and(qw -> qw
                    .like(QualityProblem::getProductName, param.getKeyWord()).or()
                    .like(QualityProblem::getProjectName, param.getKeyWord()).or()
                    .like(QualityProblem::getPresenterName, param.getKeyWord()).or()
                    .like(QualityProblem::getResponsiblePersonName, param.getKeyWord()));
        }
        queryWrapper.orderByAsc(QualityProblem::getProblemLevel).orderByDesc(QualityProblem::getFindDate);
        List<QualityProblem> qualityProblemList = this.list(queryWrapper);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        for (QualityProblem qualityProblem : qualityProblemList) {
            if (qualityProblem.getActualCompleteDate() != null) {
                continue;
            }
            String planCompleteDateStr = qualityProblem.getPlanCompleteDate();
            if (planCompleteDateStr == null) {
                qualityProblem.setDelayDay(0L);
            } else {
                Date date = new Date();
                Date planCompleteDate = format.parse(planCompleteDateStr);
                long delayDay = (date.getTime() - planCompleteDate.getTime()) / (1000 * 3600 * 24);
                qualityProblem.setDelayDay(delayDay < 0 ? 0L : delayDay);
            }
        }
        return qualityProblemList;
    }
}
