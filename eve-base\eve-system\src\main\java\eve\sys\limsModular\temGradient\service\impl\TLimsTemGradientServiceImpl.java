package eve.sys.limsModular.temGradient.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import eve.sys.limsModular.ordTaskCondition.entity.TLimsOrdtaskCondition;
import eve.sys.limsModular.ordTaskCondition.service.ITLimsOrdtaskConditionService;
import eve.sys.limsModular.order.entity.TLimsOrder;
import eve.sys.limsModular.order.service.ITLimsOrderService;
import eve.sys.limsModular.temGradient.entity.TLimsTemGradient;
import eve.sys.limsModular.temGradient.mapper.TLimsTemGradientMapper;
import eve.sys.limsModular.temGradient.service.ITLimsTemGradientService;
import eve.sys.limsModular.testMatrix.entity.TLimsTestmatrix;
import eve.sys.limsModular.testMatrix.service.ITLimsTestmatrixService;
import eve.sys.modular.test.testProgress.entity.TestProgress;
import eve.sys.modular.test.testProgress.service.ITestProgressService;
import eve.sys.modular.test.testProgressDetail.entity.TestProgressDetail;
import eve.sys.modular.test.testProgressDetail.service.ITestProgressDetailService;
import eve.sys.modular.test.testProgressSize.entity.TestProgressSize;
import eve.sys.modular.test.testProgressSize.service.ITestProgressSizeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 温度梯度 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Service
public class TLimsTemGradientServiceImpl extends ServiceImpl<TLimsTemGradientMapper, TLimsTemGradient> implements ITLimsTemGradientService {

    @Resource
    private ITLimsFolderService folderService;

    @Resource
    private ITLimsOrdtaskConditionService conditionService;

    @Resource
    private ITLimsTestmatrixService testmatrixService;

    @Resource
    private ITLimsOrderService orderService;

    @Resource
    private ITLimsOrdtaskService ordtaskService;

    @Resource
    private ITestProgressService progressService;

    @Resource
    private ITestProgressDetailService detailService;

    @Resource
    private ITestProgressSizeService testProgressSizeService;

    /**
     * 同步中检测试项目lims到pbi
     */
    @Override
    public void sync() {
        LambdaQueryWrapper<TLimsTemGradient> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(TLimsTemGradient::getDatatype, "inspectionMsg");
        //查询未同步的

        queryWrapper.and(wrapper -> {
            wrapper.eq(TLimsTemGradient::getIssync, "0").or().isNull(TLimsTemGradient::getIssync);
        });
        List<TLimsTemGradient> list = this.list(queryWrapper);

        //分组
        Map<Long, List<TLimsTemGradient>> conditions = list.stream().collect(Collectors.groupingBy(TLimsTemGradient::getOrdtaskconditionid));

        for (Long ordtaskconditionid : conditions.keySet()) {

            List<TLimsTemGradient> tLimsTemGradients = conditions.get(ordtaskconditionid);
            //排序
            List<TLimsTemGradient> gradients = tLimsTemGradients.stream().sorted(Comparator.comparing(TLimsTemGradient::getOrderno)).collect(Collectors.toList());

            if (ObjectUtil.isNotEmpty(gradients)) {
                TLimsTemGradient tLimsTemGradient = gradients.get(0);
                TLimsOrdtaskCondition condition = conditionService.getById(tLimsTemGradient.getOrdtaskconditionid());

                if (null == condition) {
                    continue;
                }
                TLimsFolder folder = folderService.getById(condition.getFolderid());
                //非测试中跳过
                if (null == folder || !"Testing".equals(folder.getStatus())) {
                    continue;
                }

                //查询测试条件
                LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid, condition.getOrdtaskid());
                List<TLimsOrdtaskCondition> conditionList = this.conditionService.list(conditionLambdaQueryWrapper);

                //查询测试项目试验矩阵
                LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
                testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, condition.getOrdtaskid());
                List<TLimsTestmatrix> testmatrices = this.testmatrixService.list(testmatrixLambdaQueryWrapper);
                Integer matricesSize = testmatrices.size();

                TLimsOrdtask ordtask = ordtaskService.getById(condition.getOrdtaskid());

                String testAddress = null;
                if(Arrays.asList("高温存储","高温高湿存储").contains(ordtask.getTestname())){
                    //第四实验室
                    testAddress = "R3";
                }

                if(StrUtil.isNotBlank(folder.getTestarea()) && (folder.getTestarea().contains("61") || folder.getTestarea().contains("62") || folder.getTestarea().contains("63"))  ){
                    //第六实验室（JM）
                    testAddress = "JM";
                }

                String tem = "";
                String day = "";
                String soc = "";
                String humidity = null;
                for (int i = 0; i < conditionList.size(); i++) {
                    if (conditionList.get(i).getConditionname().equals("温度")) {
                        tem = conditionList.get(i).getConditionvalue();
                    }
                    if (conditionList.get(i).getConditionname().equals("储存时间")) {
                        day = conditionList.get(i).getConditionvalue();
                    }
                    if (conditionList.get(i).getConditionname().equals("储存SOC")) {
                        soc = conditionList.get(i).getConditionvalue();
                    }
                    if (conditionList.get(i).getConditionname().equals("湿度")) {
                        humidity = conditionList.get(i).getConditionvalue();
                    }
                }

                List<String> sampleCodes = new ArrayList<>();
                List<String> cellTestCodes = new ArrayList<>();
                List<TLimsOrder> orderList = new ArrayList<>();
                for (int i = 0; i < testmatrices.size(); i++) {
                    if ("1".equals(testmatrices.get(i).getCheckstatus())) {
                        TLimsOrder order = orderService.getById(testmatrices.get(i).getOrderid());
                        orderList.add(order);
                    }

                }
                orderList = orderList.stream().sorted(Comparator.comparing(TLimsOrder::getOrderno)).collect(Collectors.toList());
                for (TLimsOrder order : orderList) {
                    sampleCodes.add(order.getOrderno());
                    cellTestCodes.add(order.getCelltestcode());
                }

                /**
                 * <a-select-option value="A1_3F">
                 *                       V圆柱检测室
                 *                     </a-select-option>
                 *
                 *                     <a-select-option value="R2_2F">
                 *                       材料验证检测室
                 *                     </a-select-option>
                 *
                 *                     <a-select-option value="R4_4F">
                 *                       动力电池检测室
                 *                     </a-select-option>
                 */
                // 判断是否是阶段式日历寿命测试
                boolean stageFlag = conditionList.stream()
                        .anyMatch(o -> "每次出箱电池数".equals(o.getConditionname()));

                //组装中检数据
                TestProgress addParam = TestProgress.builder()
                        .testCode(folder.getFolderno())
                        .productName(folder.getProducttype())//产品类型
                        .sampleType(folder.getCylindercellflag())
                        .productSampleStage(folder.getTechnicalstatus())
                        .testType(folder.getTesttype())
                        .testAlias(ordtask.getAlias())
                        .applicant(folder.getCreatedbyname())
                        .applicantAccount(folder.getCreatedbyid())
                        .dept(folder.getCreatedbyorgname())
                        .testAddress(StrUtil.isNotBlank(testAddress)?testAddress:(StrUtil.isBlank(folder.getTestarea()) ? folder.getTestarea() :
                                folder.getTestarea().contains("R4-动力") ? "R4_4F" :
                                        folder.getTestarea().contains("R2-材料") ? "R2_2F" :
                                                folder.getTestarea().contains("R4-V圆柱") ? "A1_3F" : folder.getTestarea()))
                        .source("lims")
                        .testProject((StrUtil.isNotBlank(tem) ? tem + "℃&" : "") + (StrUtil.isNotBlank(humidity) ? humidity + "%RH&" : "") + (StrUtil.isNotBlank(soc) ? soc + "%&" : "") + (StrUtil.isNotBlank(day) ? day + "天" : ""))
                        .t(tem)
                        .soc(soc)
                        .testPeriod(StrUtil.isNotBlank(day) ? Long.valueOf(day) : null)
                        .quantity(Long.valueOf(sampleCodes.size()))
                        .sampleCodes(JSON.toJSONString(sampleCodes))
                        .cellTestCodes(JSON.toJSONString(cellTestCodes))
                        .testStatus("Plan")
                        .ordtaskid(condition.getOrdtaskid())
                        .testPurpose(folder.getTestpurpose())
                        .stageFlag(stageFlag ? 1 : 0)
                        //湿度
                        .humidity(stringToBigDecimal(humidity))
                        .build();

                LambdaQueryWrapper<TestProgress> testProgressWrapper = new LambdaQueryWrapper<>();
                testProgressWrapper.eq(TestProgress::getOrdtaskid, condition.getOrdtaskid());
                testProgressWrapper.eq(TestProgress::getStatus, 0);
                testProgressWrapper.eq(TestProgress::getDeleteStatus, 0);
                List<TestProgress> existList = this.progressService.list(testProgressWrapper);
                boolean saveFlag = true;
                if (existList.size() > 0) {
                    saveFlag = false;
                }

                if (saveFlag) {
                    this.progressService.save(addParam);
                }

                List<TestProgressDetail> progressDetails = new ArrayList<>();
                for (int i = 0; i < gradients.size(); i++) {
                    TestProgressDetail detail = TestProgressDetail.builder()
                            .day(gradients.get(i).getStoragedays())
                            .totalDay(gradients.get(i).getTotalstoragedays())
                            .orderNumber(gradients.get(i).getOrderno().intValue())
                            .progressId(addParam.getId())
                            .innerres(gradients.get(i).getInnerres())
                            .voltage(gradients.get(i).getVoltage())
                            .height(gradients.get(i).getHeight())
                            .volume(gradients.get(i).getVolume())
                            .cellheight(gradients.get(i).getCellheight())
                            .weight(gradients.get(i).getWeight())
                            .isolateres(gradients.get(i).getIsolateres())
                            .smallinspection(gradients.get(i).getSmallinspection())
                            .largeinspection(gradients.get(i).getLargeinspection())
                            .recharge(gradients.get(i).getRecharge())
                            .picture(gradients.get(i).getPicture())
                            .video(gradients.get(i).getVideo())
                            .build();

                    progressDetails.add(detail);
                }

                if (ObjectUtil.isNotEmpty(progressDetails)) {
                    if (saveFlag) {
                        this.detailService.saveBatch(progressDetails);
                    }
                }

                //修改同步状态
                //List<TLimsTemGradient> updateList = new ArrayList<>();
                gradients.forEach(g -> {
                    //updateList.add(TLimsTemGradient.builder().id(g.getId()).issync("1").build());
                    this.updateById(TLimsTemGradient.builder().id(g.getId()).issync("1").build());
                });
                //this.updateBatchById(updateList);


            }

        }


    }

    public BigDecimal stringToBigDecimal(String input){
        try {
            if(StrUtil.isNotBlank(input)){
                return new BigDecimal(input);
            }else{
                return null;
            }
        }catch (Exception e){
            log.error(input+ "不是数字",e);
            return null;
        }
    }

    /**
     * 同步中检测试项目的尺寸信息从lims到pbi
     */
    @Override
    public void syncSizeInfo() {
        LambdaQueryWrapper<TLimsTemGradient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsTemGradient::getDatatype, "sizeInformation");
        //查询未同步的
        queryWrapper.and(wrapper -> {
            wrapper.eq(TLimsTemGradient::getIssync, "0").or().isNull(TLimsTemGradient::getIssync);
        });
        List<TLimsTemGradient> list = this.list(queryWrapper);
        //分组
        Map<Long, List<TLimsTemGradient>> conditions = list.stream().collect(Collectors.groupingBy(TLimsTemGradient::getOrdtaskconditionid));
        for (Long ordtaskconditionid : conditions.keySet()) {
            List<TLimsTemGradient> tLimsTemGradients = conditions.get(ordtaskconditionid);
            //排序
            List<TLimsTemGradient> gradients = tLimsTemGradients.stream().sorted(Comparator.comparing(TLimsTemGradient::getOrderno)).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(gradients)) {
                TLimsTemGradient tLimsTemGradient = gradients.get(0);
                TLimsOrdtaskCondition condition = conditionService.getById(tLimsTemGradient.getOrdtaskconditionid());
                if (null == condition) {
                    continue;
                }
                TLimsFolder folder = folderService.getById(condition.getFolderid());
                //非测试中跳过
                if (null == folder || !"Testing".equals(folder.getStatus())) {
                    continue;
                }
                // 查询是否已经存在计划主表，否的话则不进行同步
                LambdaQueryWrapper<TestProgress> testProgressWrapper = new LambdaQueryWrapper<>();
                testProgressWrapper.eq(TestProgress::getOrdtaskid, condition.getOrdtaskid());
                testProgressWrapper.eq(TestProgress::getStatus, 0);
                testProgressWrapper.eq(TestProgress::getDeleteStatus, 0);
                List<TestProgress> existList = this.progressService.list(testProgressWrapper);
                if (existList.size() != 1) {
                    continue;
                }
                TestProgress testProgress = existList.get(0);
                // 查询是否已经存在已同步的尺寸信息，是的话则不进行同步
                LambdaQueryWrapper<TestProgressSize> testSizeWrapper = new LambdaQueryWrapper<>();
                testSizeWrapper.eq(TestProgressSize::getProgressId, testProgress.getId());
                testSizeWrapper.eq(TestProgressSize::getDeleteStatus, 0);
                List<TestProgressSize> existSizeList = this.testProgressSizeService.list(testSizeWrapper);
                if (existSizeList.size() > 0) {
                    continue;
                }
                List<TestProgressSize> testProgressSizeList = new ArrayList<>();
                for (int i = 0; i < gradients.size(); i++) {
                    TestProgressSize size = TestProgressSize.builder()
                            .orderNo(gradients.get(i).getOrderno())
                            .sizeType(gradients.get(i).getSizetype())
                            .measureTime(gradients.get(i).getMeasuretime())
                            .calRule(gradients.get(i).getCalrule())
                            .progressId(testProgress.getId())
                            .build();
                    testProgressSizeList.add(size);
                }
                if (ObjectUtil.isNotEmpty(testProgressSizeList)) {
                    this.testProgressSizeService.saveBatch(testProgressSizeList);
                }
                //修改同步状态
                gradients.forEach(g -> {
                    this.updateById(TLimsTemGradient.builder().id(g.getId()).issync("1").build());
                });
            }
        }
    }

    @Override
    public List<TLimsTemGradient> getByOrderIdAndOrdtaskId(Long folderId, Long ordtaskId) {
        LambdaQueryWrapper<TLimsOrdtaskCondition> conditionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        conditionLambdaQueryWrapper.select(TLimsOrdtaskCondition::getId);
        conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getFolderid, folderId);
        conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getOrdtaskid, ordtaskId);
        conditionLambdaQueryWrapper.eq(TLimsOrdtaskCondition::getConditionname, "中检信息");
        conditionLambdaQueryWrapper.last(" FETCH FIRST ROW ONLY");
        TLimsOrdtaskCondition ordtaskCondition = conditionService.getOne(conditionLambdaQueryWrapper);

        if (ordtaskCondition != null) {
            LambdaQueryWrapper<TLimsTemGradient> temGradientLambdaQueryWrapper = new LambdaQueryWrapper<>();
            temGradientLambdaQueryWrapper.select(TLimsTemGradient::getTotalstoragedays, TLimsTemGradient::getSmallinspection, TLimsTemGradient::getLargeinspection, TLimsTemGradient::getRecharge);
            temGradientLambdaQueryWrapper.eq(TLimsTemGradient::getOrdtaskconditionid, ordtaskCondition.getId());
            temGradientLambdaQueryWrapper.orderByAsc(TLimsTemGradient::getTotalstoragedays);
            List<TLimsTemGradient> temGradients = list(temGradientLambdaQueryWrapper);
            return temGradients;
        }

        return new ArrayList<>();
    }

}
