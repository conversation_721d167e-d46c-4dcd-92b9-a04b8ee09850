package eve.sys.modular.bom.controller;


import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.exception.ServiceException;
import eve.core.pojo.response.ErrorResponseData;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.baseParam.IdParam;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.BomCopyParam;
import eve.sys.modular.bom.params.BomParam;
import eve.sys.modular.bom.params.SysBomParam;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bom.service.impl.BomService;
import eve.sys.modular.bomerror.entity.SysBomError;
import eve.sys.modular.bomerror.service.ISysBomErrorService;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.bomline.param.SysBomLineParam;
import eve.sys.modular.bomline.service.ISysBomLineService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
@RestController
public class SysBomController {
    @Resource
    private ISysBomService sysBomService;

    @Resource
    private ISysBomLineService bomLineService;

    @Resource
    private ISysBomErrorService bomErrorService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Resource
    private BomService bomService;

    @BusinessLog(title = "BOM物料清单查看", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/material")
    public ResponseData getBomParts(SysBom param) {
        return new SuccessResponseData(bomService.getBomParts(param));
    }

    @BusinessLog(title = "BOM管理员审核", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    @GetMapping("/sysBom/confirm")
    public ResponseData adminConfirm(SysBom param)  {
        sysBomService.adminConfirm(param);
        return new SuccessResponseData();
    }


    //bom搭建
    @BusinessLog(title = "BOM搭建", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBom/add")
    public ResponseData add(@RequestBody SysBomParam sysBomParam) {
        return new SuccessResponseData(sysBomService.add(sysBomParam));
    }

    @BusinessLog(title = "BOM创建-技术文档临时解决方案", opType = LogAnnotionOpTypeEnum.ADD)
    @PostMapping("/sysBom/addBom")
    public ResponseData addBom(@RequestBody SysBomParam sysBomParam) {
        return new SuccessResponseData(sysBomService.addBom(sysBomParam));
    }

    @BusinessLog(title = "BOM删除", opType = LogAnnotionOpTypeEnum.DELETE)
    @PostMapping("/sysBom/del")
    public ResponseData del(@RequestBody SysBomParam sysBomParam) {
        sysBomService.delBom(sysBomParam);
        return new SuccessResponseData();
    }

    //bom保存
    @PostMapping("/sysBom/save")
    public ResponseData save(@RequestBody SysBomParam sysBomParam) {
        return new SuccessResponseData(sysBomService.edit(sysBomParam));
    }

    @BusinessLog(title = "BOM撤回", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBom/widthDraw")
    public ResponseData widthDraw(@RequestBody SysBomParam sysBomParam) {
        sysBomService.withDraw(sysBomParam);
        return new SuccessResponseData();
    }

    //根据id获取单独的bom
    @BusinessLog(title = "获取BOM", opType = LogAnnotionOpTypeEnum.DETAIL)
    @GetMapping("/sysBom/get")
    public ResponseData get(SysBomParam sysBomParam) {
        return new SuccessResponseData(sysBomService.get(sysBomParam.getId()));
    }

    //获取bom列表
    @BusinessLog(title = "获取BOM列表", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/list")
    public ResponseData getList(BomParam param) {
        return new SuccessResponseData(sysBomService.getLists(param));
    }

    //获取bom错误
    @BusinessLog(title = "获取BOM错误", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/error")
    public ResponseData getBomError(SysBomParam param) {
        SysBomError sysBomError = bomErrorService.getByBomId(param.getId());
        return new SuccessResponseData(sysBomError);
    }

    //获取工厂
    @BusinessLog(title = "获取工厂列表", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/getwerks")
    public ResponseData getWerks() {
        return new SuccessResponseData(sysBomService.getWerks());
    }
    //获取工厂
    @BusinessLog(title = "获取bu列表", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/getbuConfig")
    public ResponseData getbuConfig() {
        return new SuccessResponseData(sysBomService.getBuConfig());
    }

    //获取所有的产线
    @BusinessLog(title = "获取产线列表", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/getWerkLines")
    public ResponseData getWerkLines() {
        return new SuccessResponseData(sysBomService.getWerkLines());
    }

    //获取bom所有关联的产线
    @BusinessLog(title = "获取BOM关联的产线列表", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/bomlines")
    public ResponseData getList(SysBomLineParam param) {
        return new SuccessResponseData(bomLineService.getFilterList(param));
    }

    //添加bom与产线关联
    /* @PostMapping("/sysBom/addlines")
    public ResponseData addLines(@RequestBody SysBomLineParam param) {
        return new SuccessResponseData(bomLineService.insertBatch(param) > 0);
    } */

    //bom版本升级
    @BusinessLog(title = "BOM版本升级", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBom/upgrade")
    public ResponseData upgrade(@RequestBody SysBomParam param) {

        sysBomService.upgrade(param);
        return new SuccessResponseData();
    }

    @BusinessLog(title = "BOM PDF文件更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBom/pdfUpdate")
    public ResponseData pdfUpdate(@RequestBody SysBomParam param) throws IOException {

        return new SuccessResponseData(sysBomService.pdfUpdate(param.getId(),false).get("fileId"));
    }

    @BusinessLog(title = "BOM 编码更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    @PostMapping("/sysBom/pdfUpdateAndCode")
    public ResponseData pdfUpdateAndCode(@RequestBody SysBomParam param) throws IOException {

        return new SuccessResponseData(sysBomService.pdfUpdateAndCode(param.getId(),true,param.getRemark(), null != param.getAlterType() ? param.getAlterType()+"" : null, null));
    }

    /* @PostMapping("/sysBom/update")
    public ResponseData update(@RequestBody SysBomParam param) throws IOException {
        sysBomService.updateBom(param);
        return new SuccessResponseData();
    } */

    @BusinessLog(title = "BOM提交", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    @PostMapping("/sysBom/commit")
    public ResponseData commit(@RequestBody SysBomParam param) throws IOException {
        sysBomService.commit(param);
        /* if (param.getProofs().size() > 0) {
            return new ErrorResponseData(501,"BOM扩充校验失败",param.getProofs());
        } */
        return new SuccessResponseData();
    }

    @BusinessLog(title = "BOM工厂变更OA提交", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    @PostMapping("/sysBom/werkALter2Oa")
    public ResponseData werkALter2Oa(@RequestBody SysBomParam param) throws IOException {
        sysBomService.werkALter2Oa(param);
        /* if (param.getProofs().size() > 0) {
            return new ErrorResponseData(501,"BOM扩充校验失败",param.getProofs());
        } */
        return new SuccessResponseData();
    }

    @BusinessLog(title = "BOM历史记录", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/history")
    public ResponseData getBomHistory(SysBomParam param) {
        return new SuccessResponseData(bomHistoryService.page(param.getId()));
    }

    @BusinessLog(title = "BOM历史记录B", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/historyB")
    public ResponseData getBomHistoryB(SysBomParam param) {
        return new SuccessResponseData(bomHistoryService.pageB(param));
    }

    @BusinessLog(title = "BOM最新版本", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/lasthistory")
    public ResponseData getBomLastHistory(SysBomParam param) {
        return new SuccessResponseData(sysBomService.getLastHistory(param.getId()));
    }
    @BusinessLog(title = "BOM复制", opType = LogAnnotionOpTypeEnum.IMPORT)
    @PostMapping("/sysBom/copy")
    public ResponseData copy(@RequestBody BomCopyParam param) {
        return new SuccessResponseData(sysBomService.copy(param));
    }
    @BusinessLog(title = "BOM sap提交重试", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    @PostMapping("/sysBom/retry2sap")
    public ResponseData copy(@RequestBody IdParam param) {
        sysBomService.bom2Sap(sysBomService.get(param.getId()),null);
        return new SuccessResponseData();
    }
    @BusinessLog(title = "BOM分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @PostMapping("/sysBom/page")
    public ResponseData page(@RequestBody SysBomParam param) {
        return new SuccessResponseData(sysBomService.page(param));
    }

    @BusinessLog(title = "成品BOM预览", opType = LogAnnotionOpTypeEnum.QUERY)
    @PostMapping("/sysBom/endBomPreview")
    public ResponseData endBomPreview(@RequestBody IdParam param) throws IOException {
        return new SuccessResponseData(sysBomService.endBomPdfUpdate(param.getId()));
    }

    @BusinessLog(title = "BOM sap导入验证", opType = LogAnnotionOpTypeEnum.OTHER)
    @PostMapping("/sysBom/sapImportVerify")
    public ResponseData sapImportVerify(@RequestBody SysBomParam param) throws ServiceException {

        sysBomService.sapImportVerify(param);
        return new SuccessResponseData();
    }

    @BusinessLog(title = "获取工厂选项", opType = LogAnnotionOpTypeEnum.QUERY)
    @GetMapping("/sysBom/getWerksOptions")
    public ResponseData getWerksOptions() {
        return new SuccessResponseData(sysBomService.getWerksOptions());
    }

    @BusinessLog(title = "BOM sap导入", opType = LogAnnotionOpTypeEnum.IMPORT)
    @PostMapping("/sysBom/sapImport")
    public ResponseData sapImport(@RequestBody SysBomParam param) {
        sysBomService.sapImport(param);
        return new SuccessResponseData();
    }

    @BusinessLog(title = "BOM sap校验", opType = LogAnnotionOpTypeEnum.OTHER)
    @GetMapping("/sysBom/sapVerify")
    public ResponseData sapVerify(SysBomParam param) throws ParseException {
        return new SuccessResponseData(sysBomService.sapVerify(param));
    }

    @GetMapping("/sysBom/updateBomData")
    public ResponseData updateBomData(SysBom param)  {
        sysBomService.updateBomData(param.getId());
        return new SuccessResponseData();
    }


    @GetMapping("/sysBom/initBaseBom")
    public ResponseData initBaseBom(SysBomParam param) {
        return new SuccessResponseData(sysBomService.initBaseBom(param));
    }

}

