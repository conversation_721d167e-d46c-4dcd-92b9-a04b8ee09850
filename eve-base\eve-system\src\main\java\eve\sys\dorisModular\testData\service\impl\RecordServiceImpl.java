package eve.sys.dorisModular.testData.service.impl;

import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.dorisModular.testData.entity.Record;
import eve.sys.dorisModular.testData.mapper.RecordMapper;
import eve.sys.dorisModular.testData.service.IRecordService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.mongoDbModular.shenghong.bean.HisMainData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@DS("b4")
public class RecordServiceImpl extends ServiceImpl<RecordMapper, Record> implements IRecordService {

    @Resource
    private ITLimsTestdataPrimaryService testdataPrimaryService;

    @Override
    public PageResult<Record> dataListPage(HisMainData param)  {
        TLimsTestdataPrimary testdataPrimary = testdataPrimaryService.getById(param.get_id());
        Map<String, Object> map = JSON.parseObject(testdataPrimary.getQueryparam(), Map.class);
        List<String> values = new ArrayList<>();

        for (String key : map.keySet()) {

            values.add(map.get(key).toString());
        }
        String flowId = String.join("-", values);
        LambdaQueryWrapper<Record> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Record::getFlowId, flowId);
        if (null != param.getStepId()) {
            queryWrapper.eq(Record::getStepId, param.getStepId());
        }
        if (null != param.getStepIdBefore()) {
            queryWrapper.ge(Record::getStepId, param.getStepIdBefore());
        }
        if (null != param.getStepIdAfter()) {
            queryWrapper.le(Record::getStepId, param.getStepIdAfter());
        }

        if (null != param.getCycleId()) {
            queryWrapper.eq(Record::getCycleId, param.getCycleId());
        }
        if (StrUtil.isNotBlank(param.getStepName())) {
            queryWrapper.eq(Record::getStepName, param.getStepName());
        }


        //翻页处理
        if (param.getPageNo() > 1) {
            Integer count = this.count(queryWrapper);
//            Integer recordId = this.baseMapper.selectRecordId(queryWrapper, (param.getPageNo() - 1) * param.getPageSize() - 1);
//            //大于上一页的最大记录号
//            queryWrapper.gt(Record::getRecordId, recordId);
//            queryWrapper.orderByAsc(Record::getRecordId);
//            Page<Record> recordPage = this.page(new Page<>(1, param.getPageSize()), queryWrapper);
//            List<Record> records = recordPage.getRecords();
            List<Record> records = this.baseMapper.selectRecordPage(queryWrapper, (param.getPageNo() - 1) * param.getPageSize(), param.getPageSize());
            PageResult pageResult = new PageResult();
            pageResult.setPageNo(param.getPageNo());
            pageResult.setPageSize(param.getPageSize());
            pageResult.setRows(records);
            pageResult.setTotalRows(count);
            pageResult.setTotalPage(PageUtil.totalPage(count,
                    param.getPageSize()));
            return pageResult;
        }
        queryWrapper.orderByAsc(Record::getRecordId);
        return new PageResult(this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper));

    }
}

