package eve.sys.dorisModular.testData.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sync_data.record")
public class Record implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 记录id
     */
    private Long recordId;

    /**
     * 工步序号
     */
    private Long stepNum;

    /**
     * 工步号
     */
    private Long stepId;

    /**
     * 循环号
     */
    private Long cycleId;

    /**
     * 数据关联id
     */
    private String flowId;

    /**
     * 工步名称
     */
    private String stepName;

    /**
     * 数据产生时间
     */
    private String absoluteTime;

    /**
     * 记录时间
     */
    private String recordTime;

    /**
     * 工步时间
     */
    private String stepTime;

    /**
     * 电压
     */
    private BigDecimal voltage;

    /**
     * 电流 和关键字冲突 current 改为 current1
     */
    private BigDecimal current1;

    /**
     * 容量
     */
    private BigDecimal capacity;

    /**
     * 能量
     */
    private BigDecimal energy;

    /**
     * 功率
     */
    private BigDecimal power;

    /**
     * 累计充电容量
     */
    private BigDecimal accumulateChargeCapacity;

    /**
     * 累计放电容量
     */
    private BigDecimal accumulateDischargeCapacity;

    /**
     * 累计充电能量
     */
    private BigDecimal accumulateChargeEnergy;

    /**
     * 累计放电能量
     */
    private BigDecimal accumulateDischargeEnergy;

    /**
     * 总容量
     */
    private BigDecimal totalCapacity;

    /**
     * 温度1
     */
    private BigDecimal auxTem1;

    /**
     * 温度2
     */
    private BigDecimal auxTem2;

    /**
     * 温度3
     */
    private BigDecimal auxTem3;

    /**
     * 温度4
     */
    private BigDecimal auxTem4;

    /**
     * 设备编号
     */
    private String unitNum;

    /**
     * 通道编号
     */
    private String channelId;

    /**
     * 测试条码
     */
    private String barcode;

    /**
     * 同步doris时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncDate;

    /**
     * 数据上传时间
     */
    private String createTime;

    /**
     * SOC
     */
    private BigDecimal socCalculate;


}
