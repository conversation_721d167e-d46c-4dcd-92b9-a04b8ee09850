package eve.sys.modular.bookcorner.service;

import eve.core.pojo.page.PageResult;
import eve.sys.modular.bookcorner.entity.BookCorner;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 图书角 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IBookCornerService extends IService<BookCorner> {
    List<BookCorner> getList(BookCorner param);

    PageResult<BookCorner> page(BookCorner param);

    BookCorner getById(BookCorner param);

    void add(BookCorner param);

    void edit(BookCorner param);

    void delete(BookCorner param);
}
